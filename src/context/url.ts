// Configuration de l'URL de base pour l'API
export const BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

// URL de base pour les images/assets
export const ASSETS_URL = process.env.REACT_APP_ASSETS_URL || 'http://localhost:8000';

// Alias pour la compatibilité avec le code existant
export const IMG_URL = ASSETS_URL;

// Configuration des endpoints
export const API_ENDPOINTS = {
  // Auth
  LOGIN: '/login',
  REGISTER: '/register',
  LOGOUT: '/logout',
  PROFILE: '/profile',
  CHANGE_PASSWORD: '/change-password',
  FORGOT_PASSWORD: '/forgot-password',
  RESET_PASSWORD: '/reset-password',
  VERIFY_EMAIL: '/verify-email',
  
  // Salons
  SALONS: '/salons',
  SALON_DETAIL: (id: number) => `/salons/${id}`,
  
  // Services
  SERVICES: '/services',
  SALON_SERVICES: (salonId: number) => `/salons/${salonId}/services`,
  
  // Staff/Hairdressers
  HAIRDRESSERS: '/hairdressers',
  SALON_HAIRDRESSERS: (salonId: number) => `/salons/${salonId}/hairdressers`,
  
  // Bookings
  BOOKINGS: '/bookings',
  USER_BOOKINGS: '/user/bookings',
  SALON_BOOKINGS: (salonId: number) => `/salons/${salonId}/bookings`,
  
  // Time Slots
  TIME_SLOTS: '/time-slots',
  AVAILABLE_SLOTS: '/available-slots',
  
  // Admin
  ADMIN: {
    USERS: '/admin/users',
    SALONS: '/admin/salons',
    BOOKINGS: '/admin/bookings',
    SERVICES: '/admin/services',
    STAFF: '/admin/staff',
  },
  
  // Manager
  MANAGER: {
    SALON: '/manager/salon',
    STAFF: '/manager/staff',
    BOOKINGS: '/manager/bookings',
    SERVICES: '/manager/services',
  },
  
  // Owner
  OWNER: {
    SALONS: '/owner/salons',
    STAFF: '/owner/staff',
    BOOKINGS: '/owner/bookings',
    SERVICES: '/owner/services',
    USERS: '/owner/users',
  }
};

export default BASE_URL;
