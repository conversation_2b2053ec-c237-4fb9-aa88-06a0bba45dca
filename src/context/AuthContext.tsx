import React, { createContext, useContext, useState, useEffect } from "react";
import { BASE_URL } from "./url";
import type { User, ApiResponse } from "../components/auth/types";

// Dans AuthContext.tsx
interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  resetToken: string | null;
  setResetToken: (token: string | null) => void;
  clearResetToken: () => void;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: Partial<User> & { password: string }) => Promise<void>;
  logout: () => Promise<void>;
  setUser: React.Dispatch<React.SetStateAction<User | null>>;
  updateUser: (userData: User) => void;
}

interface ApiError extends Error {
  isApiError: true;
  status: number;
  data: {
    message?: string;
    errors?: Record<string, string[]>;
  };
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [resetToken, setResetToken] = useState<string | null>(null);

  const clearResetToken = () => setResetToken(null);

  useEffect(() => {
    const checkAuth = async () => {
      const token = localStorage.getItem("auth_token");
      if (!token || token.trim() === "") {
        setIsLoading(false);
        return;
      }

      try {
        const response = await fetch(`${BASE_URL}/user`, {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        });

        if (response.ok) {
          const responseData: ApiResponse = await response.json();
          setUser(responseData.data);
        } else if (response.status === 401) {
          localStorage.removeItem("auth_token");
        }
      } catch (error) {
        console.error("Auth check error:", error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  const handleAuthResponse = async (response: Response) => {
    if (!response.ok) {
      const errorData = await response.json();
      const error = new Error(
        errorData.message || "Request failed"
      ) as ApiError;
      error.isApiError = true;
      error.status = response.status;
      error.data = errorData;
      throw error;
    }
    return response.json();
  };

  const login = async (email: string, password: string) => {
    try {
      const response = await fetch(`${BASE_URL}/login`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password }),
      });

      const data = await handleAuthResponse(response);
      localStorage.setItem("auth_token", data.access_token);
      setUser(data.user);
    } catch (error) {
      console.error("Login error:", error);
      throw error;
    }
  };

  const register = async (userData: Partial<User> & { password: string }) => {
    try {
      const response = await fetch(`${BASE_URL}/register`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify(userData),
      });

      const responseData = await response.json();

      if (!response.ok) {
        const error = new Error(responseData.message || "Registration failed");
        Object.assign(error, {
          isApiError: true,
          status: response.status,
          data: responseData,
        });
        throw error;
      }

      localStorage.setItem("auth_token", responseData.access_token);
      setUser(responseData.user);
      return responseData;
    } catch (error) {
      console.error("Registration error:", error);
      throw error;
    }
  };

  const logout = async () => {
    const token = localStorage.getItem("auth_token");
    if (!token) return;

    try {
      await fetch(`${BASE_URL}/logout`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      localStorage.removeItem("auth_token");
      setUser(null);
    }
  };

  const updateUser = (userData: User) => {
    setUser(userData);
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        resetToken,
        setResetToken,
        clearResetToken,
        login,
        register,
        logout,
        setUser,
        updateUser,
      }}
    >
      {!isLoading && children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
