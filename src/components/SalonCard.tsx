import React from 'react';
import { Link } from 'react-router-dom';
import { Star, MapPin, Clock } from 'lucide-react';
import type { Salon } from '../components/auth/types';
import { IMG_URL } from '../../src/context/url';


interface SalonCardProps {
  salon: Salon;
}

export default function SalonCard({ salon }: SalonCardProps) {
  // Sélectionne la première image si disponible, sinon affiche une image par défaut
  const imageUrl = salon.images.length > 0 ? salon.images[0] : `https://source.unsplash.com/800x600/?salon,hairdresser`;

  return (
    <Link to={`/salons/${salon.id}`} className="block">
      <div className="bg-white overflow-hidden shadow rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
        {/* Image avec hauteur fixe comme dans le backoffice */}
        <div className="relative h-48">
          <img
            src={`${IMG_URL}/storage/${imageUrl}`} 
            alt={salon.name}
            className="w-full h-full object-cover"
            onError={(e) => (e.currentTarget.src = 'https://dummyimage.com/640x480/ccc/000.png&text=Image+Not+Found')}
          />
        </div>

        {/* Contenu avec padding comme dans le backoffice */}
        <div className="p-6">
          {/* Nom du salon et note */}
          <div className="flex justify-between items-start mb-2">
            <h3 className="text-lg font-medium text-gray-900">{salon.name}</h3>
            <div className="flex items-center">
              <Star className="h-4 w-4 text-yellow-400" fill="currentColor" />
              <span className="ml-1 text-sm text-gray-600">{salon.rating.toFixed(1)}</span>
            </div>
          </div>

          {/* Adresse */}
          <div className="text-sm text-gray-500 mb-2">
            <MapPin className="h-4 w-4 inline mr-1" />
            {salon.address}
          </div>

          {/* Horaires d'ouverture */}
          <div className="text-sm text-gray-500 mb-4">
            <div className="flex items-center mb-1">
              <Clock className="h-4 w-4 inline mr-1" />
              <span className="font-medium">Horaires :</span>
            </div>
            <div className="ml-5 space-y-1">
              {Object.entries(salon.hours).map(([day, hours]) => (
                <div key={day} className="flex justify-between">
                  <span className="capitalize">{day}:</span>
                  <span>
                    {hours.open === 'Fermé' || hours.open === 'closed' ? (
                      'Fermé'
                    ) : (
                      `${hours.open} - ${hours.close}`
                    )}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Bouton Réserver */}
          <div className="border-t border-gray-100 pt-4">
            <button className="w-full bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors text-sm font-medium">
              Réserver
            </button>
          </div>
        </div>
      </div>
    </Link>
  );
}
