import React from 'react';
import { format } from 'date-fns';
import { Calendar, DollarSign, User, Star } from 'lucide-react';

interface Activity {
  id: string;
  type: 'booking' | 'payment' | 'review' | 'client';
  description: string;
  timestamp: string;
}

const activities: Activity[] = [
  {
    id: '1',
    type: 'booking',
    description: 'Nouvelle réservation de <PERSON> pour une coupe femme',
    timestamp: '2024-03-15T10:30:00Z',
  },
  {
    id: '2',
    type: 'payment',
    description: 'Paiement reçu pour la réservation #12345',
    timestamp: '2024-03-15T10:00:00Z',
  },
  {
    id: '3',
    type: 'review',
    description: 'Nouvel avis 5 étoiles de <PERSON>',
    timestamp: '2024-03-15T09:45:00Z',
  },
  {
    id: '4',
    type: 'client',
    description: 'Nouvelle inscription client : <PERSON>',
    timestamp: '2024-03-15T09:30:00Z',
  },
];

const getActivityIcon = (type: Activity['type']) => {
  switch (type) {
    case 'booking':
      return Calendar;
    case 'payment':
      return DollarSign;
    case 'review':
      return Star;
    case 'client':
      return User;
    default:
      return Calendar;
  }
};

export default function ActivityFeed() {
  return (
    <div className="flow-root">
      <ul className="-mb-8">
        {activities.map((activity, activityIdx) => {
          const Icon = getActivityIcon(activity.type);
          return (
            <li key={activity.id}>
              <div className="relative pb-8">
                {activityIdx !== activities.length - 1 ? (
                  <span
                    className="absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200"
                    aria-hidden="true"
                  />
                ) : null}
                <div className="relative flex space-x-3">
                  <div>
                    <span className="h-8 w-8 rounded-full bg-indigo-50 flex items-center justify-center ring-8 ring-white">
                      <Icon className="h-5 w-5 text-indigo-600" />
                    </span>
                  </div>
                  <div className="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                    <div>
                      <p className="text-sm text-gray-500">{activity.description}</p>
                    </div>
                    <div className="whitespace-nowrap text-right text-sm text-gray-500">
                      {format(new Date(activity.timestamp), 'HH:mm')}
                    </div>
                  </div>
                </div>
              </div>
            </li>
          );
        })}
      </ul>
    </div>
  );
}