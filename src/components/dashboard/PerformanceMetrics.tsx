import React from 'react';
import { TrendingUp, Users, Clock, Star } from 'lucide-react';

const metrics = [
  {
    label: 'Fidélisation Client',
    value: '85%',
    change: '+2.3%',
    description: 'Pourcentage de clients fidèles',
    icon: Users,
  },
  {
    label: 'Temps Moyen',
    value: '45m',
    change: '-5m',
    description: 'Temps par client',
    icon: Clock,
  },
  {
    label: 'Satisfaction Client',
    value: '4.8',
    change: '+0.2',
    description: 'Note moyenne des avis',
    icon: Star,
  },
  {
    label: 'Revenu par Client',
    value: '75€',
    change: '+5€',
    description: 'Dépense moyenne par visite',
    icon: TrendingUp,
  },
];

export default function PerformanceMetrics() {
  return (
    <div className="grid grid-cols-1 gap-5 sm:grid-cols-2">
      {metrics.map((metric) => {
        const Icon = metric.icon;
        return (
          <div
            key={metric.label}
            className="relative overflow-hidden rounded-lg bg-white px-4 pt-5 pb-12 shadow sm:px-6 sm:pt-6"
          >
            <dt>
              <div className="absolute rounded-md bg-indigo-500 p-3">
                <Icon className="h-6 w-6 text-white" />
              </div>
              <p className="ml-16 truncate text-sm font-medium text-gray-500">
                {metric.label}
              </p>
            </dt>
            <dd className="ml-16 flex items-baseline pb-6 sm:pb-7">
              <p className="text-2xl font-semibold text-gray-900">{metric.value}</p>
              <p className="ml-2 flex items-baseline text-sm font-semibold text-green-600">
                {metric.change}
              </p>
              <div className="absolute inset-x-0 bottom-0 bg-gray-50 px-4 py-4 sm:px-6">
                <div className="text-sm">
                  <span className="font-medium text-gray-500">
                    {metric.description}
                  </span>
                </div>
              </div>
            </dd>
          </div>
        );
      })}
    </div>
  );
}