import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Users,
  Scissors,
  Calendar,
  Bar<PERSON>hart,
  Settings,
  LogOut,
  Building2,
  User,
} from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import type { UserRole } from '../../types/roles';
import { IMG_URL } from '../../context/url';

const MENU_ITEMS = [
  { label: 'Utilisateurs', icon: Users, path: '/dashboard/users', roles: ['admin', 'owner'] },
  { label: 'Salons', icon: Building2, path: '/dashboard/salons', roles: ['admin', 'owner'] },
  { label: 'Services', icon: Scissors, path: '/dashboard/services', roles: ['admin', 'owner', 'manager'] },
  { label: 'Rendez-vous', icon: Calendar, path: '/dashboard/bookings', roles: ['admin', 'owner', 'manager', 'hairdresser'] },
  { label: 'Rapports', icon: <PERSON><PERSON><PERSON>, path: '/dashboard/reports', roles: ['admin', 'owner', 'manager'] },
  { label: 'Personnel', icon: User, path: '/dashboard/staff', roles: ['admin', 'owner', 'manager'] },
  { label: 'Paramètres', icon: Settings, path: '/dashboard/settings', roles: ['admin', 'owner', 'manager', 'hairdresser', 'client'] },
];

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  if (!user) return null;

  const filteredMenuItems = MENU_ITEMS.filter((item) =>
    item.roles.includes(user.role as UserRole)
  ).map((item) => {
    // Modifier le libellé selon le rôle
    if (item.label === 'Utilisateurs' && user.role === 'owner') {
      return { ...item, label: 'Gérants' };
    }
    return item;
  });

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="flex">
        {/* Sidebar */}
        <div className="w-64 bg-white shadow-lg min-h-screen">
          {/* Logo */}
          <div
            className="p-4 cursor-pointer flex items-center space-x-2"
            onClick={() => navigate('/dashboard')}
            aria-label="Retour au tableau de bord"
          >
            <Scissors className="h-8 w-8 text-indigo-600" />
            <span className="text-xl font-bold">Best-Salon</span>
          </div>

          {/* Navigation */}
          <nav className="mt-8">
            <div className="px-4 space-y-1">
              {filteredMenuItems.map((item) => {
                const Icon = item.icon;
                const isActive = location.pathname === item.path;

                return (
                  <button
                    key={item.path}
                    onClick={() => navigate(item.path)}
                    className={`
                      flex items-center w-full px-4 py-2 text-sm rounded-lg
                      ${isActive
                        ? 'bg-indigo-50 text-indigo-600'
                        : 'text-gray-600 hover:bg-gray-50'
                      }
                    `}
                    aria-label={`Aller à ${item.label}`}
                  >
                    <Icon className="h-5 w-5 mr-3" />
                    {item.label}
                  </button>
                );
              })}
            </div>
          </nav>

          {/* Déconnexion */}
          <div className="absolute bottom-0 w-64 p-4 border-t">
            <button
              onClick={() => logout()}
              className="flex items-center w-full px-4 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg"
              aria-label="Déconnexion"
            >
              <LogOut className="h-5 w-5 mr-3" />
              Déconnexion
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1">
          <header className="bg-white shadow">
            <div className="px-4 py-6">
              <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold text-gray-900">Tableau de bord</h1>
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-500">
                    Bienvenue, {user.firstName} {user.lastName}
                  </span>
                  <img
                            className="h-10 w-10 rounded-full object-cover"
                            src={user.photoUrl ? `${IMG_URL}${user.photoUrl}` : '/default_profile.jpeg'}
                            alt="Photo utilisateur"
                          />
                </div>
              </div>
            </div>
          </header>

          <main className="p-6">{children}</main>
        </div>
      </div>
    </div>
  );
}
