import React from 'react';
import { format, startOfWeek, addDays } from 'date-fns';

interface TimeSlot {
  time: string;
  isBooked: boolean;
  clientName?: string;
  service?: string;
}

interface DaySchedule {
  date: Date;
  slots: TimeSlot[];
}

// Mock data generator
const generateWeekSchedule = (startDate: Date): DaySchedule[] => {
  const week: DaySchedule[] = [];
  const weekStart = startOfWeek(startDate);

  for (let i = 0; i < 7; i++) {
    const date = addDays(weekStart, i);
    const slots: TimeSlot[] = [];
    let hour = 9;

    while (hour < 17) {
      // Randomly generate booked slots for demo
      const isBooked = Math.random() > 0.7;
      slots.push({
        time: `${hour}:00`,
        isBooked,
        ...(isBooked && {
          clientName: '<PERSON>',
          service: 'Haircut',
        }),
      });
      slots.push({
        time: `${hour}:30`,
        isBooked: Math.random() > 0.7,
        ...(isBooked && {
          clientName: '<PERSON>',
          service: 'Color',
        }),
      });
      hour++;
    }

    week.push({ date, slots });
  }

  return week;
};

export default function StaffSchedule() {
  const weekSchedule = generateWeekSchedule(new Date());

  return (
    <div className="overflow-x-auto">
      <div className="inline-block min-w-full align-middle">
        <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg">
          <table className="min-w-full divide-y divide-gray-300">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">
                  Time
                </th>
                {weekSchedule.map((day) => (
                  <th
                    key={day.date.toISOString()}
                    scope="col"
                    className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                  >
                    {format(day.date, 'EEE dd/MM')}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {Array.from({ length: 16 }).map((_, index) => {
                const timeSlot = index % 2 === 0 ? `${9 + Math.floor(index / 2)}:00` : `${9 + Math.floor(index / 2)}:30`;
                return (
                  <tr key={timeSlot}>
                    <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900">
                      {timeSlot}
                    </td>
                    {weekSchedule.map((day) => {
                      const slot = day.slots[index];
                      return (
                        <td
                          key={`${day.date.toISOString()}-${timeSlot}`}
                          className="whitespace-nowrap px-3 py-4 text-sm text-gray-500"
                        >
                          {slot.isBooked ? (
                            <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              Booked
                            </div>
                          ) : (
                            <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              Available
                            </div>
                          )}
                        </td>
                      );
                    })}
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}