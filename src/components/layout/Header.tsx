import React from 'react';
import { Link } from 'react-router-dom';
import { Scissors } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import UserMenu from './UserMenu';
import LoginButton from './LoginButton';

export default function Header() {
  const { user, isLoading, logout } = useAuth();

  // Affiche un message de chargement si l'état utilisateur n'est pas encore disponible
  if (isLoading) {
    return <div>Chargement...</div>;
  }

  return (
    <nav className="bg-white shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo et navigation */}
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <Link to="/" className="flex items-center">
                <Scissors className="h-8 w-8 text-indigo-600" />
                <span className="ml-2 text-xl font-bold text-gray-900">Best-Salon</span>
              </Link>
            </div>
            <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
              <Link
                to="/"
                className="inline-flex items-center px-1 pt-1 text-sm font-medium text-gray-900"
              >
                Accueil
              </Link>
              <Link
                to="/salons"
                className="inline-flex items-center px-1 pt-1 text-sm font-medium text-gray-500 hover:text-gray-900"
              >
                Nos Salons
              </Link>
            </div>
          </div>

          {/* Menu utilisateur ou bouton de connexion */}
          <div className="flex items-center">
            {user ? (
              <UserMenu user={user} onLogout={logout} />
            ) : (
              <LoginButton />
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}
