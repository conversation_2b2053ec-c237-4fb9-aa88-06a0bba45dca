import React from 'react';
import { Link } from 'react-router-dom';
import type { User } from '../../components/auth/types';
import { IMG_URL } from '../../context/url';

interface UserMenuProps {
  user: User;
  onLogout: () => void;
}

export default function UserMenu({ user, onLogout }: UserMenuProps) {
  return (
    <div className="flex items-center space-x-4">
      {/* Lien vers le profil */}
      <Link
        to="/profile"
        className="flex items-center text-sm font-medium text-gray-700 hover:text-gray-900"
      >
        <img
          className="h-8 w-8 rounded-full object-cover"
          src={user.photoUrl ? `${IMG_URL}${user.photoUrl}` : '/default_profile.jpeg'}
          alt={`${user.firstName} ${user.lastName}`}
        />
        <span className="ml-2">{user.firstName}</span>
      </Link>
      {/* Bouton de déconnexion */}
      <button
        onClick={onLogout}
        className="text-sm font-medium text-gray-700 hover:text-gray-900"
      >
        Déconnexion
      </button>
    </div>
  );
}
