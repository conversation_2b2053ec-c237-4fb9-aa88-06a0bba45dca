import React from 'react';
import { Clock, DollarSign } from 'lucide-react';
import type { Service } from '../types';

interface ServiceListProps {
  services: Service[];
  onSelect?: (service: Service) => void;
}

export default function ServiceList({ services, onSelect }: ServiceListProps) {
  return (
    <div className="space-y-4">
      {services.map((service) => (
        <div
          key={service.id}
          className="flex justify-between items-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow"
          onClick={() => onSelect?.(service)}
        >
          <div>
            <h3 className="font-medium text-gray-900">{service.name}</h3>
            <p className="text-sm text-gray-500">{service.description}</p>
            <div className="mt-1 flex items-center space-x-4">
              <span className="flex items-center text-sm text-gray-500">
                <Clock className="h-4 w-4 mr-1" />
                {service.duration} min
              </span>
            </div>
          </div>
          <div className="flex items-center">
            <span className="flex items-center text-lg font-semibold text-gray-900">
              <DollarSign className="h-5 w-5" />
              {service.price}
            </span>
            {onSelect && (
              <button className="ml-4 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                Book
              </button>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}