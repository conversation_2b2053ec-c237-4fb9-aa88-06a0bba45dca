import React from 'react';
import type { Hairdresser } from '../types';
import { IMG_URL } from '../context/url';

interface StaffListProps {
  hairdressers: Hairdresser[];
  onSelect?: (hairdresser: Hairdresser) => void;
}

export default function StaffList({ hairdressers, onSelect }: StaffListProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {hairdressers.map((hairdresser) => (
        <div
          key={hairdresser.id}
          className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-4"
          onClick={() => onSelect?.(hairdresser)}
        >
          <img
            src={hairdresser.photoUrl ? `${IMG_URL}${hairdresser.photoUrl}` : `https://source.unsplash.com/400x400/?hairdresser&${hairdresser.id}`}
            alt={`${hairdresser.firstName} ${hairdresser.lastName}`}
            className="w-full h-48 object-cover rounded-lg mb-4"
          />
          <h3 className="font-medium text-gray-900">
            {hairdresser.firstName} {hairdresser.lastName}
          </h3>
          <div className="mt-2">
            <h4 className="text-sm font-medium text-gray-700">Specialties:</h4>
            <div className="mt-1 flex flex-wrap gap-2">
              {hairdresser.specialties.map((specialty, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800"
                >
                  {specialty}
                </span>
              ))}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}