import React, { useState } from 'react';
import { X } from 'lucide-react';
import type { Service, Hairdresser, Salon } from '../types';
import ServiceSelection from './booking/ServiceSelection';
import StaffSelection from './booking/StaffSelection';
import DateTimeSelection from './booking/DateTimeSelection';
import BookingSummary from './booking/BookingSummary';
import AuthModal from './AuthModal'; // Import du composant AuthModal
import { useAuth } from '../context/AuthContext'; // Import du contexte d'authentification
import { BASE_URL } from '../context/url';
import toast from 'react-hot-toast';

interface BookingModalProps {
  salon: Salon;
  isOpen: boolean;
  onClose: () => void;
}

type BookingStep = 'services' | 'staff' | 'datetime' | 'summary';

export default function BookingModal({ salon, isOpen, onClose }: BookingModalProps) {
  const { user } = useAuth(); // Utiliser le contexte d'authentification pour récupérer l'utilisateur connecté
  const [step, setStep] = useState<BookingStep>('services');
  const [selectedServices, setSelectedServices] = useState<Service[]>([]);
  const [selectedHairdresser, setSelectedHairdresser] = useState<Hairdresser | null>(null);
  const [selectedDateTime, setSelectedDateTime] = useState<string | null>(null);
  const [showAuthModal, setShowAuthModal] = useState(false); // État pour afficher AuthModal
  const [filteredHairdressers, setFilteredHairdressers] = useState<Hairdresser[]>([]);
  const [isLoadingHairdressers, setIsLoadingHairdressers] = useState(false);

  if (!isOpen) return null;

  const steps = [
    { id: 'services', label: 'Services' },
    { id: 'staff', label: 'Stylist' },
    { id: 'datetime', label: 'Date & Time' },
    { id: 'summary', label: 'Summary' },
  ];

  const fetchFilteredHairdressers = async (services: Service[]) => {
    if (services.length === 0) {
      setFilteredHairdressers(salon.hairdressers);
      return;
    }

    setIsLoadingHairdressers(true);
    try {
      const serviceIds = services.map(service => service.id).join(',');
      const response = await fetch(`${BASE_URL}/salons/${salon.id}/hairdressers-by-services?services=${serviceIds}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log('Filtered hairdressers response:', data);
        
        // Convertir les données reçues au format Hairdresser
        const hairdressers: Hairdresser[] = data.data.map((h: any) => ({
          id: h.id.toString(),
          firstName: h.firstName,
          lastName: h.lastName,
          photoUrl: h.photoUrl,
          specialties: Array.isArray(h.specialties) ? h.specialties : [h.specialties],
          availability: h.availability,
        }));
        
        setFilteredHairdressers(hairdressers);
      } else {
        console.error('Failed to fetch filtered hairdressers');
        setFilteredHairdressers(salon.hairdressers);
      }
    } catch (error) {
      console.error('Error fetching filtered hairdressers:', error);
      setFilteredHairdressers(salon.hairdressers);
    } finally {
      setIsLoadingHairdressers(false);
    }
  };

  const handleNext = () => {
    switch (step) {
      case 'services':
        // Récupérer les hairdressers filtrés avant de passer à l'étape staff
        fetchFilteredHairdressers(selectedServices);
        setStep('staff');
        break;
      case 'staff':
        setStep('datetime');
        break;
      case 'datetime':
        setStep('summary');
        break;
      default:
        break;
    }
  };

  const handleBack = () => {
    switch (step) {
      case 'staff':
        // Réinitialiser les hairdressers filtrés quand on revient aux services
        setFilteredHairdressers([]);
        setSelectedHairdresser(null);
        setStep('services');
        break;
      case 'datetime':
        setStep('staff');
        break;
      case 'summary':
        setStep('datetime');
        break;
      default:
        break;
    }
  };

  const checkAvailability = async (hairdresserId: number, dateTime: string) => {
    try {
      const response = await fetch(`${BASE_URL}/appointments/check-availability?hairdresserId=${hairdresserId}&dateTime=${encodeURIComponent(dateTime)}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        return data;
      } else {
        console.error('Error checking availability');
        return { available: false };
      }
    } catch (error) {
      console.error('Error checking availability:', error);
      return { available: false };
    }
  };

  const handleConfirmBooking = async () => {
    if (!user) {
      setShowAuthModal(true);
      return;
    }

    if (!selectedDateTime || new Date(selectedDateTime) <= new Date()) {
      toast.error('Please select a valid date and time in the future.');
      return;
    }

    if (!selectedHairdresser) {
      toast.error('Please select a hairdresser.');
      return;
    }

    // Vérifier la disponibilité avant de créer la réservation
    const availability = await checkAvailability(Number(selectedHairdresser.id), new Date(selectedDateTime).toISOString());
    
    if (!availability.available) {
      if (!availability.hairdresser_available) {
        toast.error('Ce créneau horaire est déjà réservé pour ce coiffeur. Veuillez choisir un autre horaire.');
      } else if (!availability.client_available) {
        toast.error('Vous avez déjà une réservation à cette date et heure. Veuillez choisir un autre créneau.');
      } else {
        toast.error('Ce créneau horaire n\'est pas disponible.');
      }
      return;
    }

    try {
      const requestData = {
        salonId: salon.id,
        hairdresserId: selectedHairdresser?.id,
        services: selectedServices.map(service => service.id),
        dateTime: new Date(selectedDateTime).toISOString(),
      };
      
      console.log('Sending booking request:', requestData);
      
      const response = await fetch(`${BASE_URL}/appointments/add-boking`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
        body: JSON.stringify(requestData),
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Booking confirmed:', data);
        toast.success('Booking confirmed!');
        onClose();
      } else {
        const errorData = await response.json();
        console.error('Booking failed:', errorData);
        
        // Gestion spécifique des erreurs de validation
        if (errorData.message) {
          toast.error(errorData.message);
        } else if (errorData.errors) {
          // Si c'est une erreur de validation Laravel
          const errorMessages = Object.values(errorData.errors).flat();
          toast.error(errorMessages.join(', '));
        } else {
          toast.error('Erreur lors de la création de la réservation');
        }
      }
    } catch (error) {
      console.error('An error occurred:', error);
      if (error instanceof Error) {
        toast.error('An error occurred: ' + error.message);
      } else {
        toast.error('An unknown error occurred.');
      }
    }
  };

  const generateTimeSlots = (date: Date) => {
    // ... existing code ...
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {showAuthModal && <AuthModal isOpen={showAuthModal} onClose={() => setShowAuthModal(false)} />}
      <div className="flex min-h-screen items-center justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />

        <div className="inline-block w-full max-w-2xl transform overflow-hidden rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:align-middle">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between pb-4 border-b">
              <h3 className="text-lg font-medium leading-6 text-gray-900">Book Appointment</h3>
              <button
                onClick={onClose}
                className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="mt-4">
              <nav aria-label="Progress">
                <ol className="flex items-center">
                  {steps.map((s, index) => (
                    <li key={s.id} className={`relative ${index !== steps.length - 1 ? 'pr-8 sm:pr-20' : ''}`}>
                      <div className="flex items-center">
                        <div
                          className={`
                            h-6 w-6 rounded-full flex items-center justify-center
                            ${step === s.id ? 'bg-indigo-600' : 'bg-gray-300'}
                            ${index < steps.findIndex(x => x.id === step) ? 'bg-indigo-600' : ''}
                          `}
                        >
                          <span className="text-white text-sm">{index + 1}</span>
                        </div>
                        <span className="ml-2 text-sm font-medium text-gray-900">{s.label}</span>
                      </div>
                      {index !== steps.length - 1 && (
                        <div className="absolute top-3 left-full h-0.5 w-full max-w-16 bg-gray-300"></div>
                      )}
                    </li>
                  ))}
                </ol>
              </nav>

              <div className="mt-6">
                {step === 'services' && (
                  <ServiceSelection
                    services={salon.services}
                    selectedServices={selectedServices}
                    onServicesChange={setSelectedServices}
                  />
                )}
                {step === 'staff' && (
                  <div>
                    {isLoadingHairdressers ? (
                      <div className="flex justify-center items-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                        <span className="ml-2 text-gray-600">Chargement des coiffeurs...</span>
                      </div>
                                         ) : (
                       <>
                         {filteredHairdressers.length > 0 ? (
                           <StaffSelection
                             hairdressers={filteredHairdressers}
                             selectedHairdresser={selectedHairdresser}
                             onHairdresserSelect={setSelectedHairdresser}
                           />
                         ) : selectedServices.length > 0 ? (
                           <div className="text-center py-8">
                             <p className="text-gray-500">
                               Aucun coiffeur disponible pour les services sélectionnés.
                             </p>
                             <p className="text-sm text-gray-400 mt-2">
                               Veuillez sélectionner d'autres services ou revenir à l'étape précédente.
                             </p>
                           </div>
                         ) : (
                           <StaffSelection
                             hairdressers={salon.hairdressers}
                             selectedHairdresser={selectedHairdresser}
                             onHairdresserSelect={setSelectedHairdresser}
                           />
                         )}
                       </>
                     )}
                  </div>
                )}
                {step === 'datetime' && (
                  <DateTimeSelection
                    hairdresser={selectedHairdresser!}
                    onDateTimeSelect={setSelectedDateTime}
                    selectedDateTime={selectedDateTime}
                    salon={salon}
                  />
                )}
                {step === 'summary' && (
                  <BookingSummary
                    salon={salon}
                    services={selectedServices}
                    hairdresser={selectedHairdresser!}
                    dateTime={selectedDateTime!}
                  />
                )}
              </div>
            </div>
          </div>

          <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
            {step === 'summary' ? (
              <button
                type="button"
                onClick={handleConfirmBooking}
                className="inline-flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-indigo-700 sm:ml-3 sm:w-auto sm:text-sm"
              >
                Confirm Booking
              </button>
            ) : (
              <button
                type="button"
                onClick={handleNext}
                disabled={
                  (step === 'services' && selectedServices.length === 0) ||
                  (step === 'staff' && !selectedHairdresser) ||
                  (step === 'datetime' && !selectedDateTime)
                }
                className="inline-flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-indigo-700 disabled:bg-gray-300 sm:ml-3 sm:w-auto sm:text-sm"
              >
                Next
              </button>
            )}
            {step !== 'services' && (
              <button
                type="button"
                onClick={handleBack}
                className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 sm:mt-0 sm:w-auto sm:text-sm"
              >
                Back
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
