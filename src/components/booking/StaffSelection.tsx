import React from 'react';
import { Check } from 'lucide-react';
import type { Hairdresser } from '../../types';
import { IMG_URL } from '../../context/url';

interface StaffSelectionProps {
  hairdressers: Hairdresser[];
  selectedHairdresser: Hairdresser | null;
  onHairdresserSelect: (hairdresser: Hairdresser) => void;
}

export default function StaffSelection({
  hairdressers,
  selectedHairdresser,
  onHairdresserSelect,
}: StaffSelectionProps) {
  return (
    <div className="space-y-4">
      <h4 className="text-lg font-medium text-gray-900">Choisissez votre coiffeur</h4>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {hairdressers.map((hairdresser) => {
          const isSelected = selectedHairdresser?.id === hairdresser.id;
          return (
            <div
              key={hairdresser.id}
              onClick={() => onHairdresserSelect(hairdresser)}
              className={`
                p-4 rounded-lg border-2 cursor-pointer transition-all
                ${isSelected ? 'border-indigo-600 bg-indigo-50' : 'border-gray-200 hover:border-indigo-200'}
              `}
            >
              <div className="flex space-x-4">
                <img
                  src={hairdresser.photoUrl ? `${IMG_URL}${hairdresser.photoUrl}` : `https://source.unsplash.com/400x400/?hairdresser&${hairdresser.id}`}
                  alt={`${hairdresser.firstName} ${hairdresser.lastName}`}
                  className="h-20 w-20 rounded-full object-cover"
                />
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium text-gray-900">
                      {hairdresser.firstName} {hairdresser.lastName}
                    </h3>
                    {isSelected && (
                      <div className="h-6 w-6 rounded-full bg-indigo-600 flex items-center justify-center">
                        <Check className="h-4 w-4 text-white" />
                      </div>
                    )}
                  </div>
                  <div className="mt-2 flex flex-wrap gap-2">
                    {hairdresser.specialties.map((specialty, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800"
                      >
                        {specialty}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}