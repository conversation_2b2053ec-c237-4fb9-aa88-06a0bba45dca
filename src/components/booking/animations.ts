import { Variants } from 'framer-motion';

export const modalVariants: Variants = {
  hidden: {
    opacity: 0,
    scale: 0.95,
    y: 20,
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: 'spring',
      damping: 20,
      stiffness: 300,
    },
  },
  exit: {
    opacity: 0,
    scale: 0.95,
    y: 20,
  },
};

export const stepVariants: Variants = {
  enter: (direction: number) => ({
    x: direction > 0 ? 100 : -100,
    opacity: 0,
  }),
  center: {
    x: 0,
    opacity: 1,
  },
  exit: (direction: number) => ({
    x: direction < 0 ? 100 : -100,
    opacity: 0,
  }),
};

export const errorVariants: Variants = {
  hidden: {
    opacity: 0,
    y: -10,
    height: 0,
  },
  visible: {
    opacity: 1,
    y: 0,
    height: 'auto',
    transition: {
      duration: 0.2,
    },
  },
  exit: {
    opacity: 0,
    y: -10,
    height: 0,
    transition: {
      duration: 0.2,
    },
  },
};

export const progressVariants: Variants = {
  inactive: {
    scale: 1,
    backgroundColor: '#D1D5DB',
  },
  active: {
    scale: 1.1,
    backgroundColor: '#4F46E5',
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 20,
    },
  },
  complete: {
    backgroundColor: '#4F46E5',
    scale: 1,
  },
};

// Ajoutez cette ligne pour éviter l'erreur
export {};
