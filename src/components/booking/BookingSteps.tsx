import React from 'react';
import type { Service, Hairdresser, Salon } from '../../types';
import ServiceSelection from './steps/ServiceSelection';
import HairdresserSelection from './steps/HairdresserSelection';
import DateTimeSelection from './steps/DateTimeSelection';
import BookingSummary from './steps/BookingSummary';

export type BookingStep = 'services' | 'hairdresser' | 'datetime' | 'summary';

interface BookingStepsProps {
  currentStep: BookingStep;
  salon: Salon;
  services: Service[];
  selectedServices: Service[];
  onServicesChange: (services: Service[]) => void;
  hairdressers: Hairdresser[];
  selectedHairdresser: Hairdresser | null;
  onHairdresserSelect: (hairdresser: Hairdresser) => void;
  selectedDateTime: string | null;
  onDateTimeSelect: (dateTime: string) => void;
}

export default function BookingSteps({
  currentStep,
  salon,
  services,
  selectedServices,
  onServicesChange,
  hairdressers,
  selectedHairdresser,
  onHairdresserSelect,
  selectedDateTime,
  onDateTimeSelect,
}: BookingStepsProps) {
  const renderStep = () => {
    switch (currentStep) {
      case 'services':
        return (
          <ServiceSelection
            services={services}
            selectedServices={selectedServices}
            onServicesChange={onServicesChange}
          />
        );
      case 'hairdresser':
        return (
          <HairdresserSelection
            hairdressers={hairdressers}
            selectedHairdresser={selectedHairdresser}
            onHairdresserSelect={onHairdresserSelect}
          />
        );
      case 'datetime':
        return (
          <DateTimeSelection
            hairdresser={selectedHairdresser!}
            selectedDateTime={selectedDateTime}
            onDateTimeSelect={onDateTimeSelect}
          />
        );
      case 'summary':
        return (
          <BookingSummary
            salon={salon}
            services={selectedServices}
            hairdresser={selectedHairdresser!}
            dateTime={selectedDateTime!}
          />
        );
      default:
        return null;
    }
  };

  return <div className="mt-6">{renderStep()}</div>;
}