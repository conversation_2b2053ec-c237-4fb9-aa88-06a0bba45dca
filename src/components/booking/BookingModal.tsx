import React from 'react';
import { X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import type { Salon } from '../../types';
import { useBooking } from '../../hooks/useBooking';
import BookingProgress from './BookingProgress';
import BookingSteps from './BookingSteps';
import { modalVariants, errorVariants, stepVariants } from './animations';

interface BookingModalProps {
  salon: Salon;
  isOpen: boolean;
  onClose: () => void;
}

export default function BookingModal({ salon, isOpen, onClose }: BookingModalProps) {
  const {
    currentStep,
    selectedServices,
    setSelectedServices,
    selectedHairdresser,
    setSelectedHairdresser,
    selectedDateTime,
    setSelectedDateTime,
    error,
    isLoading,
    goToNextStep,
    goToPreviousStep,
    confirmBooking,
    direction,
  } = useBooking({
    salonId: salon.id, // Ajouter salonId ici
    onComplete: () => {
      onClose();
    },
  });


  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex min-h-screen items-center justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
            onClick={onClose}
          />

          <motion.div
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="inline-block w-full max-w-2xl transform overflow-hidden rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:align-middle"
          >
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="flex items-center justify-between pb-4 border-b">
                <h3 className="text-lg font-medium leading-6 text-gray-900">
                  Réserver un rendez-vous
                </h3>
                <button
                  onClick={onClose}
                  className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              <div className="mt-4">
                <BookingProgress currentStep={currentStep} />

                <AnimatePresence initial={false} mode="wait" custom={direction}>
                  {error && (
                    <motion.div
                      variants={errorVariants}
                      initial="hidden"
                      animate="visible"
                      exit="exit"
                      className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md"
                    >
                      <p className="text-sm text-red-600">{error.message}</p>
                    </motion.div>

                  )}

                  <motion.div
                    key={currentStep}
                    custom={direction}
                    variants={stepVariants}
                    initial="enter"
                    animate="center"
                    exit="exit"
                    transition={{
                      type: 'spring',
                      stiffness: 300,
                      damping: 30,
                    }}
                  >
                    <BookingSteps
                      currentStep={currentStep}
                      salon={salon}
                      services={salon.services}
                      selectedServices={selectedServices}
                      onServicesChange={setSelectedServices}
                      hairdressers={salon.hairdressers}
                      selectedHairdresser={selectedHairdresser}
                      onHairdresserSelect={setSelectedHairdresser}
                      selectedDateTime={selectedDateTime}
                      onDateTimeSelect={setSelectedDateTime}
                    />
                  </motion.div>
                </AnimatePresence>
              </div>
            </div>

            <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
              {currentStep === 'summary' ? (
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  type="button"
                  onClick={confirmBooking}
                  disabled={isLoading}
                  className="inline-flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-indigo-700 disabled:bg-indigo-300 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  {isLoading ? 'Confirmation en cours...' : 'Confirmer la réservation'}
                </motion.button>
              ) : (
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  type="button"
                  onClick={goToNextStep}
                  disabled={isLoading}
                  className="inline-flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-indigo-700 disabled:bg-indigo-300 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  {isLoading ? 'Vérification...' : 'Suivant'}
                </motion.button>
              )}
              {currentStep !== 'services' && (
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  type="button"
                  onClick={goToPreviousStep}
                  disabled={isLoading}
                  className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 sm:mt-0 sm:w-auto sm:text-sm"
                >
                  Retour
                </motion.button>
              )}
            </div>
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  );
}
