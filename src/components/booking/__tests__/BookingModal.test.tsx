import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import BookingModal from '../BookingModal';
import { mockSalon } from '../../../test/mocks';

jest.mock('../../../hooks/useBooking', () => ({
  useBooking: () => ({
    currentStep: 'services',
    selectedServices: [],
    setSelectedServices: jest.fn(),
    selectedHairdresser: null,
    setSelectedHairdresser: jest.fn(),
    selectedDateTime: null,
    setSelectedDateTime: jest.fn(),
    error: null,
    isLoading: false,
    goToNextStep: jest.fn(),
    goToPreviousStep: jest.fn(),
    confirmBooking: jest.fn(),
  }),
}));

describe('BookingModal', () => {
  const defaultProps = {
    salon: mockSalon,
    isOpen: true,
    onClose: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the modal when isOpen is true', () => {
    render(
      <MemoryRouter>
        <BookingModal {...defaultProps} />
      </MemoryRouter>
    );

    expect(screen.getByText('Réserver un rendez-vous')).toBeInTheDocument();
  });

  it('does not render when isOpen is false', () => {
    render(
      <MemoryRouter>
        <BookingModal {...defaultProps} isOpen={false} />
      </MemoryRouter>
    );

    expect(screen.queryByText('Réserver un rendez-vous')).not.toBeInTheDocument();
  });

  it('calls onClose when clicking the close button', () => {
    render(
      <MemoryRouter>
        <BookingModal {...defaultProps} />
      </MemoryRouter>
    );

    fireEvent.click(screen.getByRole('button', { name: /close/i }));
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('displays error message when present', () => {
    const error = 'Test error message';
    jest.spyOn(require('../../../hooks/useBooking'), 'useBooking').mockImplementation(() => ({
      ...jest.requireActual('../../../hooks/useBooking').useBooking(),
      error,
    }));

    render(
      <MemoryRouter>
        <BookingModal {...defaultProps} />
      </MemoryRouter>
    );

    expect(screen.getByText(error)).toBeInTheDocument();
  });

  it('disables buttons when loading', () => {
    jest.spyOn(require('../../../hooks/useBooking'), 'useBooking').mockImplementation(() => ({
      ...jest.requireActual('../../../hooks/useBooking').useBooking(),
      isLoading: true,
    }));

    render(
      <MemoryRouter>
        <BookingModal {...defaultProps} />
      </MemoryRouter>
    );

    expect(screen.getByText('Vérification...')).toBeDisabled();
  });
});