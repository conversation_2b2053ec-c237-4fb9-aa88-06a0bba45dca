import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import BookingSteps from '../BookingSteps';
import { mockSalon } from '../../../test/mocks';

describe('BookingSteps', () => {
  const defaultProps = {
    currentStep: 'services' as const,
    salon: mockSalon,
    services: mockSalon.services,
    selectedServices: [],
    onServicesChange: jest.fn(),
    hairdressers: mockSalon.hairdressers,
    selectedHairdresser: null,
    onHairdresserSelect: jest.fn(),
    selectedDateTime: null,
    onDateTimeSelect: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders service selection step', () => {
    render(<BookingSteps {...defaultProps} />);
    expect(screen.getByText('Sélectionnez vos services')).toBeInTheDocument();
  });

  it('renders hairdresser selection step', () => {
    render(<BookingSteps {...defaultProps} currentStep="hairdresser" />);
    expect(screen.getByText('Choisissez votre coiffeur')).toBeInTheDocument();
  });

  it('renders datetime selection step', () => {
    render(
      <BookingSteps
        {...defaultProps}
        currentStep="datetime"
        selectedHairdresser={mockSalon.hairdressers[0]}
      />
    );
    expect(screen.getByText('Sélectionnez une date')).toBeInTheDocument();
  });

  it('renders booking summary step', () => {
    render(
      <BookingSteps
        {...defaultProps}
        currentStep="summary"
        selectedServices={[mockSalon.services[0]]}
        selectedHairdresser={mockSalon.hairdressers[0]}
        selectedDateTime="2024-03-20T14:00:00.000Z"
      />
    );
    expect(screen.getByText('Récapitulatif de la réservation')).toBeInTheDocument();
  });

  it('calls onServicesChange when selecting a service', () => {
    render(<BookingSteps {...defaultProps} />);
    fireEvent.click(screen.getByText(mockSalon.services[0].name));
    expect(defaultProps.onServicesChange).toHaveBeenCalled();
  });

  it('calls onHairdresserSelect when selecting a hairdresser', () => {
    render(<BookingSteps {...defaultProps} currentStep="hairdresser" />);
    fireEvent.click(screen.getByText(`${mockSalon.hairdressers[0].firstName} ${mockSalon.hairdressers[0].lastName}`));
    expect(defaultProps.onHairdresserSelect).toHaveBeenCalled();
  });
});