import React from 'react';
import { render, screen } from '@testing-library/react';
import BookingSummary from '../steps/BookingSummary';
import { mockSalon } from '../../../test/mocks';

describe('BookingSummary', () => {
  const defaultProps = {
    salon: mockSalon,
    services: [mockSalon.services[0]],
    hairdresser: mockSalon.hairdressers[0],
    dateTime: '2024-03-20T14:00:00.000Z',
  };

  it('renders salon information', () => {
    render(<BookingSummary {...defaultProps} />);
    expect(screen.getByText(mockSalon.name)).toBeInTheDocument();
    expect(screen.getByText(mockSalon.address)).toBeInTheDocument();
  });

  it('renders service details', () => {
    render(<BookingSummary {...defaultProps} />);
    expect(screen.getByText(mockSalon.services[0].name)).toBeInTheDocument();
    expect(screen.getByText(`${mockSalon.services[0].price}€`)).toBeInTheDocument();
  });

  it('renders hairdresser information', () => {
    render(<BookingSummary {...defaultProps} />);
    expect(screen.getByText(`${mockSalon.hairdressers[0].firstName} ${mockSalon.hairdressers[0].lastName}`)).toBeInTheDocument();
  });

  it('calculates total duration and price correctly', () => {
    const multipleServices = [
      mockSalon.services[0],
      { ...mockSalon.services[0], id: '2', price: 30, duration: 30 },
    ];
    render(<BookingSummary {...defaultProps} services={multipleServices} />);
    
    const totalDuration = multipleServices.reduce((sum, service) => sum + service.duration, 0);
    const totalPrice = multipleServices.reduce((sum, service) => sum + service.price, 0);
    
    expect(screen.getByText(`${totalDuration} minutes`)).toBeInTheDocument();
    expect(screen.getByText(`${totalPrice}€`)).toBeInTheDocument();
  });
});