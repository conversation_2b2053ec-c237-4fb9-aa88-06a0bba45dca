import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import DateTimeSelection from '../steps/DateTimeSelection';
import { mockSalon } from '../../../test/mocks';

describe('DateTimeSelection', () => {
  const defaultProps = {
    hairdresser: mockSalon.hairdressers[0],
    selectedDateTime: null,
    onDateTimeSelect: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders date selection', () => {
    render(<DateTimeSelection {...defaultProps} />);
    expect(screen.getByText('Sélectionnez une date')).toBeInTheDocument();
  });

  it('renders time slots', () => {
    render(<DateTimeSelection {...defaultProps} />);
    expect(screen.getByText('09:00')).toBeInTheDocument();
    expect(screen.getByText('09:30')).toBeInTheDocument();
  });

  it('highlights selected date', () => {
    const today = new Date();
    render(<DateTimeSelection {...defaultProps} />);
    const dateButton = screen.getByText(today.getDate().toString());
    fireEvent.click(dateButton);
    expect(dateButton.parentElement).toHaveClass('border-indigo-600');
  });

  it('calls onDateTimeSelect when selecting a time slot', () => {
    render(<DateTimeSelection {...defaultProps} />);
    fireEvent.click(screen.getByText('09:00'));
    expect(defaultProps.onDateTimeSelect).toHaveBeenCalled();
  });
});