import React, { useState } from 'react';
import { format, addDays, startOfDay } from 'date-fns';
import { fr } from 'date-fns/locale';
import type { Hairdresser, Salon } from '../../types';

interface DateTimeSelectionProps {
  hairdresser: Hairdresser;
  salon: Salon;
  selectedDateTime: string | null;
  onDateTimeSelect: (dateTime: string) => void;
}

const generateTimeSlots = (date: Date) => {
  const slots = [];
  let hour = 9;
  while (hour < 17) {
    slots.push(
      new Date(date.getFullYear(), date.getMonth(), date.getDate(), hour, 0),
      new Date(date.getFullYear(), date.getMonth(), date.getDate(), hour, 30)
    );
    hour++;
  }
  return slots;
};

export default function DateTimeSelection({
  hairdresser,
  salon,
  selectedDateTime,
  onDateTimeSelect,
}: DateTimeSelectionProps) {
  const [selectedDate, setSelectedDate] = useState(startOfDay(new Date()));
  const availableDates = Array.from({ length: 7 }, (_, i) => addDays(new Date(), i));
  const timeSlots = generateTimeSlots(selectedDate);

  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-4">Sélectionnez une date</h4>
        <div className="grid grid-cols-7 gap-2">
          {availableDates.map((date) => {
            const isSelected = selectedDate.getTime() === startOfDay(date).getTime();
            return (
              <button
                key={date.toISOString()}
                onClick={() => setSelectedDate(startOfDay(date))}
                className={`
                  p-2 text-center rounded-lg border transition-colors
                  ${isSelected
                    ? 'border-indigo-600 bg-indigo-50 text-indigo-600'
                    : 'border-gray-200 hover:border-indigo-200'
                  }
                `}
              >
                <div className="text-xs text-gray-500">
                  {format(date, 'EEE', { locale: fr })}
                </div>
                <div className="text-sm font-semibold">
                  {format(date, 'd', { locale: fr })}
                </div>
              </button>
            );
          })}
        </div>
      </div>

      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-4">Sélectionnez une heure</h4>
        <div className="grid grid-cols-3 sm:grid-cols-4 gap-2">
          {timeSlots.map((slot) => {
            const dateTimeString = slot.toISOString();
            const isSelected = selectedDateTime === dateTimeString;
            return (
              <button
                key={dateTimeString}
                onClick={() => onDateTimeSelect(dateTimeString)}
                className={`
                  p-2 text-center rounded-lg border transition-colors
                  ${isSelected
                    ? 'border-indigo-600 bg-indigo-50 text-indigo-600'
                    : 'border-gray-200 hover:border-indigo-200'
                  }
                `}
              >
                {format(slot, 'HH:mm')}
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );
}