import React from 'react';
import { motion } from 'framer-motion';
import type { BookingStep } from './BookingSteps';

import { progressVariants } from './animations';

interface BookingProgressProps {
  currentStep: BookingStep;
}

const steps: { id: BookingStep; label: string }[] = [
  { id: 'services', label: 'Services' },
  { id: 'hairdresser', label: 'Coiff<PERSON>' },
  { id: 'datetime', label: 'Date & Heure' },
  { id: 'summary', label: 'Résumé' },
];

export default function BookingProgress({ currentStep }: BookingProgressProps) {
  const currentStepIndex = steps.findIndex(step => step.id === currentStep);

  if (currentStepIndex === -1) {
    console.warn(`Invalid currentStep: "${currentStep}"`);
    return null;
  }

  return (
    <nav aria-label="Progress">
      <ol className="flex items-center">
        {steps.map((step, index) => {
          const isActive = index === currentStepIndex;
          const isComplete = index < currentStepIndex;

          return (
            <li
              key={step.id}
              className={`relative ${index !== steps.length - 1 ? 'pr-8 sm:pr-20' : ''}`}
              aria-current={isActive ? 'step' : undefined}
            >
              <div className="flex items-center">
                <motion.div
                  className={`h-6 w-6 rounded-full flex items-center justify-center ${
                    isComplete ? 'bg-indigo-600' : isActive ? 'bg-indigo-400' : 'bg-gray-300'
                  }`}
                  variants={progressVariants}
                  initial="inactive"
                  animate={isActive ? 'active' : isComplete ? 'complete' : 'inactive'}
                >
                  <span className="text-white text-sm">{index + 1}</span>
                </motion.div>
                <span className="ml-2 text-sm font-medium text-gray-900">{step.label}</span>
              </div>
              {index !== steps.length - 1 && (
                <motion.div
                  className="absolute top-3 left-full h-0.5 w-full"
                  initial={{ backgroundColor: '#D1D5DB' }}
                  animate={{
                    backgroundColor: isComplete ? '#4F46E5' : '#D1D5DB',
                  }}
                  transition={{ duration: 0.2 }}
                />
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
}
