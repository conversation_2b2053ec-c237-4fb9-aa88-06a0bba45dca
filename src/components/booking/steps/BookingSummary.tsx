import React from 'react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Clock, Calendar, MapPin } from 'lucide-react';
import type { Service, Hairdresser, Salon } from '../../../types';

interface BookingSummaryProps {
  salon: Salon;
  services: Service[];
  hairdresser: Hairdresser;
  dateTime: string;
}

export default function BookingSummary({
  salon,
  services,
  hairdresser,
  dateTime,
}: BookingSummaryProps) {
  const totalDuration = services.reduce((sum, service) => sum + service.duration, 0);
  const totalPrice = services.reduce((sum, service) => sum + service.price, 0);
  const appointmentDate = new Date(dateTime);

  return (
    <div className="space-y-6">
      <h4 className="text-lg font-medium text-gray-900">Récapitulatif de la réservation</h4>

      <div className="bg-gray-50 rounded-lg p-4 space-y-4">
        <div className="flex items-start space-x-3">
          <MapPin className="h-5 w-5 text-gray-400 mt-1" />
          <div>
            <h5 className="font-medium text-gray-900">{salon.name}</h5>
            <p className="text-sm text-gray-500">{salon.address}</p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <Calendar className="h-5 w-5 text-gray-400" />
          <p className="text-gray-900">
            {format(appointmentDate, 'EEEE d MMMM yyyy', { locale: fr })}
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <Clock className="h-5 w-5 text-gray-400" />
          <p className="text-gray-900">{format(appointmentDate, 'HH:mm')}</p>
        </div>
      </div>

      <div className="border-t border-b border-gray-200 py-4">
        <h5 className="font-medium text-gray-900 mb-4">Services</h5>
        <div className="space-y-2">
          {services.map((service) => (
            <div key={service.id} className="flex justify-between text-sm">
              <span className="text-gray-500">{service.name}</span>
              <span className="text-gray-900">{service.price}€</span>
            </div>
          ))}
          <div className="pt-2 border-t border-gray-200">
            <div className="flex justify-between font-medium">
              <span className="text-gray-900">Total</span>
              <span className="text-gray-900">{totalPrice}€</span>
            </div>
            <div className="flex justify-between text-sm text-gray-500">
              <span>Durée</span>
              <span>{totalDuration} minutes</span>
            </div>
          </div>
        </div>
      </div>

      <div>
        <h5 className="font-medium text-gray-900 mb-4">Coiffeur</h5>
        <div className="flex items-center space-x-4">
          <img
            src={hairdresser.photoUrl || `https://source.unsplash.com/400x400/?hairdresser&${hairdresser.id}`}
            alt={`${hairdresser.firstName} ${hairdresser.lastName}`}
            className="h-12 w-12 rounded-full object-cover"
          />
          <div>
            <p className="font-medium text-gray-900">
              {hairdresser.firstName} {hairdresser.lastName}
            </p>
            <div className="flex flex-wrap gap-2 mt-1">
              {hairdresser.specialties.map((specialty, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800"
                >
                  {specialty}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}