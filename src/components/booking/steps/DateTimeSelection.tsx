import React, { useState, useEffect } from 'react';
import { format, addDays, startOfDay } from 'date-fns';
import { fr } from 'date-fns/locale';
import { bookingService } from '../../../services/booking.service';
import type { Hairdresser } from '../../../types';
import type { TimeSlot } from '../../../api/booking';

interface DateTimeSelectionProps {
  hairdresser: Hairdresser;
  selectedDateTime: string | null;
  onDateTimeSelect: (dateTime: string) => void;
}

export default function DateTimeSelection({
  hairdresser,
  selectedDateTime,
  onDateTimeSelect,
}: DateTimeSelectionProps) {
  const [selectedDate, setSelectedDate] = useState(startOfDay(new Date()));
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const availableDates = Array.from({ length: 7 }, (_, i) => addDays(new Date(), i));

  useEffect(() => {
    fetchAvailableTimeSlots();
  }, [selectedDate, hairdresser.id]);

  const fetchAvailableTimeSlots = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const slots = await bookingService.getTimeSlots(
        hairdresser.id,
        format(selectedDate, 'yyyy-MM-dd')
      );
      setTimeSlots(slots);
    } catch (err) {
      setError('Erreur lors du chargement des créneaux disponibles');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-4">Sélectionnez une date</h4>
        <div className="grid grid-cols-7 gap-2">
          {availableDates.map((date) => {
            const isSelected = selectedDate.getTime() === startOfDay(date).getTime();
            return (
              <button
                key={date.toISOString()}
                onClick={() => setSelectedDate(startOfDay(date))}
                className={`
                  p-2 text-center rounded-lg border transition-colors
                  ${isSelected
                    ? 'border-indigo-600 bg-indigo-50 text-indigo-600'
                    : 'border-gray-200 hover:border-indigo-200'
                  }
                `}
              >
                <div className="text-xs text-gray-500">
                  {format(date, 'EEE', { locale: fr })}
                </div>
                <div className="text-sm font-semibold">
                  {format(date, 'd', { locale: fr })}
                </div>
              </button>
            );
          })}
        </div>
      </div>

      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-4">Sélectionnez une heure</h4>
        {error ? (
          <div className="text-sm text-red-600">{error}</div>
        ) : isLoading ? (
          <div className="text-sm text-gray-500">Chargement des créneaux...</div>
        ) : (
          <div className="grid grid-cols-3 sm:grid-cols-4 gap-2">
            {timeSlots.map((slot) => {
              const dateTimeString = new Date(
                selectedDate.getFullYear(),
                selectedDate.getMonth(),
                selectedDate.getDate(),
                parseInt(slot.start.split(':')[0]),
                parseInt(slot.start.split(':')[1])
              ).toISOString();

              const isSelected = selectedDateTime === dateTimeString;

              return (
                <button
                  key={`${dateTimeString}-${slot.start}`}
                  onClick={() => onDateTimeSelect(dateTimeString)}
                  disabled={!slot.available}
                  className={`
                    p-2 text-center rounded-lg border transition-colors
                    ${isSelected
                      ? 'border-indigo-600 bg-indigo-50 text-indigo-600'
                      : slot.available
                      ? 'border-gray-200 hover:border-indigo-200'
                      : 'border-gray-200 bg-gray-50 text-gray-400 cursor-not-allowed'
                    }
                  `}
                >
                  {slot.start}
                </button>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}