import React from 'react';
import { Check } from 'lucide-react';
import type { Service } from '../../../types';

interface ServiceSelectionProps {
  services: Service[];
  selectedServices: Service[];
  onServicesChange: (services: Service[]) => void;
}

export default function ServiceSelection({
  services,
  selectedServices,
  onServicesChange,
}: ServiceSelectionProps) {
  const handleServiceToggle = (service: Service) => {
    if (selectedServices.find(s => s.id === service.id)) {
      onServicesChange(selectedServices.filter(s => s.id !== service.id));
    } else {
      onServicesChange([...selectedServices, service]);
    }
  };

  return (
    <div className="space-y-4">
      <h4 className="text-lg font-medium text-gray-900">Sélectionnez vos services</h4>
      <div className="space-y-2">
        {services.map((service) => {
          const isSelected = selectedServices.find(s => s.id === service.id);
          return (
            <div
              key={service.id}
              onClick={() => handleServiceToggle(service)}
              className={`
                p-4 rounded-lg border-2 cursor-pointer transition-colors
                ${isSelected ? 'border-indigo-600 bg-indigo-50' : 'border-gray-200 hover:border-indigo-200'}
              `}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">{service.name}</h3>
                  <p className="text-sm text-gray-500">{service.description}</p>
                  <p className="mt-1 text-sm text-gray-500">{service.duration} minutes</p>
                </div>
                <div className="flex items-center space-x-4">
                  <span className="text-lg font-semibold text-gray-900">{service.price}€</span>
                  {isSelected && (
                    <div className="h-6 w-6 rounded-full bg-indigo-600 flex items-center justify-center">
                      <Check className="h-4 w-4 text-white" />
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
      {selectedServices.length > 0 && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <div className="flex justify-between items-center">
            <div>
              <p className="text-sm text-gray-500">Services sélectionnés :</p>
              <p className="text-lg font-medium text-gray-900">
                {selectedServices.length} {selectedServices.length === 1 ? 'service' : 'services'}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Total :</p>
              <p className="text-lg font-medium text-gray-900">
                {selectedServices.reduce((sum, service) => sum + service.price, 0)}€
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}