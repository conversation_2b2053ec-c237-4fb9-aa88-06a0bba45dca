import React from 'react';
import { X, Calendar, Clock, User, MapPin } from 'lucide-react';
import type { Booking } from '../../types';

interface BookingDetailModalProps {
  booking: Booking | null;
  isOpen: boolean;
  onClose: () => void;
  onConfirm?: (bookingId: string) => void;
  onReject?: (bookingId: string, reason: string) => void;
  onComplete?: (bookingId: string) => void;
}

export default function BookingDetailModal({
  booking,
  isOpen,
  onClose,
  onConfirm,
  onReject,
  onComplete
}: BookingDetailModalProps) {
  if (!isOpen || !booking) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4">
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">Détails de la réservation</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 space-y-4">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                Réservation #{booking.id}
              </h3>
            </div>
            <span className={`px-3 py-1 text-sm font-medium rounded-full ${
              booking.status === 'confirmed' ? 'bg-green-100 text-green-800' :
              booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
              'bg-red-100 text-red-800'
            }`}>
              {booking.status === 'confirmed' ? 'Confirmé' :
               booking.status === 'pending' ? 'En attente' : 'Annulé'}
            </span>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <Calendar className="h-5 w-5 text-gray-400" />
              <div>
                <p className="font-medium text-gray-900">
                  {new Date(booking.dateTime).toLocaleDateString('fr-FR')}
                </p>
                <p className="text-sm text-gray-500 flex items-center">
                  <Clock className="h-4 w-4 mr-1" />
                  {new Date(booking.dateTime).toLocaleTimeString('fr-FR')}
                </p>
              </div>
            </div>
          </div>

          <div className="border rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3 flex items-center">
              <User className="h-4 w-4 mr-2" />
              Client
            </h4>
            <p className="text-sm">
              {booking.client?.firstName} {booking.client?.lastName}
            </p>
            <p className="text-sm text-gray-500">{booking.client?.email}</p>
          </div>

          <div className="border rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3 flex items-center">
              <MapPin className="h-4 w-4 mr-2" />
              Salon
            </h4>
            <p className="text-sm">{booking.salon?.name}</p>
          </div>

                     <div className="border rounded-lg p-4">
             <h4 className="font-medium text-gray-900 mb-3">Services</h4>
             <div className="space-y-2">
               {booking.services?.map((service: any) => (
                 <div key={service.id} className="flex justify-between">
                   <span className="text-sm">{service.name}</span>
                   <span className="text-sm font-medium">
                     {service.pivot?.price_at_time || service.price}€
                   </span>
                 </div>
               ))}
             </div>
             {booking.total_amount && (
               <div className="mt-3 pt-2 border-t border-gray-200">
                 <div className="flex justify-between font-medium">
                   <span>Total</span>
                   <span>{booking.total_amount}€</span>
                 </div>
               </div>
             )}
           </div>

                     <div className="flex justify-end space-x-3 pt-4">
             <button
               onClick={onClose}
               className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
             >
               Fermer
             </button>
             
             {booking.status === 'pending' && onConfirm && (
               <button
                 onClick={() => {
                   onConfirm(booking.id);
                   onClose();
                 }}
                 className="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700"
               >
                 Confirmer
               </button>
             )}
             
             {booking.status === 'confirmed' && onComplete && (
               <button
                 onClick={() => {
                   onComplete(booking.id);
                   onClose();
                 }}
                 className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
               >
                 Marquer comme terminé
               </button>
             )}
           </div>
        </div>
      </div>
    </div>
  );
} 