import React, { useState, useEffect } from 'react';
import { BASE_URL, API_ENDPOINTS } from '../../context/url';
import { useAuth } from '../../context/AuthContext';

interface PayPalPaymentProps {
  bookingId: number;
  amount: number;
  onSuccess: (orderData: any) => void;
  onError: (error: string) => void;
  onCancel?: () => void;
}

declare global {
  interface Window {
    paypal?: any;
  }
}

const PayPalPayment: React.FC<PayPalPaymentProps> = ({
  bookingId,
  amount,
  onSuccess,
  onError,
  onCancel
}) => {
  const { token } = useAuth();
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [paypalLoaded, setPaypalLoaded] = useState(false);

  useEffect(() => {
    loadPayPalScript();
  }, []);

  useEffect(() => {
    if (paypalLoaded && window.paypal) {
      renderPayPalButton();
    }
  }, [paypalLoaded]);

  const loadPayPalScript = () => {
    // Vérifier si PayPal est déjà chargé
    if (window.paypal) {
      setPaypalLoaded(true);
      setLoading(false);
      return;
    }

    // Charger le script PayPal
    const script = document.createElement('script');
    script.src = `https://www.paypal.com/sdk/js?client-id=${process.env.REACT_APP_PAYPAL_CLIENT_ID}&currency=EUR&locale=fr_FR`;
    script.async = true;
    
    script.onload = () => {
      setPaypalLoaded(true);
      setLoading(false);
    };
    
    script.onerror = () => {
      setLoading(false);
      onError('Erreur lors du chargement de PayPal');
    };

    document.body.appendChild(script);
  };

  const createOrder = async () => {
    try {
      setProcessing(true);
      
      const response = await fetch(`${BASE_URL}${API_ENDPOINTS.PAYPAL_ORDER}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          booking_id: bookingId,
          return_url: `${window.location.origin}/payment/success`,
          cancel_url: `${window.location.origin}/payment/cancel`,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        return data.order_id;
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erreur lors de la création de la commande PayPal');
      }
    } catch (error) {
      onError(error instanceof Error ? error.message : 'Erreur inconnue');
      throw error;
    } finally {
      setProcessing(false);
    }
  };

  const onApprove = async (data: any) => {
    try {
      setProcessing(true);
      
      const response = await fetch(`${BASE_URL}${API_ENDPOINTS.PAYPAL_CAPTURE}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          order_id: data.orderID,
        }),
      });

      if (response.ok) {
        const captureData = await response.json();
        onSuccess(captureData);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erreur lors de la capture du paiement');
      }
    } catch (error) {
      onError(error instanceof Error ? error.message : 'Erreur lors du traitement du paiement');
    } finally {
      setProcessing(false);
    }
  };

  const renderPayPalButton = () => {
    const paypalButtonContainer = document.getElementById('paypal-button-container');
    if (!paypalButtonContainer || !window.paypal) return;

    // Nettoyer le conteneur
    paypalButtonContainer.innerHTML = '';

    window.paypal.Buttons({
      style: {
        layout: 'vertical',
        color: 'blue',
        shape: 'rect',
        label: 'paypal',
        height: 45,
      },
      
      createOrder: async () => {
        try {
          return await createOrder();
        } catch (error) {
          console.error('Erreur lors de la création de la commande:', error);
          return null;
        }
      },

      onApprove: onApprove,

      onCancel: () => {
        console.log('Paiement PayPal annulé');
        if (onCancel) {
          onCancel();
        }
      },

      onError: (err: any) => {
        console.error('Erreur PayPal:', err);
        onError('Une erreur est survenue avec PayPal. Veuillez réessayer.');
      }
    }).render('#paypal-button-container');
  };

  if (loading) {
    return (
      <div className="max-w-md mx-auto bg-white p-6 rounded-lg shadow-lg">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-2 text-gray-600">Chargement de PayPal...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-md mx-auto bg-white p-6 rounded-lg shadow-lg">
      <h3 className="text-lg font-semibold mb-4">Paiement PayPal</h3>
      
      <div className="mb-6">
        <p className="text-sm text-gray-600 mb-2">Montant à payer:</p>
        <p className="text-2xl font-bold text-gray-900">{amount.toFixed(2)} €</p>
      </div>

      {processing && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500 mr-2"></div>
            <span className="text-blue-700 text-sm">Traitement du paiement en cours...</span>
          </div>
        </div>
      )}

      <div id="paypal-button-container" className="mb-4"></div>

      <div className="text-xs text-gray-500 text-center">
        <p>Paiement sécurisé par PayPal</p>
        <div className="flex justify-center items-center mt-2 space-x-2">
          <span>🔒</span>
          <span>SSL</span>
          <span>•</span>
          <span>Protection des achats PayPal</span>
        </div>
        <p className="mt-2">
          Vous serez redirigé vers PayPal pour finaliser votre paiement
        </p>
      </div>
    </div>
  );
};

export default PayPalPayment;
