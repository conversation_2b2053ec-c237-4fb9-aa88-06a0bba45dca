import React, { useState } from 'react';
import StripePayment from './StripePayment';
import PayPalPayment from './PayPalPayment';

interface PaymentInterfaceProps {
  bookingId: number;
  amount: number;
  onSuccess: (paymentData: any) => void;
  onError: (error: string) => void;
  onCancel?: () => void;
}

type PaymentMethod = 'stripe' | 'paypal';

const PaymentInterface: React.FC<PaymentInterfaceProps> = ({
  bookingId,
  amount,
  onSuccess,
  onError,
  onCancel
}) => {
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod>('stripe');
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [paymentData, setPaymentData] = useState<any>(null);

  const handlePaymentSuccess = (data: any) => {
    setPaymentSuccess(true);
    setPaymentData(data);
    onSuccess(data);
  };

  const handlePaymentError = (error: string) => {
    onError(error);
  };

  if (paymentSuccess) {
    return (
      <div className="max-w-md mx-auto bg-white p-6 rounded-lg shadow-lg text-center">
        <div className="mb-4">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
            <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
        </div>
        
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Paiement réussi !
        </h3>
        
        <p className="text-gray-600 mb-4">
          Votre paiement de {amount.toFixed(2)} € a été traité avec succès.
        </p>
        
        <div className="bg-gray-50 p-4 rounded-lg mb-4">
          <div className="text-sm text-gray-600">
            <p><strong>Montant:</strong> {amount.toFixed(2)} €</p>
            <p><strong>Méthode:</strong> {selectedMethod === 'stripe' ? 'Carte bancaire' : 'PayPal'}</p>
            {paymentData && (
              <p><strong>ID de transaction:</strong> {
                selectedMethod === 'stripe' 
                  ? paymentData.id 
                  : paymentData.capture?.id || 'N/A'
              }</p>
            )}
          </div>
        </div>
        
        <p className="text-xs text-gray-500">
          Un email de confirmation vous sera envoyé sous peu.
        </p>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      {/* Sélecteur de méthode de paiement */}
      <div className="bg-white p-6 rounded-lg shadow-lg mb-6">
        <h2 className="text-xl font-semibold mb-4">Choisissez votre méthode de paiement</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            onClick={() => setSelectedMethod('stripe')}
            className={`p-4 border-2 rounded-lg transition-all ${
              selectedMethod === 'stripe'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center justify-center mb-2">
              <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                  <path d="M4 8h16v2H4z" fill="white"/>
                </svg>
              </div>
            </div>
            <h3 className="font-medium text-gray-900">Carte bancaire</h3>
            <p className="text-sm text-gray-600 mt-1">
              Visa, Mastercard, American Express
            </p>
            <div className="flex justify-center mt-2 space-x-2">
              <span className="text-xs bg-gray-100 px-2 py-1 rounded">Visa</span>
              <span className="text-xs bg-gray-100 px-2 py-1 rounded">MC</span>
              <span className="text-xs bg-gray-100 px-2 py-1 rounded">Amex</span>
            </div>
          </button>

          <button
            onClick={() => setSelectedMethod('paypal')}
            className={`p-4 border-2 rounded-lg transition-all ${
              selectedMethod === 'paypal'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center justify-center mb-2">
              <div className="w-8 h-8 bg-blue-500 rounded flex items-center justify-center">
                <span className="text-white font-bold text-sm">PP</span>
              </div>
            </div>
            <h3 className="font-medium text-gray-900">PayPal</h3>
            <p className="text-sm text-gray-600 mt-1">
              Compte PayPal ou carte via PayPal
            </p>
            <div className="flex justify-center mt-2">
              <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                Protection des achats
              </span>
            </div>
          </button>
        </div>
      </div>

      {/* Interface de paiement */}
      <div className="transition-all duration-300">
        {selectedMethod === 'stripe' ? (
          <StripePayment
            bookingId={bookingId}
            amount={amount}
            onSuccess={handlePaymentSuccess}
            onError={handlePaymentError}
          />
        ) : (
          <PayPalPayment
            bookingId={bookingId}
            amount={amount}
            onSuccess={handlePaymentSuccess}
            onError={handlePaymentError}
            onCancel={onCancel}
          />
        )}
      </div>

      {/* Informations de sécurité */}
      <div className="mt-6 bg-gray-50 p-4 rounded-lg">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
          </div>
          <div className="ml-3">
            <h4 className="text-sm font-medium text-gray-900">Paiement sécurisé</h4>
            <div className="mt-1 text-sm text-gray-600">
              <ul className="list-disc list-inside space-y-1">
                <li>Toutes les transactions sont chiffrées SSL</li>
                <li>Vos données bancaires ne sont jamais stockées sur nos serveurs</li>
                <li>Conformité PCI DSS pour la sécurité des paiements</li>
                <li>Protection contre la fraude intégrée</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Politique d'annulation */}
      <div className="mt-4 text-xs text-gray-500 text-center">
        <p>
          En procédant au paiement, vous acceptez nos{' '}
          <a href="/terms" className="text-blue-600 hover:underline">
            conditions d'utilisation
          </a>{' '}
          et notre{' '}
          <a href="/privacy" className="text-blue-600 hover:underline">
            politique de confidentialité
          </a>.
        </p>
        <p className="mt-1">
          Politique d'annulation: Annulation gratuite jusqu'à 24h avant le rendez-vous.
        </p>
      </div>
    </div>
  );
};

export default PaymentInterface;
