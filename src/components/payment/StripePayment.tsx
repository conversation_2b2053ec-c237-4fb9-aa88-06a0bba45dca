import React, { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { BASE_URL, API_ENDPOINTS } from '../../context/url';
import { useAuth } from '../../context/AuthContext';

// Charger Stripe avec votre clé publique
const stripePromise = loadStripe(process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY || '');

interface StripePaymentProps {
  bookingId: number;
  amount: number;
  onSuccess: (paymentIntent: any) => void;
  onError: (error: string) => void;
}

interface PaymentMethod {
  id: number;
  provider_id: string;
  type: string;
  last_four?: string;
  brand?: string;
  exp_month?: string;
  exp_year?: string;
  is_default: boolean;
}

const CheckoutForm: React.FC<StripePaymentProps> = ({ 
  bookingId, 
  amount, 
  onSuccess, 
  onError 
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const { token } = useAuth();
  
  const [processing, setProcessing] = useState(false);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('');
  const [saveCard, setSaveCard] = useState(false);
  const [useNewCard, setUseNewCard] = useState(true);
  const [clientSecret, setClientSecret] = useState<string>('');

  useEffect(() => {
    fetchPaymentMethods();
  }, []);

  const fetchPaymentMethods = async () => {
    try {
      const response = await fetch(`${BASE_URL}${API_ENDPOINTS.PAYMENT_METHODS}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setPaymentMethods(data.payment_methods.filter((pm: PaymentMethod) => pm.type === 'card'));
      }
    } catch (error) {
      console.error('Erreur lors du chargement des méthodes de paiement:', error);
    }
  };

  const createPaymentIntent = async (paymentMethodId?: string) => {
    try {
      const response = await fetch(`${BASE_URL}${API_ENDPOINTS.STRIPE_PAYMENT_INTENT}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          booking_id: bookingId,
          payment_method_id: paymentMethodId,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        return data;
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erreur lors de la création du paiement');
      }
    } catch (error) {
      throw error;
    }
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setProcessing(true);

    try {
      if (useNewCard) {
        // Utiliser une nouvelle carte
        const cardElement = elements.getElement(CardElement);
        if (!cardElement) {
          throw new Error('Élément de carte non trouvé');
        }

        // Créer le Payment Intent
        const { client_secret } = await createPaymentIntent();

        // Confirmer le paiement
        const { error, paymentIntent } = await stripe.confirmCardPayment(client_secret, {
          payment_method: {
            card: cardElement,
          }
        });

        if (error) {
          throw new Error(error.message || 'Erreur de paiement');
        }

        // Sauvegarder la carte si demandé
        if (saveCard && paymentIntent.payment_method) {
          await savePaymentMethod(paymentIntent.payment_method as string);
        }

        onSuccess(paymentIntent);
      } else {
        // Utiliser une carte sauvegardée
        if (!selectedPaymentMethod) {
          throw new Error('Veuillez sélectionner une méthode de paiement');
        }

        const { client_secret } = await createPaymentIntent(selectedPaymentMethod);

        const { error, paymentIntent } = await stripe.confirmCardPayment(client_secret);

        if (error) {
          throw new Error(error.message || 'Erreur de paiement');
        }

        onSuccess(paymentIntent);
      }
    } catch (error) {
      onError(error instanceof Error ? error.message : 'Erreur inconnue');
    } finally {
      setProcessing(false);
    }
  };

  const savePaymentMethod = async (paymentMethodId: string) => {
    try {
      await fetch(`${BASE_URL}${API_ENDPOINTS.PAYMENT_METHODS}/stripe`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          payment_method_id: paymentMethodId,
          make_default: paymentMethods.length === 0,
        }),
      });
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de la méthode de paiement:', error);
    }
  };

  const cardElementOptions = {
    style: {
      base: {
        fontSize: '16px',
        color: '#424770',
        '::placeholder': {
          color: '#aab7c4',
        },
      },
      invalid: {
        color: '#9e2146',
      },
    },
  };

  return (
    <div className="max-w-md mx-auto bg-white p-6 rounded-lg shadow-lg">
      <h3 className="text-lg font-semibold mb-4">Paiement par carte</h3>
      
      <div className="mb-4">
        <p className="text-sm text-gray-600 mb-2">Montant à payer:</p>
        <p className="text-2xl font-bold text-gray-900">{amount.toFixed(2)} €</p>
      </div>

      <form onSubmit={handleSubmit}>
        {paymentMethods.length > 0 && (
          <div className="mb-4">
            <div className="flex space-x-4 mb-3">
              <label className="flex items-center">
                <input
                  type="radio"
                  checked={!useNewCard}
                  onChange={() => setUseNewCard(false)}
                  className="mr-2"
                />
                Carte sauvegardée
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  checked={useNewCard}
                  onChange={() => setUseNewCard(true)}
                  className="mr-2"
                />
                Nouvelle carte
              </label>
            </div>

            {!useNewCard && (
              <select
                value={selectedPaymentMethod}
                onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                <option value="">Sélectionner une carte</option>
                {paymentMethods.map((method) => (
                  <option key={method.id} value={method.provider_id}>
                    {method.brand?.toUpperCase()} •••• {method.last_four} 
                    ({method.exp_month}/{method.exp_year})
                    {method.is_default && ' (Par défaut)'}
                  </option>
                ))}
              </select>
            )}
          </div>
        )}

        {useNewCard && (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Informations de la carte
            </label>
            <div className="p-3 border border-gray-300 rounded-lg focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-transparent">
              <CardElement options={cardElementOptions} />
            </div>
            
            <label className="flex items-center mt-3">
              <input
                type="checkbox"
                checked={saveCard}
                onChange={(e) => setSaveCard(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm text-gray-600">
                Sauvegarder cette carte pour les prochains paiements
              </span>
            </label>
          </div>
        )}

        <button
          type="submit"
          disabled={!stripe || processing}
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {processing ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              Traitement en cours...
            </div>
          ) : (
            `Payer ${amount.toFixed(2)} €`
          )}
        </button>
      </form>

      <div className="mt-4 text-xs text-gray-500 text-center">
        <p>Paiement sécurisé par Stripe</p>
        <div className="flex justify-center items-center mt-2 space-x-2">
          <span>🔒</span>
          <span>SSL</span>
          <span>•</span>
          <span>Visa</span>
          <span>•</span>
          <span>Mastercard</span>
          <span>•</span>
          <span>American Express</span>
        </div>
      </div>
    </div>
  );
};

const StripePayment: React.FC<StripePaymentProps> = (props) => {
  return (
    <Elements stripe={stripePromise}>
      <CheckoutForm {...props} />
    </Elements>
  );
};

export default StripePayment;
