import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>, Unlock, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>riangle } from 'lucide-react';
import { useAuth } from '../context/AuthContext';

interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
}

interface RolePermissions {
  [key: string]: {
    name: string;
    color: string;
    permissions: string[];
    features: string[];
  };
}

const rolePermissions: RolePermissions = {
  admin: {
    name: 'Administrateur',
    color: 'bg-purple-100 text-purple-800',
    permissions: [
      'salon.manage.all',
      'user.manage.all',
      'booking.manage.all',
      'service.manage.all',
      'review.moderate.all',
      'analytics.view.global',
      'role.assign',
      'system.settings'
    ],
    features: [
      'Gestion complète de tous les salons',
      'Gestion de tous les utilisateurs',
      'Modération des avis',
      'Accès aux statistiques globales',
      'Configuration système',
      'Attribution des rôles'
    ]
  },
  owner: {
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    color: 'bg-blue-100 text-blue-800',
    permissions: [
      'salon.manage.own',
      'manager.manage.own',
      'hairdresser.manage.own',
      'booking.view.own',
      'analytics.view.own',
      'service.manage.own'
    ],
    features: [
      'Gestion de ses propres salons',
      'Gestion de ses managers',
      'Gestion de ses coiffeurs',
      'Consultation des réservations',
      'Accès aux statistiques de ses salons',
      'Gestion des services de ses salons'
    ]
  },
  manager: {
    name: 'Gérant',
    color: 'bg-green-100 text-green-800',
    permissions: [
      'hairdresser.manage.salon',
      'booking.manage.salon',
      'service.manage.salon',
      'schedule.manage.salon',
      'analytics.view.salon'
    ],
    features: [
      'Gestion des coiffeurs du salon',
      'Gestion des réservations du salon',
      'Gestion des services du salon',
      'Gestion des horaires du salon',
      'Accès aux statistiques du salon'
    ]
  },
  hairdresser: {
    name: 'Coiffeur',
    color: 'bg-orange-100 text-orange-800',
    permissions: [
      'schedule.manage.own',
      'booking.view.own',
      'service.manage.own',
      'profile.manage.own',
      'review.view.own'
    ],
    features: [
      'Gestion de son planning',
      'Consultation de ses réservations',
      'Gestion de ses services',
      'Gestion de son profil',
      'Consultation des avis clients'
    ]
  },
  client: {
    name: 'Client',
    color: 'bg-gray-100 text-gray-800',
    permissions: [
      'booking.create.own',
      'booking.view.own',
      'review.create.own',
      'profile.manage.own',
      'salon.view.all'
    ],
    features: [
      'Réservation de services',
      'Consultation de son historique',
      'Donner des avis',
      'Gestion de son profil',
      'Consultation des salons'
    ]
  }
};

export default function PermissionManager() {
  const { user } = useAuth();
  const [selectedRole, setSelectedRole] = useState<string>('admin');
  const [activeTab, setActiveTab] = useState<'permissions' | 'features'>('permissions');

  if (!user || user.role !== 'admin') {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Accès restreint</h3>
          <p className="text-gray-500">Vous devez être administrateur pour accéder à cette page.</p>
        </div>
      </div>
    );
  }

  const currentRole = rolePermissions[selectedRole];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Gestionnaire de Permissions</h2>
        <div className="flex items-center space-x-2">
          <Shield className="h-5 w-5 text-gray-500" />
          <span className="text-sm text-gray-500">Gestion des accès</span>
        </div>
      </div>

      {/* Sélecteur de rôle */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Sélectionner un rôle</h3>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
          {Object.keys(rolePermissions).map(role => (
            <button
              key={role}
              onClick={() => setSelectedRole(role)}
              className={`p-3 rounded-lg border-2 transition-all ${
                selectedRole === role
                  ? 'border-indigo-500 bg-indigo-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="text-center">
                <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mb-2 ${rolePermissions[role].color}`}>
                  {rolePermissions[role].name}
                </div>
                <div className="text-xs text-gray-500">
                  {rolePermissions[role].permissions.length} permissions
                </div>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Détails du rôle sélectionné */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${currentRole.color}`}>
                {currentRole.name}
              </span>
              <div className="flex space-x-2">
                <button
                  onClick={() => setActiveTab('permissions')}
                  className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                    activeTab === 'permissions'
                      ? 'bg-indigo-100 text-indigo-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Permissions
                </button>
                <button
                  onClick={() => setActiveTab('features')}
                  className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                    activeTab === 'features'
                      ? 'bg-indigo-100 text-indigo-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Fonctionnalités
                </button>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Lock className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-500">
                {currentRole.permissions.length} permissions actives
              </span>
            </div>
          </div>
        </div>

        <div className="p-6">
          {activeTab === 'permissions' ? (
            <div className="space-y-4">
              <h4 className="text-lg font-medium text-gray-900 mb-4">Permissions détaillées</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {currentRole.permissions.map((permission, index) => (
                  <div key={index} className="flex items-center p-3 bg-gray-50 rounded-lg">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <Unlock className="h-4 w-4 text-green-600" />
                      </div>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">{permission}</p>
                      <p className="text-xs text-gray-500">
                        Permission accordée pour ce rôle
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <h4 className="text-lg font-medium text-gray-900 mb-4">Fonctionnalités disponibles</h4>
              <div className="space-y-3">
                {currentRole.features.map((feature, index) => (
                  <div key={index} className="flex items-start p-4 bg-blue-50 rounded-lg">
                    <div className="flex-shrink-0">
                      <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                        <Settings className="h-3 w-3 text-blue-600" />
                      </div>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">{feature}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Informations de sécurité */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-start">
          <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">
              Informations de sécurité
            </h3>
            <div className="mt-2 text-sm text-yellow-700">
              <p>
                • Les permissions sont appliquées automatiquement selon le rôle de l'utilisateur
              </p>
              <p>
                • Les modifications de permissions nécessitent une validation administrateur
              </p>
              <p>
                • Les accès sont vérifiés côté serveur pour garantir la sécurité
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Statistiques des rôles */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {Object.entries(rolePermissions).map(([role, data]) => (
          <div key={role} className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between mb-4">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${data.color}`}>
                {data.name}
              </span>
              <UserCheck className="h-4 w-4 text-gray-400" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Permissions:</span>
                <span className="font-medium">{data.permissions.length}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Fonctionnalités:</span>
                <span className="font-medium">{data.features.length}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
} 