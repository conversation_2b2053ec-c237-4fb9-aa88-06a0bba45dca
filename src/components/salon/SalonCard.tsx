import React from 'react';
import { Link } from 'react-router-dom';
import { Star, MapPin, Clock } from 'lucide-react';
import type { Salon } from '../../types';

interface SalonCardProps {
  salon: Salon;
}

export default function SalonCard({ salon }: SalonCardProps) {
  const today = new Date().toLocaleDateString('fr-FR', { weekday: 'long' }).toLowerCase();

  const todayHours = salon.hours[today];

  return (
    <Link to={`/salons/${salon.id}`} className="block">
      <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
        <img
          className="h-48 w-full object-cover"
          src={`https://source.unsplash.com/800x600/?salon,hairdresser&${salon.id}`}
          alt={salon.name}
        />
        <div className="p-6">
          <div className="flex justify-between items-start">
            <h3 className="text-xl font-semibold text-gray-900">{salon.name}</h3>
            <div className="flex items-center">
              <Star className="h-5 w-5 text-yellow-400" />
              <span className="ml-1 text-sm text-gray-600">{salon.rating.toFixed(1)}</span>
            </div>
          </div>
          <div className="mt-2 flex items-center text-sm text-gray-500">
            <MapPin className="h-4 w-4 mr-1" />
            {salon.address}
          </div>
          <div className="mt-2 flex items-center text-sm text-gray-500">
            <Clock className="h-4 w-4 mr-1" />
            {todayHours.open === 'closed' 
              ? 'Fermé'
              : `${todayHours.open} - ${todayHours.close}`
            }
          </div>
          <div className="mt-4">
            <button className="w-full bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors">
              Réserver
            </button>
          </div>
        </div>
      </div>
    </Link>
  );
}