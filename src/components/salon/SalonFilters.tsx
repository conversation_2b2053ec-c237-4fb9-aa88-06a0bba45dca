import React from 'react';
import { Search, Filter } from 'lucide-react';

interface SalonFiltersProps {
  onSearch: (term: string) => void;
  onFilterChange: (filters: any) => void;
}

export default function SalonFilters({ onSearch, onFilterChange }: SalonFiltersProps) {
  return (
    <div className="bg-white p-4 rounded-lg shadow-md">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            className="pl-10 w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            placeholder="Rechercher un salon..."
            onChange={(e) => onSearch(e.target.value)}
          />
        </div>
        
        <div>
          <select
            className="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            onChange={(e) => onFilterChange({ rating: e.target.value })}
          >
            <option value="">Note : Toutes</option>
            <option value="4">4+ étoiles</option>
            <option value="3">3+ étoiles</option>
          </select>
        </div>

        <div>
          <select
            className="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            onChange={(e) => onFilterChange({ service: e.target.value })}
          >
            <option value="">Tous les services</option>
            <option value="coupe-femme">Coupe femme</option>
            <option value="coupe-homme">Coupe homme</option>
            <option value="coloration">Coloration</option>
          </select>
        </div>

        <div>
          <input
            type="date"
            className="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            onChange={(e) => onFilterChange({ date: e.target.value })}
          />
        </div>
      </div>
    </div>
  );
}