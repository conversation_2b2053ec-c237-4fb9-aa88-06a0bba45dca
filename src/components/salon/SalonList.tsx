import React from 'react';
import SalonCard from './SalonCard';
import type { Salon } from '../../types';

interface SalonListProps {
  salons: Salon[];
  isLoading: boolean;
}

export default function SalonList({ salons, isLoading }: SalonListProps) {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="bg-gray-200 h-48 rounded-t-lg"></div>
            <div className="bg-white p-6 rounded-b-lg shadow-md space-y-3">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (salons.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Aucun salon trouvé</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {salons.map((salon) => (
        <SalonCard key={salon.id} salon={salon} />
      ))}
    </div>
  );
}