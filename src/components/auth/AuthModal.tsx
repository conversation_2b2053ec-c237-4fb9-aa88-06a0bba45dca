import React, { useState } from "react";
import { X } from "lucide-react";
import { useAuth } from "../../context/AuthContext";
import type { User } from "../../types";
import toast from "react-hot-toast";
import { Loader2 } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { BASE_URL } from "../../context/url";

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialMode?: "login" | "register";
}

type RegisterFormData = {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  password_confirmation: string;
  phone: string;
  gender: "male" | "female" | "other";
};

export default function AuthModal({
  isOpen,
  onClose,
  initialMode = "login",
}: AuthModalProps) {
  const navigate = useNavigate();
  const [mode, setMode] = useState<"login" | "register" | "forgotPassword">(
    initialMode
  );
  const [formData, setFormData] = useState<RegisterFormData>({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    password_confirmation: "",
    phone: "",
    gender: "other",
  });
  const [forgotPasswordEmail, setForgotPasswordEmail] = useState("");
  const [error, setError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { login, register } = useAuth();

  const displayValidationErrors = (errors: Record<string, string[]>) => {
    Object.entries(errors).forEach(([field, messages]) => {
      messages.forEach((message) => {
        toast.error(message, {
          position: "top-right",
          duration: 4000,
        });
      });
    });
  };

  if (!isOpen) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsSubmitting(true);

    try {
      if (mode === "login") {
        await login(formData.email, formData.password);
        toast.success("Connexion réussie !");
        onClose();
      } else if (mode === "register") {
        const userData: Partial<User> & {
          password: string;
          password_confirmation: string;
        } = {
          ...formData,
          gender: formData.gender,
        };
        await register(userData);
        navigate("/confirm-email", { state: { email: formData.email } });
      }
    } catch (err: any) {
      if (err.isApiError) {
        if (err.status === 422) {
          const errors = err.data?.errors;
          if (errors) {
            displayValidationErrors(errors);
          } else {
            toast.error(err.data?.message || "Erreur de validation", {
              position: "top-right",
              duration: 4000,
            });
          }
        } else if (err.status === 401 || err.status === 403) {
          if (err.data?.errors) {
            const errorMessages = Object.values(
              err.data.errors
            ).flat() as string[];
            errorMessages.forEach((message) => {
              toast.error(message, {
                position: "top-right",
                duration: 4000,
              });
            });
          } else {
            toast.error(err.data?.message || "Erreur d'authentification", {
              position: "top-right",
              duration: 4000,
            });
          }
        }
      } else {
        toast.error("Une erreur inattendue est survenue", {
          position: "top-right",
          duration: 4000,
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleForgotPassword = async () => {
    if (!forgotPasswordEmail) {
      toast.error("Veuillez entrer votre email");
      return;
    }

    try {
      setIsSubmitting(true);
      const response = await fetch(`${BASE_URL}/resend-verification`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email: forgotPasswordEmail }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Erreur lors de l'envoi du code");
      }

      toast.success("Un code de vérification a été envoyé à votre email");
      navigate("/confirm-email-forgot-password", {
        state: { email: forgotPasswordEmail },
      });
    } catch (error: any) {
      toast.error(error.message || "Une erreur est survenue");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    if (mode === "forgotPassword" && name === "email") {
      setForgotPasswordEmail(value);
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center px-4">
        <div
          className="fixed inset-0 bg-gray-500 bg-opacity-75"
          onClick={onClose}
        />

        <div className="relative w-full max-w-md transform overflow-hidden rounded-lg bg-white shadow-xl transition-all">
          <div className="absolute right-0 top-0 pr-4 pt-4">
            <button
              onClick={onClose}
              className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium leading-6 text-gray-900">
              {mode === "login"
                ? "Connexion"
                : mode === "register"
                ? "Inscription"
                : "Mot de passe oublié"}
            </h3>

            {error && <div className="mt-2 text-sm text-red-600">{error}</div>}

            {mode === "forgotPassword" ? (
              <div className="mt-4 space-y-4">
                <div>
                  <label
                    htmlFor="forgot-email"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Email
                  </label>
                  <input
                    type="email"
                    name="email"
                    id="forgot-email"
                    value={forgotPasswordEmail}
                    onChange={handleChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  />
                </div>
                <button
                  type="button"
                  onClick={handleForgotPassword}
                  disabled={isSubmitting}
                  className={`w-full rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
                    isSubmitting ? "opacity-75 cursor-not-allowed" : ""
                  }`}
                >
                  {isSubmitting ? (
                    <span className="flex items-center justify-center">
                      <Loader2 className="animate-spin h-4 w-4 mr-2" />
                      Envoi en cours...
                    </span>
                  ) : (
                    "Envoyer le lien de réinitialisation"
                  )}
                </button>
                <div className="text-center text-sm mt-4">
                  <button
                    onClick={() => setMode("login")}
                    className="text-indigo-600 hover:text-indigo-500"
                  >
                    Retour à la connexion
                  </button>
                </div>
              </div>
            ) : (
              <>
                <form onSubmit={handleSubmit} className="mt-4 space-y-4">
                  {mode === "register" && (
                    <>
                      <div>
                        <label
                          htmlFor="firstName"
                          className="block text-sm font-medium text-gray-700"
                        >
                          Prénom
                        </label>
                        <input
                          type="text"
                          name="firstName"
                          id="firstName"
                          value={formData.firstName}
                          onChange={handleChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        />
                      </div>
                      <div>
                        <label
                          htmlFor="lastName"
                          className="block text-sm font-medium text-gray-700"
                        >
                          Nom
                        </label>
                        <input
                          type="text"
                          name="lastName"
                          id="lastName"
                          value={formData.lastName}
                          onChange={handleChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        />
                      </div>
                      <div>
                        <label
                          htmlFor="phone"
                          className="block text-sm font-medium text-gray-700"
                        >
                          Téléphone
                        </label>
                        <input
                          type="tel"
                          name="phone"
                          id="phone"
                          value={formData.phone}
                          onChange={handleChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        />
                      </div>
                      <div>
                        <label
                          htmlFor="gender"
                          className="block text-sm font-medium text-gray-700"
                        >
                          Genre
                        </label>
                        <select
                          name="gender"
                          id="gender"
                          value={formData.gender}
                          onChange={handleChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        >
                          <option value="male">Homme</option>
                          <option value="female">Femme</option>
                          <option value="other">Autre</option>
                        </select>
                      </div>
                    </>
                  )}
                  <div>
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Email
                    </label>
                    <input
                      type="email"
                      name="email"
                      id="email"
                      value={formData.email}
                      onChange={handleChange}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="password"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Mot de passe
                    </label>
                    <input
                      type="password"
                      name="password"
                      id="password"
                      value={formData.password}
                      onChange={handleChange}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    />
                  </div>
                  {mode === "register" && (
                    <div>
                      <label
                        htmlFor="password_confirmation"
                        className="block text-sm font-medium text-gray-700"
                      >
                        Confirmation du mot de passe
                      </label>
                      <input
                        type="password"
                        name="password_confirmation"
                        id="password_confirmation"
                        value={formData.password_confirmation}
                        onChange={handleChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      />
                    </div>
                  )}
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className={`w-full rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
                      isSubmitting ? "opacity-75 cursor-not-allowed" : ""
                    }`}
                  >
                    {isSubmitting ? (
                      <span className="flex items-center justify-center">
                        <Loader2 className="animate-spin h-4 w-4 mr-2" />
                        {mode === "login" ? "Connexion..." : "Inscription..."}
                      </span>
                    ) : mode === "login" ? (
                      "Se connecter"
                    ) : (
                      "S'inscrire"
                    )}
                  </button>
                </form>
                <div className="mt-4 text-center text-sm">
                  {mode === "login" ? (
                    <>
                      <p>
                        Pas encore de compte ?{" "}
                        <button
                          onClick={() => setMode("register")}
                          className="text-indigo-600 hover:text-indigo-500"
                        >
                          S'inscrire
                        </button>
                      </p>
                      <p className="mt-2">
                        <button
                          onClick={() => setMode("forgotPassword")}
                          className="text-indigo-600 hover:text-indigo-500"
                        >
                          Mot de passe oublié ?
                        </button>
                      </p>
                    </>
                  ) : (
                    <p>
                      Déjà un compte ?{" "}
                      <button
                        onClick={() => setMode("login")}
                        className="text-indigo-600 hover:text-indigo-500"
                      >
                        Se connecter
                      </button>
                    </p>
                  )}
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
