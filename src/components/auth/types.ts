// Définition du type User
export interface User {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  gender: string;
  photoUrl: string | null;
  role: string;
  created_at: string;
  updated_at: string;
}


// Définition pour une réponse d'API contenant un utilisateur
export interface ApiResponse {
  data: User;
}

// Définition pour AuthFormData utilisé dans les formulaires d'authentification
export interface AuthFormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phone: string;
  gender: 'male' | 'female' | 'other';
}

export type AuthMode = 'login' | 'register';

export interface Booking {
  id: string | null;
  clientId: string | number; // Accepte à la fois string et number
  salonId: number;
  hairdresserId: number;
  services: Array<{
    id: string | null;
    name: string;
    duration: number;
    price: string;
    description: string;
  }>;
  dateTime: string;
  status: 'confirmed' | 'cancelled' | 'pending';
  salon?: {
    id: string | null;
    name: string;
    address: string;
    hours: {
      open: string;
      close: string;
    };
    contact: {
      phone: string;
      email: string;
    };
    rating: string;
  };
}

export interface Salon {
  id: string;
  name: string;
  address: string;
  hours: {
    [day: string]: { open: string; close: string };
  };
  services: any[]; // Placeholder
  hairdressers: any[]; // Placeholder
  contact: {
    phone: string;
    email: string;
  };
  rating: number;
  reviews: any[]; // Placeholder
  images: string[]; // Liste des URLs d'images
}


