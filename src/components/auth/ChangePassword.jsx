import React, { useState, useEffect } from "react";
import { Loader2 } from "lucide-react";
import { useNavigate, useLocation } from "react-router-dom";
import toast from "react-hot-toast";
import { BASE_URL } from "../../context/url";
import { useAuth } from "../../context/AuthContext";

export default function ChangePassword() {
  const location = useLocation();
  const navigate = useNavigate();
  const { resetToken, setResetToken, clearResetToken } = useAuth();

  // Déclaration des états en un seul endroit
  const [email, setEmail] = useState("");
  const [formData, setFormData] = useState({
    password: "",
    password_confirmation: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const tokenFromState = location.state?.token;
    const emailFromState = location.state?.email;
    const emailFromStorage = localStorage.getItem("resetEmail");

    if (!emailFromState && !emailFromStorage) {
      toast.error("Informations de réinitialisation manquantes");
      navigate("/forgot-password");
      return;
    }

    setEmail(emailFromState || emailFromStorage);

    // Stocker le code de vérification (6 chiffres)
    if (tokenFromState) {
      setResetToken(tokenFromState);
      localStorage.setItem("resetToken", tokenFromState);
    }
  }, [location.state, navigate, setResetToken]);
  // Debug des données importantes
  useEffect(() => {
    console.log("ChangePassword data:", {
      email,
      resetToken,
      formData,
    });
  }, [email, resetToken, formData]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!email || !resetToken) {
      toast.error("Données de session incomplètes");
      return;
    }

    if (formData.password !== formData.password_confirmation) {
      toast.error("Les mots de passe ne correspondent pas");
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch(`${BASE_URL}/reset-password`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({
          email,
          token: resetToken, // Le code à 6 chiffres
          password: formData.password,
          password_confirmation: formData.password_confirmation,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Erreur lors de la réinitialisation");
      }

      toast.success("Mot de passe réinitialisé avec succès !");
      localStorage.removeItem("resetEmail");
      localStorage.removeItem("resetToken");
      clearResetToken();
      navigate("/");
    } catch (error) {
      console.error("Reset error:", error);
      toast.error(error.message || "Une erreur est survenue");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Garde-fou si les données critiques manquent
  if (!email || !resetToken) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md text-center">
          <h1 className="text-xl font-bold text-red-500">Session invalide</h1>
          <p className="mt-2">
            Veuillez recommencer le processus de réinitialisation
          </p>
          <button
            onClick={() => navigate("/forgot-password")}
            className="mt-4 text-indigo-600 hover:text-indigo-500"
          >
            Retour à la page de réinitialisation
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold">
            Réinitialisation du mot de passe
          </h2>
          <p className="text-sm text-gray-500 mt-2">
            Pour l'email : <span className="font-medium">{email}</span>
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label
              htmlFor="password"
              className="block text-sm font-medium text-gray-700"
            >
              Nouveau mot de passe
            </label>
            <input
              type="password"
              name="password"
              id="password"
              value={formData.password}
              onChange={handleChange}
              required
              minLength={8}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>

          <div>
            <label
              htmlFor="password_confirmation"
              className="block text-sm font-medium text-gray-700"
            >
              Confirmation du mot de passe
            </label>
            <input
              type="password"
              name="password_confirmation"
              id="password_confirmation"
              value={formData.password_confirmation}
              onChange={handleChange}
              required
              minLength={8}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>

          <button
            type="submit"
            disabled={isSubmitting}
            className={`w-full rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
              isSubmitting ? "opacity-75 cursor-not-allowed" : ""
            }`}
          >
            {isSubmitting ? (
              <span className="flex items-center justify-center">
                <Loader2 className="animate-spin h-4 w-4 mr-2" />
                En cours...
              </span>
            ) : (
              "Réinitialiser le mot de passe"
            )}
          </button>
        </form>

        <div className="mt-4 text-center text-sm">
          <button
            onClick={() => navigate("/login")}
            className="text-indigo-600 hover:text-indigo-500"
          >
            Retour à la connexion
          </button>
        </div>
      </div>
    </div>
  );
}
