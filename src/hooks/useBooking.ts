import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { bookingService } from '../services/booking.service';
import type { Service, Hairdresser } from '../types';
import type { BookingFormData, BookingError } from '../api/booking';

interface UseBookingProps {
  onComplete?: () => void;
  salonId: string;
}

export function useBooking({ onComplete, salonId }: UseBookingProps) {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState<'services' | 'hairdresser' | 'datetime' | 'summary'>('services');
  const [selectedServices, setSelectedServices] = useState<Service[]>([]);
  const [selectedHairdresser, setSelectedHairdresser] = useState<Hairdresser | null>(null);
  const [selectedDateTime, setSelectedDateTime] = useState<string | null>(null);
  const [error, setError] = useState<BookingError | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [direction, setDirection] = useState<string | null>(null);

  const validateStep = (step: string): boolean => {
    switch (step) {
      case 'services':
        return selectedServices.length > 0;
      case 'hairdresser':
        return selectedHairdresser !== null;
      case 'datetime':
        return selectedDateTime !== null;
      default:
        return true;
    }
  };

  const goToNextStep = async () => {
    if (!validateStep(currentStep)) {
      setError({ message: 'Veuillez remplir tous les champs requis' });
      return;
    }

    if (currentStep === 'datetime' && selectedDateTime && selectedHairdresser) {
      try {
        setIsLoading(true);
        const isAvailable = await bookingService.checkAvailability(
          selectedHairdresser.id,
          selectedDateTime
        );
        if (!isAvailable) {
          setError({ message: 'Ce créneau n\'est plus disponible' });
          return;
        }
      } catch (err) {
        setError({ message: 'Erreur lors de la vérification de la disponibilité' });
        return;
      } finally {
        setIsLoading(false);
      }
    }

    const steps: typeof currentStep[] = ['services', 'hairdresser', 'datetime', 'summary'];
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1]);
      setError(null);
    }
  };

  const goToPreviousStep = () => {
    const steps: typeof currentStep[] = ['services', 'hairdresser', 'datetime', 'summary'];
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1]);
      setError(null);
    }
  };

  const confirmBooking = async () => {
    if (!selectedHairdresser || !selectedDateTime || selectedServices.length === 0) {
      setError({ message: 'Informations de réservation incomplètes' });
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const bookingData: BookingFormData = {
        salonId,
        hairdresserId: selectedHairdresser.id,
        services: selectedServices.map(s => s.id),
        dateTime: selectedDateTime,
      };

      await bookingService.createBooking(bookingData);
      onComplete?.();
      navigate('/profile');
    } catch (err) {
      setError({ message: 'Erreur lors de la création de la réservation' });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    currentStep,
    selectedServices,
    setSelectedServices,
    selectedHairdresser,
    setSelectedHairdresser,
    selectedDateTime,
    setSelectedDateTime,
    error,
    isLoading,
    goToNextStep,
    goToPreviousStep,
    confirmBooking,
    direction,
  };
}