import { useState, useEffect } from 'react';
import { staffService } from '../services/staff.service';
import type { Hairdresser } from '../types';

export function useStaff() {
  const [staff, setStaff] = useState<Hairdresser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchStaff();
  }, []);

  const fetchStaff = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await staffService.getAllStaff();
      setStaff(data);
    } catch (err) {
      setError('Erreur lors du chargement du personnel');
      console.error('Staff error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const addStaffMember = async (staffData: Partial<Hairdresser>) => {
    try {
      setError(null);
      const newStaff = await staffService.createStaffMember(staffData);
      setStaff([...staff, newStaff]);
      return newStaff;
    } catch (err) {
      setError('Erreur lors de l\'ajout du membre du personnel');
      throw err;
    }
  };

  const updateStaffMember = async (id: string, staffData: Partial<Hairdresser>) => {
    try {
      setError(null);
      const updatedStaff = await staffService.updateStaffMember(id, staffData);
      setStaff(staff.map(s => s.id === id ? updatedStaff : s));
      return updatedStaff;
    } catch (err) {
      setError('Erreur lors de la mise à jour du membre du personnel');
      throw err;
    }
  };

  const deleteStaffMember = async (id: string) => {
    try {
      setError(null);
      await staffService.deleteStaffMember(id);
      setStaff(staff.filter(s => s.id !== id));
    } catch (err) {
      setError('Erreur lors de la suppression du membre du personnel');
      throw err;
    }
  };

  return {
    staff,
    isLoading,
    error,
    addStaffMember,
    updateStaffMember,
    deleteStaffMember,
    refreshStaff: fetchStaff,
  };
}