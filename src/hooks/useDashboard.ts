import { useState, useEffect } from 'react';
import { dashboardService } from '../services/dashboard.service';
import { useAuth } from '../context/AuthContext';

export function useDashboard() {
  const [stats, setStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  useEffect(() => {
    fetchDashboardStats();
  }, [user?.role]);

  const fetchDashboardStats = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      setError(null);

      let data;
      switch (user.role) {
        case 'admin':
          data = await dashboardService.getAdminStats();
          break;
        case 'owner':
          data = await dashboardService.getOwnerStats();
          break;
        case 'manager':
          data = await dashboardService.getManagerStats();
          break;
        case 'hairdresser':
          data = await dashboardService.getHairdresserStats();
          break;
        case 'client':
          data = await dashboardService.getClientStats();
          break;
        default:
          throw new Error('Rôle utilisateur non valide');
      }

      setStats(data);
    } catch (err) {
      setError('Erreur lors du chargement des statistiques');
      console.error('Dashboard error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshStats = () => {
    fetchDashboardStats();
  };

  return {
    stats,
    isLoading,
    error,
    refreshStats,
  };
}