<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\SalonController;
use App\Http\Controllers\API\BookingServiceController;
use App\Http\Controllers\Auth\VerificationController;
/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

Route::get('/salons-list', [SalonController::class, 'salonList']);
Route::get('/salons-details/{id}', [SalonController::class, 'showSalonDetails']);
Route::get('/show-all-salon-details/{idSalon}', [SalonController::class, 'showSalonWithServicesAndOtherDetails']);
Route::get('/salons/search', [SalonController::class, 'search']);
Route::get('/services', [App\Http\Controllers\API\ServiceController::class, 'index']);
Route::get('/salons/{salonId}/hairdressers-by-services', [SalonController::class, 'getHairdressersByServices']);


// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/forgot-password', [AuthController::class, 'forgotPassword']);
Route::post('/reset-password', [AuthController::class, 'resetPassword']);
Route::get('/email/verify/{id}', [AuthController::class, 'verify'])->name('verification.verify');
Route::post('/verify-email', [AuthController::class, 'verifyEmail']);
Route::post('/verify-email-forgot-password', [AuthController::class, 'verifyEmailForgotPassword']);
Route::post('/resend-verification', [AuthController::class, 'resendVerificationCode']);
Route::post('/reset-password', [AuthController::class, 'resetPassword']);


// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Auth
    Route::get('/user', [AuthController::class, 'user']);
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::post('/refresh', [AuthController::class, 'refresh']);
    Route::put('/password', [AuthController::class, 'updatePassword']);
    
    // Profile management
    Route::post('/profile/update', [AuthController::class, 'updateProfile']);
    Route::post('/profile/change-password', [AuthController::class, 'changePassword']);
    Route::post('/profile/update-photo', [AuthController::class, 'updatePhoto']);

    // Booking Services
    Route::prefix('bookings/{booking}/services')->group(function () {
        Route::get('/', [BookingServiceController::class, 'getBookingServices']);
        Route::post('/', [BookingServiceController::class, 'addServices']);
        Route::delete('/{serviceId}', [BookingServiceController::class, 'removeService']);
        Route::put('/{serviceId}/price', [BookingServiceController::class, 'updateServicePrice']);
    });

    // Include other API route files
    require __DIR__.'/admin.php';
    require __DIR__.'/owner.php';
    require __DIR__.'/manager.php';
    require __DIR__.'/hairdresser.php';
    require __DIR__.'/client.php';
});