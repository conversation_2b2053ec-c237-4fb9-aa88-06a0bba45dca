<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\SalonController;
use App\Http\Controllers\API\BookingServiceController;
use App\Http\Controllers\Auth\VerificationController;
/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

Route::get('/salons-list', [SalonController::class, 'salonList']);
Route::get('/salons-details/{id}', [SalonController::class, 'showSalonDetails']);
Route::get('/show-all-salon-details/{idSalon}', [SalonController::class, 'showSalonWithServicesAndOtherDetails']);
Route::get('/salons/search', [SalonController::class, 'search']);
Route::get('/services', [App\Http\Controllers\API\ServiceController::class, 'index']);
Route::get('/salons/{salonId}/hairdressers-by-services', [SalonController::class, 'getHairdressersByServices']);


// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/forgot-password', [AuthController::class, 'forgotPassword']);
Route::post('/reset-password', [AuthController::class, 'resetPassword']);
Route::get('/email/verify/{id}', [AuthController::class, 'verify'])->name('verification.verify');
Route::post('/verify-email', [AuthController::class, 'verifyEmail']);
Route::post('/verify-email-forgot-password', [AuthController::class, 'verifyEmailForgotPassword']);
Route::post('/resend-verification', [AuthController::class, 'resendVerificationCode']);
Route::post('/reset-password', [AuthController::class, 'resetPassword']);


// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Auth
    Route::get('/user', [AuthController::class, 'user']);
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::post('/refresh', [AuthController::class, 'refresh']);
    Route::put('/password', [AuthController::class, 'updatePassword']);
    
    // Profile management
    Route::post('/profile/update', [AuthController::class, 'updateProfile']);
    Route::post('/profile/change-password', [AuthController::class, 'changePassword']);
    Route::post('/profile/update-photo', [AuthController::class, 'updatePhoto']);

    // Booking Services
    Route::prefix('bookings/{booking}/services')->group(function () {
        Route::get('/', [BookingServiceController::class, 'getBookingServices']);
        Route::post('/', [BookingServiceController::class, 'addServices']);
        Route::delete('/{serviceId}', [BookingServiceController::class, 'removeService']);
        Route::put('/{serviceId}/price', [BookingServiceController::class, 'updateServicePrice']);
    });

    // Messaging routes
    Route::prefix('conversations')->group(function () {
        Route::get('/', [App\Http\Controllers\API\ConversationController::class, 'index']);
        Route::post('/', [App\Http\Controllers\API\ConversationController::class, 'store']);
        Route::get('/search', [App\Http\Controllers\API\ConversationController::class, 'search']);
        Route::get('/{conversation}', [App\Http\Controllers\API\ConversationController::class, 'show']);
        Route::put('/{conversation}', [App\Http\Controllers\API\ConversationController::class, 'update']);
        Route::delete('/{conversation}', [App\Http\Controllers\API\ConversationController::class, 'destroy']);
        Route::post('/{conversation}/participants', [App\Http\Controllers\API\ConversationController::class, 'addParticipant']);
        Route::delete('/{conversation}/participants', [App\Http\Controllers\API\ConversationController::class, 'removeParticipant']);
        Route::get('/{conversation}/participants', [App\Http\Controllers\API\ConversationController::class, 'participants']);

        // Messages dans une conversation
        Route::get('/{conversation}/messages', [App\Http\Controllers\API\MessageController::class, 'index']);
        Route::post('/{conversation}/messages', [App\Http\Controllers\API\MessageController::class, 'store']);
        Route::post('/{conversation}/messages/mark-all-read', [App\Http\Controllers\API\MessageController::class, 'markAllAsRead']);
    });

    Route::prefix('messages')->group(function () {
        Route::get('/{message}', [App\Http\Controllers\API\MessageController::class, 'show']);
        Route::put('/{message}', [App\Http\Controllers\API\MessageController::class, 'update']);
        Route::delete('/{message}', [App\Http\Controllers\API\MessageController::class, 'destroy']);
        Route::post('/{message}/read', [App\Http\Controllers\API\MessageController::class, 'markAsRead']);
    });

    // Payment routes
    Route::prefix('payments')->group(function () {
        Route::get('/', [App\Http\Controllers\API\PaymentController::class, 'index']);
        Route::get('/{payment}', [App\Http\Controllers\API\PaymentController::class, 'show']);
        Route::post('/{payment}/refund', [App\Http\Controllers\API\PaymentController::class, 'refund']);

        // Stripe routes
        Route::post('/stripe/payment-intent', [App\Http\Controllers\API\PaymentController::class, 'createStripePaymentIntent']);
        Route::post('/stripe/confirm', [App\Http\Controllers\API\PaymentController::class, 'confirmStripePayment']);

        // PayPal routes
        Route::post('/paypal/order', [App\Http\Controllers\API\PaymentController::class, 'createPayPalOrder']);
        Route::post('/paypal/capture', [App\Http\Controllers\API\PaymentController::class, 'capturePayPalOrder']);
    });

    // Payment methods routes
    Route::prefix('payment-methods')->group(function () {
        Route::get('/', [App\Http\Controllers\API\PaymentController::class, 'paymentMethods']);
        Route::post('/stripe', [App\Http\Controllers\API\PaymentController::class, 'saveStripePaymentMethod']);
        Route::delete('/{paymentMethod}', [App\Http\Controllers\API\PaymentController::class, 'deletePaymentMethod']);
        Route::post('/{paymentMethod}/default', [App\Http\Controllers\API\PaymentController::class, 'setDefaultPaymentMethod']);
    });

    // Include other API route files
    require __DIR__.'/admin.php';
    require __DIR__.'/owner.php';
    require __DIR__.'/manager.php';
    require __DIR__.'/hairdresser.php';
    require __DIR__.'/client.php';
});

// Webhook routes (sans authentification)
Route::prefix('webhooks')->group(function () {
    Route::post('/stripe', [App\Http\Controllers\API\WebhookController::class, 'stripeWebhook']);
    Route::post('/paypal', [App\Http\Controllers\API\WebhookController::class, 'paypalWebhook']);
});