<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\{
    AdminDashboardController,
    UserManagementController,
    SalonManagementController,
    ServiceManagementController,
    BookingManagementController,
    ReportController,
    <PERSON>tings<PERSON><PERSON>roller,
    SalonManagerController,
    StaffController
};
use App\Models\User;

/*
|--------------------------------------------------------------------------
| Admin API Routes
|--------------------------------------------------------------------------
*/

// Route::get('/test-dd', function () {
//     return dd(User::find(4));
// });

Route::middleware(['auth:sanctum', 'role:admin'])->group(function () {
    // Dashboard
    Route::get('/dashboard/stats', [AdminDashboardController::class, 'getStats']);

    // User Management
    Route::prefix('users')->group(function () {
        Route::get('/', [UserManagementController::class, 'index']);
        Route::get('/users-list', [UserManagementController::class, 'userList']);
        Route::post('/add-users', [UserManagementController::class, 'store']);
        
        // Routes spécifiques avant les routes avec paramètres
        Route::get('/free-managers', [UserManagementController::class, 'getFreeManagers']);
        Route::get('/owners', [UserManagementController::class, 'getOwners']);
        Route::get('/owner-stats', [UserManagementController::class, 'getOwnerStats']);
        Route::get('/test-managers', [UserManagementController::class, 'testManagers']);
        Route::post('/create-test-manager', [UserManagementController::class, 'createTestManager']);
        Route::post('/bulk-action', [UserManagementController::class, 'bulkAction']);
        
        // Routes avec paramètres en dernier
        Route::get('/{user}', [UserManagementController::class, 'show']);
        Route::post('/update-users/{user}', [UserManagementController::class, 'update']);
        Route::delete('/delete-users/{user}', [UserManagementController::class, 'destroy']);
    });

    // Salon Management
    Route::prefix('salons')->group(function () {
        Route::get('/', [SalonManagementController::class, 'index']);
        Route::post('/add-salon', [SalonManagementController::class, 'store']);
        Route::get('/{salon}', [SalonManagementController::class, 'show']);
        Route::post('/update-salon/{salon}', [SalonManagementController::class, 'update']);
        Route::delete('/delete-salon/{salon}', [SalonManagementController::class, 'destroy']);
        Route::get('/{salon}/analytics', [SalonManagementController::class, 'getAnalytics']);
        Route::put('/{salon}/status', [SalonManagementController::class, 'updateStatus']);
        
        // Manager Assignment
        Route::get('/manager-assignment', [SalonManagerController::class, 'index']);
        Route::post('/assign-manager', [SalonManagerController::class, 'assignManager']);
        Route::delete('/remove-manager', [SalonManagerController::class, 'removeManager']);
        Route::get('/unassigned-managers', [SalonManagerController::class, 'getUnassignedManagers']);
        Route::get('/salons-without-manager', [SalonManagerController::class, 'getSalonsWithoutManager']);
    });

    
    // Service Management
    Route::prefix('services')->group(function () {
        Route::get('/', [ServiceManagementController::class, 'index']);
        Route::get('/service-list', [ServiceManagementController::class, 'serviceList']);
        Route::post('/add-services', [ServiceManagementController::class, 'store']);
        Route::get('/{service}', [ServiceManagementController::class, 'show']);
        Route::put('/update-service/{service}', [ServiceManagementController::class, 'update']);
        Route::delete('/delete-service/{service}', [ServiceManagementController::class, 'destroy']);
        Route::post('/bulk-update', [ServiceManagementController::class, 'bulkUpdate']);
        Route::get('/analytics', [ServiceManagementController::class, 'getAnalytics']);
    });

    // Booking Management
    Route::prefix('bookings')->group(function () {
        Route::get('/', [BookingManagementController::class, 'index']);
        Route::get('/{booking}', [BookingManagementController::class, 'show']);
        Route::put('/{booking}', [BookingManagementController::class, 'update']);
        Route::delete('/{booking}', [BookingManagementController::class, 'destroy']);
        Route::get('/analytics', [BookingManagementController::class, 'getAnalytics']);
        Route::post('/bulk-update', [BookingManagementController::class, 'bulkUpdate']);
        Route::post('/{booking}/confirm', [BookingManagementController::class, 'confirm']);
        Route::post('/{booking}/reject', [BookingManagementController::class, 'reject']);
        Route::post('/reset-statuses', [BookingManagementController::class, 'resetBookingStatuses']);
    });

    // Reports
    Route::prefix('reports')->group(function () {
        Route::get('/financial', [ReportController::class, 'getFinancialReport']);
        Route::get('/bookings', [ReportController::class, 'getBookingReport']);
        Route::get('/customers', [ReportController::class, 'getCustomerReport']);
        Route::get('/services', [ReportController::class, 'getServiceReport']);
        Route::post('/export', [ReportController::class, 'exportReport']);
    });

    // Staff Management
    Route::prefix('staff')->group(function () {
        Route::get('/', [StaffController::class, 'index']);
        Route::post('/add-staff', [StaffController::class, 'store']);
        Route::get('/{staff}', [StaffController::class, 'show']);
        Route::post('/update-staff/{staff}', [StaffController::class, 'update']);
        Route::delete('/{staff}', [StaffController::class, 'destroy']);
        Route::put('/{staff}/availability', [StaffController::class, 'updateAvailability']);
    });

    // Settings
    Route::prefix('settings')->group(function () {
        Route::get('/', [SettingsController::class, 'index']);
        Route::put('/', [SettingsController::class, 'update']);

        // Email Settings
        Route::get('/email', [SettingsController::class, 'getEmailSettings']);
        Route::put('/email', [SettingsController::class, 'updateEmailSettings']);

        // SMS Settings
        Route::get('/sms', [SettingsController::class, 'getSMSSettings']);
        Route::put('/sms', [SettingsController::class, 'updateSMSSettings']);

        // Payment Settings
        Route::get('/payment', [SettingsController::class, 'getPaymentSettings']);
        Route::put('/payment', [SettingsController::class, 'updatePaymentSettings']);
    });
});