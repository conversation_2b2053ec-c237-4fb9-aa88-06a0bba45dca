<?php

use App\Http\Controllers\API\SalonController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Client\{
    ClientDashboardController,
    AppointmentController,
    PreferenceController,
    Favorite<PERSON>ontroller,
    Loyal<PERSON>Controller,
    ReviewController
};

/*
|--------------------------------------------------------------------------
| Client API Routes
|--------------------------------------------------------------------------
*/

// Route::get('/salons-list', [SalonController::class, 'salonList']);

// Route::get('/reserve', [AppointmentController::class, 'index']);


Route::middleware(['auth:sanctum', 'role:client'])->group(function () {
    // Dashboard
    Route::get('/dashboard/stats', [ClientDashboardController::class, 'getStats']);

    // Appointment Management
    Route::prefix('appointments')->group(function () {
        Route::get('/', [AppointmentController::class, 'index']);
        Route::post('/add-boking', [AppointmentController::class, 'store']);
        Route::get('/check-availability', [AppointmentController::class, 'checkAvailability']);
        Route::get('/{booking}', [AppointmentController::class, 'show']);
        Route::post('/{booking}/cancel', [AppointmentController::class, 'cancel']);
        Route::post('/{booking}/reschedule', [AppointmentController::class, 'reschedule']);
    });

    // Time Slots Management
    Route::prefix('time-slots')->group(function () {
        Route::get('/available', [\App\Http\Controllers\Client\TimeSlotController::class, 'getAvailableSlots']);
        Route::get('/weekly', [\App\Http\Controllers\Client\TimeSlotController::class, 'getWeeklyAvailability']);
        Route::get('/hairdressers', [\App\Http\Controllers\Client\TimeSlotController::class, 'getAvailableHairdressers']);
        Route::post('/check-availability', [\App\Http\Controllers\Client\TimeSlotController::class, 'checkSlotAvailability']);
        Route::post('/alternatives', [\App\Http\Controllers\Client\TimeSlotController::class, 'getSuggestedAlternatives']);
        Route::get('/{timeSlot}', [\App\Http\Controllers\Client\TimeSlotController::class, 'getSlotDetails']);
        Route::post('/calculate-duration', [\App\Http\Controllers\Client\TimeSlotController::class, 'calculateServicesDuration']);
    });

    // Preferences
    Route::prefix('preferences')->group(function () {
        Route::get('/', [PreferenceController::class, 'show']);
        Route::put('/', [PreferenceController::class, 'update']);
        Route::put('/notifications', [PreferenceController::class, 'updateNotifications']);
    });

    // Favorites
    Route::prefix('favorites')->group(function () {
        Route::get('/', [FavoriteController::class, 'index']);
        Route::post('/salons/{salon}', [FavoriteController::class, 'toggleSalon']);
        Route::post('/hairdressers/{hairdresser}', [FavoriteController::class, 'toggleHairdresser']);
        Route::post('/styles/{style}', [FavoriteController::class, 'toggleStyle']);
    });

    // Loyalty Program
    Route::prefix('loyalty')->group(function () {
        Route::get('/status', [LoyaltyController::class, 'getStatus']);
        Route::get('/transactions', [LoyaltyController::class, 'getTransactionHistory']);
        Route::post('/rewards/{reward}/redeem', [LoyaltyController::class, 'redeemReward']);
    });

    // Reviews
    Route::prefix('reviews')->group(function () {
        Route::get('/', [ReviewController::class, 'index']);
        Route::post('/bookings/{booking}', [ReviewController::class, 'store']);
        Route::put('/{review}', [ReviewController::class, 'update']);
        Route::delete('/{review}', [ReviewController::class, 'destroy']);
    });
});
