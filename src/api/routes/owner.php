<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use App\Http\Controllers\Owner\{
    OwnerDashboardController,
    SalonController,
    StaffController,
    ServiceController,
    BookingController,
    FinancialController,
    InventoryController
};
use App\Http\Controllers\Owner\ManagerController;

/*
|--------------------------------------------------------------------------
| Owner API Routes
|--------------------------------------------------------------------------
*/

Route::middleware(['auth:sanctum', 'role:owner'])->group(function () {

    // Dashboard
    Route::get('/dashboard/stats', [OwnerDashboardController::class, 'getStats']);

    // Manager Management (Gestion des gérants par les propriétaires)
    Route::prefix('managers')->group(function () {
        Route::get('/', [ManagerController::class, 'getOwnerManagers']);
        Route::post('/', [ManagerController::class, 'storeManager']);
        Route::get('/{manager}', [ManagerController::class, 'showManager']);
        Route::post('/update/{manager}', [ManagerController::class, 'updateManager']);
        Route::delete('/{manager}', [ManagerController::class, 'destroyManager']);
    });

    // Salon Management
    Route::prefix('salons')->group(function () {
        Route::get('/', [SalonController::class, 'index']);
        Route::post('/add-salon', [SalonController::class, 'store']);
        Route::get('/{salon}', [SalonController::class, 'show']);
        Route::post('/update-salon/{salon}', [SalonController::class, 'update']);
        Route::delete('/delete-salon/{salon}', [SalonController::class, 'destroy']);
        Route::get('/{salon}/analytics', [SalonController::class, 'getAnalytics']);
    });

    // Staff Management
    Route::prefix('staff')->group(function () {
        Route::get('/', [StaffController::class, 'index']);
        Route::post('/', [StaffController::class, 'store']);
        Route::get('/{staff}', [StaffController::class, 'show']);
        Route::post('/update-staff/{staff}', [StaffController::class, 'update']);
        Route::delete('/{staff}', [StaffController::class, 'destroy']);
        Route::put('/{staff}/availability', [StaffController::class, 'updateAvailability']);
    });

    // Service Management
    Route::prefix('services')->group(function () {
        Route::get('/', [ServiceController::class, 'index']);
        Route::get('/salons', [ServiceController::class, 'getSalons']);
        Route::post('/', [ServiceController::class, 'store']);
        Route::get('/{service}', [ServiceController::class, 'show']);
        Route::put('/{service}', [ServiceController::class, 'update']);
        Route::delete('/{service}', [ServiceController::class, 'destroy']);
        Route::post('/bulk-update', [ServiceController::class, 'bulkUpdate']);
    });

    // Booking Management
    Route::prefix('bookings')->group(function () {
        Route::get('/', [BookingController::class, 'index']);
        Route::get('/{booking}', [BookingController::class, 'show']);
        Route::put('/{booking}', [BookingController::class, 'update']);
        Route::post('/{booking}/confirm', [BookingController::class, 'confirm']);
        Route::post('/{booking}/reject', [BookingController::class, 'reject']);
        Route::get('/analytics', [BookingController::class, 'getAnalytics']);
        Route::post('/reset-statuses', [BookingController::class, 'resetBookingStatuses']);
    });

    // Financial Management
    Route::prefix('financial')->group(function () {
        Route::get('/overview', [FinancialController::class, 'getFinancialOverview']);
        Route::get('/staff-performance', [FinancialController::class, 'getStaffPerformance']);
        Route::get('/outstanding-payments', [FinancialController::class, 'getOutstandingPayments']);
        Route::post('/generate-report', [FinancialController::class, 'generateReport']);
    });

    // Inventory Management
    Route::prefix('inventory')->group(function () {
        Route::get('/', [InventoryController::class, 'index']);
        Route::post('/', [InventoryController::class, 'store']);
        Route::get('/{product}', [InventoryController::class, 'show']);
        Route::put('/{product}', [InventoryController::class, 'update']);
        Route::delete('/{product}', [InventoryController::class, 'destroy']);
        Route::post('/{product}/adjust-stock', [InventoryController::class, 'adjustStock']);
        Route::get('/low-stock', [InventoryController::class, 'getLowStockAlert']);
    });

    // Time Slots Management
    Route::prefix('time-slots')->group(function () {
        Route::get('/overview', [\App\Http\Controllers\Owner\TimeSlotManagementController::class, 'getAllSalonsOverview']);
        Route::post('/salons/{salon}/setup-defaults', [\App\Http\Controllers\Owner\TimeSlotManagementController::class, 'setupDefaultTemplates']);
        Route::get('/comparative-stats', [\App\Http\Controllers\Owner\TimeSlotManagementController::class, 'getComparativeStats']);
        Route::post('/bulk-configure', [\App\Http\Controllers\Owner\TimeSlotManagementController::class, 'bulkConfigureTemplates']);
    });
});
