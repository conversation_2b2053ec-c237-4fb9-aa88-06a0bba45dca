<?php

namespace Database\Factories;

use App\Models\Salon;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Storage;

class SalonFactory extends Factory
{
    protected $model = Salon::class;

    public function definition()
    {
        // Génération d'un nom unique pour l'image
        $imageName = 'salon_' . $this->faker->unique()->numberBetween(1, 1000) . '.jpg';

        // Chemin relatif dans le dossier public
        $imagePath = 'salon_photos/' . $imageName;

        // URL de l'image aléatoire depuis Lorem Picsum
        $imageUrl = 'https://picsum.photos/640/480';

        // Téléchargement de l'image
        try {
            $imageContent = file_get_contents($imageUrl);
            if ($imageContent) {
                // Enregistrement de l'image dans le stockage public
                Storage::disk('public')->put($imagePath, $imageContent);
            }
        } catch (\Exception $e) {
            // Gestion des erreurs en cas d'échec du téléchargement
            $imagePath = null; // Laisser l'image vide si le téléchargement échoue
        }

        // Liste des jours de la semaine
        $daysOfWeek = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche'];

        // Génération des heures aléatoires pour chaque jour avec une logique correcte
        $hours = array_map(function ($day) {
            $openTime = $this->faker->time('H:i');
            $closeTime = $this->faker->time('H:i');

            // Assurer que l'heure d'ouverture est toujours inférieure ou égale à l'heure de fermeture
            if (strtotime($openTime) > strtotime($closeTime)) {
                [$openTime, $closeTime] = [$closeTime, $openTime]; // Inverser si nécessaire
            }

            return [
                'day' => $day,
                'open' => $openTime,
                'close' => $closeTime,
            ];
        }, $daysOfWeek);

        return [
            'name' => $this->faker->company,
            'address' => $this->faker->address,
            'hours' => $hours, // Tableau d'objets contenant les jours et les heures
            'contact' => [
                'phone' => $this->faker->phoneNumber,
                'email' => $this->faker->safeEmail,
            ],
            // Enregistrement du chemin relatif dans la colonne `images`
            'images' => $imagePath,
        ];
    }
}
