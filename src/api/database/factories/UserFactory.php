<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Eloquent\Factories\Factory;

class UserFactory extends Factory
{
    protected $model = User::class;

    public function definition()
    {
        return [
            'firstName' => $this->faker->firstName,
            'lastName' => $this->faker->lastName,
            'email' => $this->faker->unique()->safeEmail,
            'password' => Hash::make('password'),
            'phone' => $this->faker->phoneNumber,
            'salonId' => \App\Models\Salon::factory(),
            // 'specialties' => [$this->faker->word, $this->faker->word],
           'availability' => json_encode(['Monday' => '9-5', 'Tuesday' => '10-6']),

            'role' => 'hairdresser',
        ];
    }
}
