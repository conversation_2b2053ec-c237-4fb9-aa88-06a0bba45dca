<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('availability_templates', function (Blueprint $table) {
            // Ajouter les index avec des noms plus courts
            $table->index(['hairdresser_id', 'day_of_week', 'is_active'], 'avail_tmpl_hairdresser_day_active_idx');
            $table->index(['salon_id', 'day_of_week', 'is_active'], 'avail_tmpl_salon_day_active_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('availability_templates', function (Blueprint $table) {
            // Supprimer les nouveaux index
            $table->dropIndex('avail_tmpl_hairdresser_day_active_idx');
            $table->dropIndex('avail_tmpl_salon_day_active_idx');

            // Recréer les anciens index (qui causeront l'erreur, mais c'est pour le rollback)
            $table->index(['hairdresser_id', 'day_of_week', 'is_active']);
            $table->index(['salon_id', 'day_of_week', 'is_active']);
        });
    }
};
