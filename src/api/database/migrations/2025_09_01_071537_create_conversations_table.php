<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('conversations', function (Blueprint $table) {
            $table->id();
            $table->string('title')->nullable(); // Titre de la conversation (optionnel)
            $table->enum('type', ['private', 'group', 'booking_related'])->default('private');
            $table->foreignId('booking_id')->nullable()->constrained('bookings')->onDelete('cascade'); // Lié à une réservation
            $table->foreignId('salon_id')->nullable()->constrained('salons')->onDelete('cascade'); // Contexte salon
            $table->json('participants'); // IDs des participants
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamp('last_message_at')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Index pour optimiser les requêtes
            $table->index(['salon_id', 'is_active']);
            $table->index(['booking_id']);
            $table->index(['last_message_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('conversations');
    }
};
