<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('managers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('owner_id');
            $table->unsignedBigInteger('manager_id');
            $table->timestamps();
            $table->softDeletes();

            // Clés étrangères
            $table->foreign('owner_id')->references('id')->on('users')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('manager_id')->references('id')->on('users')->onUpdate('cascade')->onDelete('cascade');

            // Index pour optimiser les requêtes
            $table->index(['owner_id', 'manager_id']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('managers');
    }
}; 