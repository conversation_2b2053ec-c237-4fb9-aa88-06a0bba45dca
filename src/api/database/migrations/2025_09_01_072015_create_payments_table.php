<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('booking_id')->constrained('bookings')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('payment_intent_id')->nullable(); // Stripe Payment Intent ID
            $table->string('paypal_order_id')->nullable(); // PayPal Order ID
            $table->enum('provider', ['stripe', 'paypal']);
            $table->enum('status', ['pending', 'processing', 'succeeded', 'failed', 'canceled', 'refunded'])->default('pending');
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('EUR');
            $table->decimal('fee_amount', 10, 2)->nullable(); // Frais de transaction
            $table->decimal('net_amount', 10, 2)->nullable(); // Montant net après frais
            $table->json('metadata')->nullable(); // Données supplémentaires
            $table->string('failure_reason')->nullable();
            $table->timestamp('paid_at')->nullable();
            $table->timestamp('refunded_at')->nullable();
            $table->decimal('refunded_amount', 10, 2)->default(0);
            $table->timestamps();

            // Index pour optimiser les requêtes
            $table->index(['booking_id', 'status']);
            $table->index(['user_id', 'status']);
            $table->index(['provider', 'status']);
            $table->index(['payment_intent_id']);
            $table->index(['paypal_order_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
