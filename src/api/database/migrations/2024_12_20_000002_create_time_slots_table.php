<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('time_slots', function (Blueprint $table) {
            $table->id();
            $table->foreignId('availability_template_id')->constrained('availability_templates')->onDelete('cascade');
            $table->foreignId('hairdresser_id')->constrained('hairdressers')->onDelete('cascade');
            $table->foreignId('salon_id')->constrained('salons')->onDelete('cascade');
            $table->date('date'); // Date du créneau
            $table->time('start_time'); // Heure de début du créneau
            $table->time('end_time'); // Heure de fin du créneau
            $table->integer('duration'); // Durée en minutes
            $table->enum('status', ['available', 'booked', 'blocked', 'maintenance'])->default('available');
            $table->foreignId('booking_id')->nullable()->constrained('bookings')->onDelete('set null');
            $table->text('notes')->nullable(); // Notes internes (maintenance, etc.)
            $table->timestamps();

            // Index pour optimiser les requêtes de disponibilité
            $table->index(['hairdresser_id', 'date', 'status']);
            $table->index(['salon_id', 'date', 'status']);
            $table->index(['date', 'start_time', 'status']);
            
            // Contrainte d'unicité pour éviter les créneaux en double
            $table->unique(['hairdresser_id', 'date', 'start_time']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('time_slots');
    }
};
