<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_methods', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->enum('provider', ['stripe', 'paypal']);
            $table->string('provider_id'); // ID du moyen de paiement chez le provider
            $table->enum('type', ['card', 'paypal_account', 'bank_account']);
            $table->string('last_four')->nullable(); // 4 derniers chiffres pour les cartes
            $table->string('brand')->nullable(); // Visa, Mastercard, etc.
            $table->string('exp_month')->nullable();
            $table->string('exp_year')->nullable();
            $table->string('country')->nullable();
            $table->json('billing_details')->nullable();
            $table->boolean('is_default')->default(false);
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Index pour optimiser les requêtes
            $table->index(['user_id', 'is_active']);
            $table->index(['provider', 'provider_id']);
            $table->unique(['user_id', 'provider_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_methods');
    }
};
