<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('slot_exceptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('hairdresser_id')->constrained('hairdressers')->onDelete('cascade');
            $table->date('date'); // Date de l'exception
            $table->time('start_time')->nullable(); // Heure de début (null = toute la journée)
            $table->time('end_time')->nullable(); // Heure de fin (null = toute la journée)
            $table->enum('type', ['unavailable', 'break', 'lunch', 'vacation', 'sick', 'training', 'custom'])->default('unavailable');
            $table->string('reason')->nullable(); // Raison de l'exception
            $table->boolean('recurring')->default(false); // Si l'exception se répète
            $table->json('recurring_pattern')->nullable(); // Pattern de récurrence (weekly, monthly, etc.)
            $table->date('recurring_until')->nullable(); // Date de fin de récurrence
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Index pour optimiser les requêtes
            $table->index(['hairdresser_id', 'date', 'is_active']);
            $table->index(['date', 'type', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('slot_exceptions');
    }
};
