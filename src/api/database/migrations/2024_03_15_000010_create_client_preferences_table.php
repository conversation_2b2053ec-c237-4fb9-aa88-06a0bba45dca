<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('client_preferences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('userId')->constrained('users');
            $table->foreignId('salonId')->constrained('salons');
            $table->foreignId('preferred_hairdresser_id')->nullable()->constrained('hairdressers');
            $table->json('preferred_services')->nullable();
            $table->json('preferred_days')->nullable();
            $table->json('preferred_times')->nullable();
            $table->json('notification_preferences');
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('client_preferences');
    }
};