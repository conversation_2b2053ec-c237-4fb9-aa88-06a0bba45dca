<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Ajouter la référence au time slot
            $table->foreignId('time_slot_id')->nullable()->after('hairdresserId')->constrained('time_slots')->onDelete('set null');
            
            // Ajouter la durée totale calculée des services
            $table->integer('total_duration')->nullable()->after('total_amount'); // Durée totale en minutes
            
            // Ajouter l'heure de fin calculée
            $table->dateTime('end_time')->nullable()->after('dateTime');
            
            // Marquer l'ancien système comme optionnel pour la transition
            $table->dateTime('dateTime')->nullable()->change();
            
            // Index pour optimiser les requêtes
            $table->index(['time_slot_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropForeign(['time_slot_id']);
            $table->dropIndex(['time_slot_id', 'status']);
            $table->dropColumn(['time_slot_id', 'total_duration', 'end_time']);
            
            // Remettre dateTime comme obligatoire
            $table->dateTime('dateTime')->nullable(false)->change();
        });
    }
};
