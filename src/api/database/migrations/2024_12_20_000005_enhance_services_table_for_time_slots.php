<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('services', function (Blueprint $table) {
            // Ajouter des informations de durée plus précises
            $table->integer('preparation_time')->default(0)->after('duration'); // Temps de préparation en minutes
            $table->integer('cleanup_time')->default(5)->after('preparation_time'); // Temps de nettoyage en minutes
            $table->boolean('requires_consultation')->default(false)->after('cleanup_time'); // Nécessite une consultation
            $table->integer('consultation_duration')->default(15)->after('requires_consultation'); // Durée consultation en minutes
            
            // Contraintes de réservation
            $table->integer('min_advance_booking')->default(60)->after('consultation_duration'); // Minutes min à l'avance
            $table->integer('max_advance_booking')->default(43200)->after('min_advance_booking'); // Minutes max à l'avance (30 jours)
            
            // Disponibilité par jour de la semaine
            $table->json('available_days')->nullable()->after('max_advance_booking'); // [1,2,3,4,5] pour lun-ven
            
            // Index pour optimiser les requêtes
            $table->index(['active', 'salonId']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('services', function (Blueprint $table) {
            $table->dropIndex(['active', 'salonId']);
            $table->dropColumn([
                'preparation_time',
                'cleanup_time', 
                'requires_consultation',
                'consultation_duration',
                'min_advance_booking',
                'max_advance_booking',
                'available_days'
            ]);
        });
    }
};
