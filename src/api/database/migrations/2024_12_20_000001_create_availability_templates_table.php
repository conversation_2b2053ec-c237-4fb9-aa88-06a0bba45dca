<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('availability_templates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('salon_id')->constrained('salons')->onDelete('cascade');
            $table->foreignId('hairdresser_id')->constrained('hairdressers')->onDelete('cascade');
            $table->string('name')->nullable(); // Nom du template (ex: "Horaires d'été")
            $table->tinyInteger('day_of_week'); // 0=Dimanche, 1=Lundi, ..., 6=Samedi
            $table->time('start_time'); // Heure de début (ex: 09:00)
            $table->time('end_time'); // Heure de fin (ex: 18:00)
            $table->integer('slot_duration')->default(30); // Durée d'un créneau en minutes
            $table->integer('buffer_time')->default(15); // Temps de buffer entre créneaux en minutes
            $table->integer('max_advance_booking')->default(30); // Nombre de jours max pour réserver à l'avance
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Index pour optimiser les requêtes
            $table->index(['hairdresser_id', 'day_of_week', 'is_active']);
            $table->index(['salon_id', 'day_of_week', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('availability_templates');
    }
};
