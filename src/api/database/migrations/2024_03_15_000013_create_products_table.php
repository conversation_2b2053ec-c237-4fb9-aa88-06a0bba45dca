<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();

            // Clé étrangère salonId avec suppression en cascade
            $table->foreignId('salonId')
                  ->constrained('salons')
                  ->onDelete('cascade');

            $table->string('name');
            $table->string('category');
            $table->integer('stock');
            $table->integer('min_stock');
            $table->decimal('price', 10, 2);
            $table->string('supplier');
            $table->text('description')->nullable();
            
            // SKU avec une valeur par défaut "N/A"
            $table->string('sku')->default('N/A');

            $table->boolean('active')->default(true);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('products');
    }
};
