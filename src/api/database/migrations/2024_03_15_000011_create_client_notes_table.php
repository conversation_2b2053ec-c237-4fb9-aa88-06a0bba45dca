<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('client_notes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('clientId')->constrained('users');
            $table->foreignId('hairdresserId')->constrained('hairdressers');
            $table->text('content');
            $table->enum('type', ['style', 'preference', 'allergy', 'general']);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('client_notes');
    }
};