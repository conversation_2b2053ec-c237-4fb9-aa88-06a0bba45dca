<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class AdminSeeder extends Seeder
{
    public function run()
    {
        // Création des utilisateurs pour chaque rôle
        User::create([
            'firstName' => 'Admin',
            'lastName' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'phone' => '0000000000',
            'role' => 'admin',
            'salonId' => null,
            'email_verified_at' => Carbon::now(),
        ]);

        User::create([
            'firstName' => 'Propriétaire',
            'lastName' => 'Salon',
            'email' => '<EMAIL>',
            'password' => Hash::make('owner123'),
            'phone' => '0000000001',
            'role' => 'owner',
            'salonId' => null,
            'email_verified_at' => Carbon::now(),
        ]);

        User::create([
            'firstName' => 'Gérant',
            'lastName' => 'Salon',
            'email' => '<EMAIL>',
            'password' => Hash::make('manager123'),
            'phone' => '0000000002',
            'role' => 'manager',
            'salonId' => null,
            'email_verified_at' => Carbon::now(),
        ]);

        User::create([
            'firstName' => 'Coiffeur',
            'lastName' => 'Salon',
            'email' => '<EMAIL>',
            'password' => Hash::make('hairdresser123'),
            'phone' => '0000000003',
            'role' => 'hairdresser',
            'salonId' => null,
            'email_verified_at' => Carbon::now(),
        ]);
    }
}