<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Hairdresser;
use App\Models\User;
use App\Models\Salon;

class HairdresserSeeder extends Seeder
{
    public function run()
    {
        $salons = Salon::all();

        if ($salons->isEmpty()) {
            $this->command->warn('Aucun salon trouvé. Veuillez exécuter le SalonSeeder avant ce seeder.');
            return;
        }

        $hairdresserUsers = User::where('role', 'hairdresser')->get();

        foreach ($hairdresserUsers as $user) {
            Hairdresser::create([
                'userId' => $user->id,
                'salonId' => $salons->random()->id, // Associe chaque coiffeur à un salon aléatoire
                'specialties' => json_encode(['cutting', 'styling', 'coloring']),
                'availability' => json_encode([
                    ['day' => 'Monday', 'start' => '09:00', 'end' => '17:00'],
                    ['day' => 'Tuesday', 'start' => '10:00', 'end' => '18:00'],
                ]),
                'rating' => rand(0, 50) / 10, // Note aléatoire entre 0 et 5.0
                'active' => true,
            ]);
        }
    }
}
