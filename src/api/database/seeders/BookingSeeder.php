<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Booking;
use App\Models\User;
use App\Models\Salon;
use App\Models\Hairdresser;
use Illuminate\Support\Facades\DB;

class BookingSeeder extends Seeder
{
    public function run()
{
    $clients = \App\Models\User::where('role', 'client')->get();
    $salons = \App\Models\Salon::all();
    $hairdressers = \App\Models\Hairdresser::all();

    if ($clients->isEmpty() || $salons->isEmpty() || $hairdressers->isEmpty()) {
        $this->command->warn('Pas de données disponibles pour générer les réservations.');
        return;
    }

    $this->command->info("Nombre de clients : {$clients->count()}");
    $this->command->info("Nombre de salons : {$salons->count()}");
    $this->command->info("Nombre de coiffeurs : {$hairdressers->count()}");

    foreach ($clients as $client) {
        foreach ($salons as $salon) {
            $salonHairdressers = $hairdressers->where('salonId', $salon->id);
            if ($salonHairdressers->isEmpty()) {
                $this->command->warn("Aucun coiffeur disponible pour le salon ID {$salon->id}");
                continue;
            }

            $hairdresser = $salonHairdressers->random();

            \App\Models\Booking::create([
                'clientId' => $client->id,
                'salonId' => $salon->id,
                'hairdresserId' => $hairdresser->id,
                'dateTime' => now()->addDays(rand(1, 30))->setTime(rand(9, 18), 0),
                'status' => collect(['pending', 'confirmed', 'completed', 'cancelled'])->random(),
                'total_amount' => rand(50, 200),
                'loyalty_points_earned' => rand(0, 50),
                'rating' => rand(30, 50) / 10,
                'cancellation_reason' => null,
                'notes' => 'Remarque aléatoire générée pour la réservation.',
            ]);
            
            

            $this->command->info("Réservation créée pour Client ID {$client->id}, Salon ID {$salon->id}, Coiffeur ID {$hairdresser->id}");
        }
    }
}

}
