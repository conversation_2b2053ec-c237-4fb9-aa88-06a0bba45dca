<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Service;
use App\Models\Salon;

class ServiceSeeder extends Seeder
{
    public function run()
    {
        $salons = Salon::all();

        foreach ($salons as $salon) {
            Service::factory()->count(5)->create([
                'salonId' => $salon->id, // Associe chaque service à un salon
            ]);
        }
    }
}
