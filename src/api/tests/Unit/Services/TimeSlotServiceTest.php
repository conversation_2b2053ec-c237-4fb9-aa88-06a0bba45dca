<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\TimeSlotService;
use App\Models\TimeSlot;
use App\Models\AvailabilityTemplate;
use App\Models\Hairdresser;
use App\Models\Salon;
use App\Models\Service;
use App\Models\Booking;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TimeSlotServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $timeSlotService;
    protected $salon;
    protected $hairdresser;
    protected $template;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->timeSlotService = app(TimeSlotService::class);
        $this->salon = Salon::factory()->create();
        $this->hairdresser = Hairdresser::factory()->create(['salonId' => $this->salon->id]);
        $this->template = AvailabilityTemplate::factory()->create([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $this->hairdresser->id,
            'day_of_week' => 1, // Lundi
            'start_time' => '09:00',
            'end_time' => '17:00',
            'slot_duration' => 30,
            'buffer_time' => 15,
        ]);
    }

    public function test_generate_time_slots_for_hairdresser()
    {
        $startDate = now()->next(Carbon::MONDAY)->toDateString();
        $endDate = now()->next(Carbon::MONDAY)->addDays(6)->toDateString();

        $generated = $this->timeSlotService->generateTimeSlotsForHairdresser(
            $this->hairdresser,
            $startDate,
            $endDate
        );

        $this->assertGreaterThan(0, $generated);
        
        // Vérifier que les créneaux ont été créés
        $slots = TimeSlot::where('hairdresser_id', $this->hairdresser->id)
            ->whereBetween('date', [$startDate, $endDate])
            ->get();

        $this->assertEquals($generated, $slots->count());
        
        // Vérifier qu'il y a des créneaux pour le lundi (day_of_week = 1)
        $mondaySlots = $slots->where('date', $startDate);
        $this->assertGreaterThan(0, $mondaySlots->count());
    }

    public function test_generate_time_slots_for_salon()
    {
        // Créer un deuxième coiffeur
        $hairdresser2 = Hairdresser::factory()->create(['salonId' => $this->salon->id]);
        AvailabilityTemplate::factory()->create([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $hairdresser2->id,
            'day_of_week' => 1,
        ]);

        $startDate = now()->next(Carbon::MONDAY)->toDateString();
        $endDate = now()->next(Carbon::MONDAY)->toDateString();

        $generated = $this->timeSlotService->generateTimeSlotsForSalon(
            $this->salon,
            $startDate,
            $endDate
        );

        $this->assertGreaterThan(0, $generated);
        
        // Vérifier que les créneaux ont été créés pour les deux coiffeurs
        $slots1 = TimeSlot::where('hairdresser_id', $this->hairdresser->id)->count();
        $slots2 = TimeSlot::where('hairdresser_id', $hairdresser2->id)->count();
        
        $this->assertGreaterThan(0, $slots1);
        $this->assertGreaterThan(0, $slots2);
    }

    public function test_find_available_slots()
    {
        // Créer des créneaux
        $availableSlot = TimeSlot::factory()->create([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $this->hairdresser->id,
            'status' => TimeSlot::STATUS_AVAILABLE,
            'date' => now()->addDay()->toDateString(),
            'duration' => 60,
        ]);

        $bookedSlot = TimeSlot::factory()->create([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $this->hairdresser->id,
            'status' => TimeSlot::STATUS_BOOKED,
            'date' => now()->addDay()->toDateString(),
            'duration' => 60,
        ]);

        $slots = $this->timeSlotService->findAvailableSlots($this->salon->id);

        $this->assertEquals(1, $slots->count());
        $this->assertEquals($availableSlot->id, $slots->first()->id);
    }

    public function test_find_available_slots_with_duration_filter()
    {
        // Créer des créneaux avec différentes durées
        $shortSlot = TimeSlot::factory()->create([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $this->hairdresser->id,
            'status' => TimeSlot::STATUS_AVAILABLE,
            'date' => now()->addDay()->toDateString(),
            'duration' => 30,
        ]);

        $longSlot = TimeSlot::factory()->create([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $this->hairdresser->id,
            'status' => TimeSlot::STATUS_AVAILABLE,
            'date' => now()->addDay()->toDateString(),
            'duration' => 90,
        ]);

        // Chercher des créneaux d'au moins 60 minutes
        $slots = $this->timeSlotService->findAvailableSlots($this->salon->id, null, null, 60);

        $this->assertEquals(1, $slots->count());
        $this->assertEquals($longSlot->id, $slots->first()->id);
    }

    public function test_calculate_total_service_duration()
    {
        $service1 = Service::factory()->create([
            'duration' => 30,
            'preparation_time' => 5,
            'cleanup_time' => 5,
        ]);

        $service2 = Service::factory()->create([
            'duration' => 45,
            'preparation_time' => 10,
            'cleanup_time' => 5,
        ]);

        $totalDuration = $this->timeSlotService->calculateTotalServiceDuration([
            $service1->id,
            $service2->id
        ]);

        // (30+5+5) + (45+10+5) = 40 + 60 = 100
        $this->assertEquals(100, $totalDuration);
    }

    public function test_book_time_slot()
    {
        $timeSlot = TimeSlot::factory()->create([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $this->hairdresser->id,
            'status' => TimeSlot::STATUS_AVAILABLE,
            'date' => now()->addDay()->toDateString(),
        ]);

        $booking = Booking::factory()->create();

        $result = $this->timeSlotService->bookTimeSlot($timeSlot, $booking);

        $this->assertTrue($result);
        $this->assertEquals(TimeSlot::STATUS_BOOKED, $timeSlot->fresh()->status);
        $this->assertEquals($booking->id, $timeSlot->fresh()->booking_id);
        $this->assertEquals($timeSlot->id, $booking->fresh()->time_slot_id);
    }

    public function test_book_time_slot_fails_if_not_available()
    {
        $timeSlot = TimeSlot::factory()->create([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $this->hairdresser->id,
            'status' => TimeSlot::STATUS_BOOKED,
            'date' => now()->addDay()->toDateString(),
        ]);

        $booking = Booking::factory()->create();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Ce créneau n\'est plus disponible');

        $this->timeSlotService->bookTimeSlot($timeSlot, $booking);
    }

    public function test_release_time_slot()
    {
        $booking = Booking::factory()->create();
        $timeSlot = TimeSlot::factory()->create([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $this->hairdresser->id,
            'status' => TimeSlot::STATUS_BOOKED,
            'booking_id' => $booking->id,
            'date' => now()->addDay()->toDateString(),
        ]);

        $booking->time_slot_id = $timeSlot->id;
        $booking->save();

        $result = $this->timeSlotService->releaseTimeSlot($timeSlot);

        $this->assertTrue($result);
        $this->assertEquals(TimeSlot::STATUS_AVAILABLE, $timeSlot->fresh()->status);
        $this->assertNull($timeSlot->fresh()->booking_id);
        $this->assertNull($booking->fresh()->time_slot_id);
    }

    public function test_check_availability()
    {
        $date = now()->addDay()->toDateString();
        $startTime = '14:00:00';
        $duration = 60;

        // Pas de conflit - devrait être disponible
        $available = $this->timeSlotService->checkAvailability(
            $this->hairdresser->id,
            $date,
            $startTime,
            $duration
        );

        $this->assertTrue($available);

        // Créer un créneau en conflit
        TimeSlot::factory()->create([
            'hairdresser_id' => $this->hairdresser->id,
            'date' => $date,
            'start_time' => '14:30:00',
            'end_time' => '15:30:00',
            'status' => TimeSlot::STATUS_BOOKED,
        ]);

        // Maintenant il devrait y avoir un conflit
        $available = $this->timeSlotService->checkAvailability(
            $this->hairdresser->id,
            $date,
            $startTime,
            $duration
        );

        $this->assertFalse($available);
    }

    public function test_get_weekly_availability()
    {
        $startDate = now()->startOfWeek()->toDateString();

        // Créer des créneaux pour différents jours
        TimeSlot::factory()->create([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $this->hairdresser->id,
            'status' => TimeSlot::STATUS_AVAILABLE,
            'date' => $startDate,
        ]);

        TimeSlot::factory()->create([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $this->hairdresser->id,
            'status' => TimeSlot::STATUS_AVAILABLE,
            'date' => Carbon::parse($startDate)->addDay()->toDateString(),
        ]);

        $weeklyData = $this->timeSlotService->getWeeklyAvailability($this->salon->id, $startDate);

        $this->assertIsArray($weeklyData);
        $this->assertCount(7, $weeklyData); // 7 jours dans la semaine
        
        // Vérifier la structure des données
        foreach ($weeklyData as $dayData) {
            $this->assertArrayHasKey('date', $dayData);
            $this->assertArrayHasKey('day_name', $dayData);
            $this->assertArrayHasKey('slots', $dayData);
        }
    }

    public function test_block_time_slots()
    {
        // Créer des créneaux disponibles
        TimeSlot::factory()->count(3)->create([
            'hairdresser_id' => $this->hairdresser->id,
            'date' => now()->addDay()->toDateString(),
            'status' => TimeSlot::STATUS_AVAILABLE,
        ]);

        $reason = 'Maintenance';
        $blocked = $this->timeSlotService->blockTimeSlots(
            $this->hairdresser->id,
            now()->addDay()->toDateString(),
            now()->addDay()->toDateString(),
            null,
            null,
            $reason
        );

        $this->assertEquals(3, $blocked);
        
        $blockedSlots = TimeSlot::where('hairdresser_id', $this->hairdresser->id)
            ->where('status', TimeSlot::STATUS_BLOCKED)
            ->get();

        $this->assertEquals(3, $blockedSlots->count());
        $this->assertEquals($reason, $blockedSlots->first()->notes);
    }

    public function test_get_occupancy_stats()
    {
        $startDate = now()->toDateString();
        $endDate = now()->addDay()->toDateString();

        // Créer des créneaux avec différents statuts
        TimeSlot::factory()->count(2)->create([
            'salon_id' => $this->salon->id,
            'date' => $startDate,
            'status' => TimeSlot::STATUS_AVAILABLE,
        ]);

        TimeSlot::factory()->count(3)->create([
            'salon_id' => $this->salon->id,
            'date' => $startDate,
            'status' => TimeSlot::STATUS_BOOKED,
        ]);

        TimeSlot::factory()->create([
            'salon_id' => $this->salon->id,
            'date' => $startDate,
            'status' => TimeSlot::STATUS_BLOCKED,
        ]);

        $stats = $this->timeSlotService->getOccupancyStats($this->salon->id, $startDate, $endDate);

        $this->assertEquals(6, $stats['total']);
        $this->assertEquals(2, $stats['available']);
        $this->assertEquals(3, $stats['booked']);
        $this->assertEquals(1, $stats['blocked']);
        $this->assertEquals(50.0, $stats['occupancy_rate']); // 3/6 * 100
    }
}
