<?php

namespace App\Notifications;

use App\Models\Booking;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class BookingConfirmation extends Notification
{
    use Queueable;

    protected $booking;

    public function __construct(Booking $booking)
    {
        $this->booking = $booking;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        $salon = $this->booking->salon;
        $hairdresser = $this->booking->hairdresser;

        return (new MailMessage)
            ->subject('Booking Confirmation - ' . $salon->name)
            ->greeting('Hello ' . $notifiable->firstName . '!')
            ->line('Your booking has been confirmed.')
            ->line('Details:')
            ->line('Date: ' . $this->booking->dateTime->format('l, F j, Y'))
            ->line('Time: ' . $this->booking->dateTime->format('g:i A'))
            ->line('Salon: ' . $salon->name)
            ->line('Hairdresser: ' . $hairdresser->user->firstName . ' ' . $hairdresser->user->lastName)
            ->line('Services: ' . $this->booking->services->pluck('name')->implode(', '))
            ->line('Total Amount: $' . number_format($this->booking->total_amount, 2))
            ->action('View Booking Details', url('/bookings/' . $this->booking->id))
            ->line('Thank you for choosing our services!');
    }

    public function toArray($notifiable)
    {
        return [
            'booking_id' => $this->booking->id,
            'type' => 'booking_confirmation',
            'message' => 'Your booking has been confirmed',
            'date' => $this->booking->dateTime->format('Y-m-d H:i:s'),
        ];
    }
}