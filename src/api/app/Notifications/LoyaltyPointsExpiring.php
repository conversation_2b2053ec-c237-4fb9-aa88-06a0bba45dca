<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class LoyaltyPointsExpiring extends Notification
{
    use Queueable;

    protected $points;
    protected $expiryDate;

    public function __construct(int $points, $expiryDate)
    {
        $this->points = $points;
        $this->expiryDate = $expiryDate;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Loyalty Points Expiring Soon')
            ->greeting('Hello ' . $notifiable->firstName . '!')
            ->line($this->points . ' loyalty points will expire on ' . $this->expiryDate->format('F j, Y'))
            ->line('Don\'t let your points go to waste! Redeem them now for great rewards.')
            ->action('View Available Rewards', url('/rewards'))
            ->line('Thank you for being a valued customer!');
    }

    public function toArray($notifiable)
    {
        return [
            'type' => 'loyalty_points_expiring',
            'points' => $this->points,
            'expiry_date' => $this->expiryDate->format('Y-m-d'),
            'message' => $this->points . ' points expiring on ' . $this->expiryDate->format('M j, Y'),
            'date' => now()->format('Y-m-d H:i:s'),
        ];
    }
}