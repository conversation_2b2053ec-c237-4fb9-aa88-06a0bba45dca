<?php

namespace App\Notifications;

use App\Models\Review;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class NewReview extends Notification
{
    use Queueable;

    protected $review;

    public function __construct(Review $review)
    {
        $this->review = $review;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('New Review Received')
            ->greeting('Hello ' . $notifiable->firstName . '!')
            ->line('A new review has been submitted for your salon.')
            ->line('Rating: ' . $this->review->rating . ' stars')
            ->line('Comment: ' . $this->review->comment)
            ->line('From: ' . $this->review->user->firstName . ' ' . $this->review->user->lastName)
            ->line('Source: ' . ucfirst($this->review->source))
            ->action('View Review', url('/dashboard/reviews'))
            ->line('Thank you for maintaining high service standards!');
    }

    public function toArray($notifiable)
    {
        return [
            'review_id' => $this->review->id,
            'type' => 'new_review',
            'rating' => $this->review->rating,
            'message' => 'New review received',
            'date' => now()->format('Y-m-d H:i:s'),
        ];
    }
}