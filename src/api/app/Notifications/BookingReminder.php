<?php

namespace App\Notifications;

use App\Models\Booking;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class BookingReminder extends Notification
{
    use Queueable;

    protected $booking;

    public function __construct(Booking $booking)
    {
        $this->booking = $booking;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        $salon = $this->booking->salon;

        return (new MailMessage)
            ->subject('Appointment Reminder - ' . $salon->name)
            ->greeting('Hello ' . $notifiable->firstName . '!')
            ->line('This is a reminder for your upcoming appointment.')
            ->line('Details:')
            ->line('Date: ' . $this->booking->dateTime->format('l, F j, Y'))
            ->line('Time: ' . $this->booking->dateTime->format('g:i A'))
            ->line('Salon: ' . $salon->name)
            ->line('Address: ' . $salon->address)
            ->action('View Booking Details', url('/bookings/' . $this->booking->id))
            ->line('We look forward to seeing you!');
    }

    public function toArray($notifiable)
    {
        return [
            'booking_id' => $this->booking->id,
            'type' => 'booking_reminder',
            'message' => 'Reminder: You have an upcoming appointment',
            'date' => $this->booking->dateTime->format('Y-m-d H:i:s'),
        ];
    }
}