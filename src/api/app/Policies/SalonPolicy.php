<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Salon;

class SalonPolicy
{
    public function viewAny(User $user)
    {
        return $user->hasPermission('view-salons');
    }

    public function view(User $user, Salon $salon)
    {
        return $user->hasPermission('view-salons') ||
               $user->id === $salon->ownerId ||
               $user->id === $salon->managerId;
    }

    public function create(User $user)
    {
        return $user->hasPermission('create-salons');
    }

    public function update(User $user, Salon $salon)
    {
        return $user->hasPermission('edit-salons') ||
               $user->id === $salon->ownerId;
    }

    public function delete(User $user, Salon $salon)
    {
        return $user->hasPermission('delete-salons');
    }

    public function manage(User $user, Salon $salon)
    {
        return $user->id === $salon->ownerId ||
               $user->id === $salon->managerId;
    }
}