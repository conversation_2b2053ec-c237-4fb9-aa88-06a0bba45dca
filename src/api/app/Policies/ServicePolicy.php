<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Service;

class ServicePolicy
{
    public function viewAny(User $user)
    {
        return $user->hasPermission('view-services');
    }

    public function view(User $user, Service $service)
    {
        return $user->hasPermission('view-services');
    }

    public function create(User $user)
    {
        return $user->hasPermission('create-services');
    }

    public function update(User $user, Service $service)
    {
        // Pour les managers, vérifier qu'ils gèrent le salon du service
        if ($user->role === 'manager') {
            return $user->salonId === $service->salonId;
        }
        
        return $user->hasPermission('edit-services') ||
               $user->id === $service->salon->ownerId ||
               $user->id === $service->salon->managerId;
    }

    public function delete(User $user, Service $service)
    {
        // Pour les managers, vérifier qu'ils gèrent le salon du service
        if ($user->role === 'manager') {
            return $user->salonId === $service->salonId;
        }
        
        return $user->hasPermission('delete-services') ||
               $user->id === $service->salon->ownerId;
    }
}