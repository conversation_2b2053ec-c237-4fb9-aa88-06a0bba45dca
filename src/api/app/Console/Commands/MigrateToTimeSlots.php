<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Booking;
use App\Models\Hairdresser;
use App\Models\Salon;
use App\Services\TimeSlotService;
use App\Services\AvailabilityService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class MigrateToTimeSlots extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'timeslots:migrate 
                            {--dry-run : Exécuter en mode test sans modifications}
                            {--salon= : ID du salon spécifique à migrer}
                            {--hairdresser= : ID du coiffeur spécifique à migrer}
                            {--days= : Nombre de jours à l\'avance pour générer les créneaux (défaut: 30)}';

    /**
     * The console command description.
     */
    protected $description = 'Migrer les données existantes vers le nouveau système de créneaux horaires';

    protected $timeSlotService;
    protected $availabilityService;

    public function __construct(TimeSlotService $timeSlotService, AvailabilityService $availabilityService)
    {
        parent::__construct();
        $this->timeSlotService = $timeSlotService;
        $this->availabilityService = $availabilityService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Début de la migration vers le système de créneaux horaires');
        
        $dryRun = $this->option('dry-run');
        $salonId = $this->option('salon');
        $hairdresserId = $this->option('hairdresser');
        $days = $this->option('days') ?? 30;

        if ($dryRun) {
            $this->warn('⚠️  Mode test activé - Aucune modification ne sera effectuée');
        }

        try {
            // Étape 1: Créer les templates de disponibilité par défaut
            $this->info('📋 Étape 1: Création des templates de disponibilité...');
            $templatesCreated = $this->createDefaultTemplates($salonId, $hairdresserId, $dryRun);
            $this->info("✅ {$templatesCreated} templates créés");

            // Étape 2: Générer les créneaux futurs
            $this->info('🕐 Étape 2: Génération des créneaux futurs...');
            $slotsGenerated = $this->generateFutureSlots($salonId, $hairdresserId, $days, $dryRun);
            $this->info("✅ {$slotsGenerated} créneaux générés");

            // Étape 3: Migrer les réservations existantes
            $this->info('📅 Étape 3: Migration des réservations existantes...');
            $bookingsMigrated = $this->migrateExistingBookings($salonId, $hairdresserId, $dryRun);
            $this->info("✅ {$bookingsMigrated} réservations migrées");

            // Étape 4: Rapport final
            $this->displayMigrationReport($templatesCreated, $slotsGenerated, $bookingsMigrated);

            $this->info('🎉 Migration terminée avec succès !');

        } catch (\Exception $e) {
            $this->error('❌ Erreur lors de la migration: ' . $e->getMessage());
            $this->error($e->getTraceAsString());
            return 1;
        }

        return 0;
    }

    /**
     * Créer les templates de disponibilité par défaut
     */
    private function createDefaultTemplates($salonId, $hairdresserId, $dryRun)
    {
        $templatesCreated = 0;
        
        $query = Hairdresser::query()->active();
        
        if ($salonId) {
            $query->where('salonId', $salonId);
        }
        
        if ($hairdresserId) {
            $query->where('id', $hairdresserId);
        }

        $hairdressers = $query->get();

        $this->info("Traitement de {$hairdressers->count()} coiffeur(s)...");

        foreach ($hairdressers as $hairdresser) {
            $this->line("  - {$hairdresser->user->firstName} {$hairdresser->user->lastName}");

            // Vérifier s'il a déjà des templates
            $existingTemplates = $hairdresser->availabilityTemplates()->count();
            
            if ($existingTemplates > 0) {
                $this->warn("    ⚠️  Templates existants trouvés ({$existingTemplates}), ignoré");
                continue;
            }

            if (!$dryRun) {
                // Créer des templates par défaut pour lundi à vendredi
                $templates = $this->availabilityService->createDefaultTemplates($hairdresser, [1, 2, 3, 4, 5]);
                $templatesCreated += count($templates);
                $this->info("    ✅ " . count($templates) . " templates créés");
            } else {
                $templatesCreated += 5; // Simulation
                $this->info("    🔍 5 templates seraient créés");
            }
        }

        return $templatesCreated;
    }

    /**
     * Générer les créneaux futurs
     */
    private function generateFutureSlots($salonId, $hairdresserId, $days, $dryRun)
    {
        $slotsGenerated = 0;
        $startDate = now()->toDateString();
        $endDate = now()->addDays($days)->toDateString();

        if ($salonId) {
            $salon = Salon::find($salonId);
            if (!$salon) {
                throw new \Exception("Salon avec ID {$salonId} non trouvé");
            }

            if (!$dryRun) {
                $generated = $this->timeSlotService->generateTimeSlotsForSalon($salon, $startDate, $endDate);
                $slotsGenerated += $generated;
            } else {
                // Estimation pour le mode test
                $hairdressersCount = $salon->hairdressers()->active()->count();
                $slotsGenerated += $hairdressersCount * $days * 16; // Estimation: 16 créneaux par jour
            }

        } elseif ($hairdresserId) {
            $hairdresser = Hairdresser::find($hairdresserId);
            if (!$hairdresser) {
                throw new \Exception("Coiffeur avec ID {$hairdresserId} non trouvé");
            }

            if (!$dryRun) {
                $generated = $this->timeSlotService->generateTimeSlotsForHairdresser($hairdresser, $startDate, $endDate);
                $slotsGenerated += $generated;
            } else {
                $slotsGenerated += $days * 16; // Estimation
            }

        } else {
            // Tous les salons
            $salons = Salon::where('status', 'active')->get();
            
            foreach ($salons as $salon) {
                $this->line("  - Salon: {$salon->name}");
                
                if (!$dryRun) {
                    $generated = $this->timeSlotService->generateTimeSlotsForSalon($salon, $startDate, $endDate);
                    $slotsGenerated += $generated;
                    $this->info("    ✅ {$generated} créneaux générés");
                } else {
                    $hairdressersCount = $salon->hairdressers()->active()->count();
                    $estimated = $hairdressersCount * $days * 16;
                    $slotsGenerated += $estimated;
                    $this->info("    🔍 {$estimated} créneaux seraient générés");
                }
            }
        }

        return $slotsGenerated;
    }

    /**
     * Migrer les réservations existantes
     */
    private function migrateExistingBookings($salonId, $hairdresserId, $dryRun)
    {
        $bookingsMigrated = 0;
        
        $query = Booking::whereNull('time_slot_id')
            ->whereNotNull('dateTime')
            ->where('dateTime', '>=', now())
            ->whereIn('status', ['pending', 'confirmed']);

        if ($salonId) {
            $query->where('salonId', $salonId);
        }

        if ($hairdresserId) {
            $query->where('hairdresserId', $hairdresserId);
        }

        $bookings = $query->get();

        $this->info("Migration de {$bookings->count()} réservation(s)...");

        foreach ($bookings as $booking) {
            $this->line("  - Réservation #{$booking->id} - {$booking->dateTime}");

            try {
                if (!$dryRun) {
                    $migrated = $this->migrateBooking($booking);
                    if ($migrated) {
                        $bookingsMigrated++;
                        $this->info("    ✅ Migrée vers créneau #{$booking->time_slot_id}");
                    } else {
                        $this->warn("    ⚠️  Impossible de migrer - créneau non trouvé");
                    }
                } else {
                    $bookingsMigrated++;
                    $this->info("    🔍 Serait migrée");
                }

            } catch (\Exception $e) {
                $this->error("    ❌ Erreur: " . $e->getMessage());
            }
        }

        return $bookingsMigrated;
    }

    /**
     * Migrer une réservation spécifique
     */
    private function migrateBooking(Booking $booking)
    {
        return DB::transaction(function () use ($booking) {
            $date = $booking->dateTime->toDateString();
            $startTime = $booking->dateTime->format('H:i:s');

            // Calculer la durée totale si pas déjà définie
            if (!$booking->total_duration) {
                $totalDuration = $this->timeSlotService->calculateTotalServiceDuration(
                    $booking->services->pluck('id')->toArray()
                );
                $booking->total_duration = $totalDuration;
                $booking->end_time = $booking->dateTime->copy()->addMinutes($totalDuration);
            }

            // Chercher un créneau correspondant
            $timeSlot = \App\Models\TimeSlot::where('hairdresser_id', $booking->hairdresserId)
                ->where('date', $date)
                ->where('start_time', $startTime)
                ->where('status', 'available')
                ->first();

            if ($timeSlot) {
                // Assigner le créneau
                $booking->assignTimeSlot($timeSlot);
                $booking->save();
                return true;
            }

            return false;
        });
    }

    /**
     * Afficher le rapport de migration
     */
    private function displayMigrationReport($templatesCreated, $slotsGenerated, $bookingsMigrated)
    {
        $this->info('');
        $this->info('📊 RAPPORT DE MIGRATION');
        $this->info('========================');
        $this->table(
            ['Élément', 'Quantité'],
            [
                ['Templates créés', $templatesCreated],
                ['Créneaux générés', $slotsGenerated],
                ['Réservations migrées', $bookingsMigrated],
            ]
        );

        // Statistiques supplémentaires
        $totalBookings = Booking::whereNotNull('time_slot_id')->count();
        $totalSlots = \App\Models\TimeSlot::count();
        $availableSlots = \App\Models\TimeSlot::available()->count();

        $this->info('');
        $this->info('📈 STATISTIQUES ACTUELLES');
        $this->info('=========================');
        $this->table(
            ['Métrique', 'Valeur'],
            [
                ['Total réservations avec créneaux', $totalBookings],
                ['Total créneaux', $totalSlots],
                ['Créneaux disponibles', $availableSlots],
                ['Taux d\'occupation', $totalSlots > 0 ? round((($totalSlots - $availableSlots) / $totalSlots) * 100, 2) . '%' : '0%'],
            ]
        );
    }
}
