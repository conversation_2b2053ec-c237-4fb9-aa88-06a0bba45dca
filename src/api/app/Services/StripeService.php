<?php

namespace App\Services;

use App\Models\Payment;
use App\Models\PaymentMethod;
use App\Models\User;
use App\Models\Booking;
use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Customer;
use Stripe\PaymentMethod as StripePaymentMethod;
use Stripe\Refund;
use Exception;

class StripeService
{
    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Créer ou récupérer un client Stripe
     */
    public function getOrCreateCustomer(User $user)
    {
        if ($user->stripe_customer_id) {
            try {
                return Customer::retrieve($user->stripe_customer_id);
            } catch (Exception $e) {
                // Si le client n'existe plus, on en crée un nouveau
            }
        }

        $customer = Customer::create([
            'email' => $user->email,
            'name' => $user->firstName . ' ' . $user->lastName,
            'phone' => $user->phone,
            'metadata' => [
                'user_id' => $user->id,
            ],
        ]);

        $user->update(['stripe_customer_id' => $customer->id]);

        return $customer;
    }

    /**
     * Créer un Payment Intent pour une réservation
     */
    public function createPaymentIntent(Booking $booking, $paymentMethodId = null)
    {
        $customer = $this->getOrCreateCustomer($booking->client);

        $paymentIntentData = [
            'amount' => $this->convertToStripeAmount($booking->total_amount),
            'currency' => 'eur',
            'customer' => $customer->id,
            'metadata' => [
                'booking_id' => $booking->id,
                'user_id' => $booking->clientId,
                'salon_id' => $booking->salonId,
            ],
            'description' => "Réservation salon #{$booking->id}",
        ];

        if ($paymentMethodId) {
            $paymentIntentData['payment_method'] = $paymentMethodId;
            $paymentIntentData['confirmation_method'] = 'manual';
            $paymentIntentData['confirm'] = true;
        }

        $paymentIntent = PaymentIntent::create($paymentIntentData);

        // Créer l'enregistrement de paiement dans notre base
        $payment = Payment::create([
            'booking_id' => $booking->id,
            'user_id' => $booking->clientId,
            'payment_intent_id' => $paymentIntent->id,
            'provider' => Payment::PROVIDER_STRIPE,
            'status' => $this->mapStripeStatus($paymentIntent->status),
            'amount' => $booking->total_amount,
            'currency' => 'eur',
            'metadata' => [
                'stripe_payment_intent' => $paymentIntent->toArray(),
            ],
        ]);

        return [
            'payment_intent' => $paymentIntent,
            'payment' => $payment,
        ];
    }

    /**
     * Confirmer un Payment Intent
     */
    public function confirmPaymentIntent($paymentIntentId, $paymentMethodId = null)
    {
        $confirmData = [];
        
        if ($paymentMethodId) {
            $confirmData['payment_method'] = $paymentMethodId;
        }

        $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
        $paymentIntent = $paymentIntent->confirm($confirmData);

        // Mettre à jour notre enregistrement de paiement
        $payment = Payment::where('payment_intent_id', $paymentIntentId)->first();
        if ($payment) {
            $payment->update([
                'status' => $this->mapStripeStatus($paymentIntent->status),
                'metadata' => array_merge($payment->metadata ?? [], [
                    'stripe_payment_intent' => $paymentIntent->toArray(),
                ]),
            ]);

            if ($paymentIntent->status === 'succeeded') {
                $payment->markAsPaid();
            }
        }

        return $paymentIntent;
    }

    /**
     * Sauvegarder une méthode de paiement
     */
    public function savePaymentMethod(User $user, $paymentMethodId, $makeDefault = false)
    {
        $customer = $this->getOrCreateCustomer($user);
        
        // Attacher la méthode de paiement au client
        $stripePaymentMethod = StripePaymentMethod::retrieve($paymentMethodId);
        $stripePaymentMethod->attach(['customer' => $customer->id]);

        // Créer l'enregistrement dans notre base
        $paymentMethod = PaymentMethod::create([
            'user_id' => $user->id,
            'provider' => PaymentMethod::PROVIDER_STRIPE,
            'provider_id' => $paymentMethodId,
            'type' => $this->mapStripePaymentMethodType($stripePaymentMethod->type),
            'last_four' => $stripePaymentMethod->card->last4 ?? null,
            'brand' => $stripePaymentMethod->card->brand ?? null,
            'exp_month' => $stripePaymentMethod->card->exp_month ?? null,
            'exp_year' => $stripePaymentMethod->card->exp_year ?? null,
            'country' => $stripePaymentMethod->card->country ?? null,
            'billing_details' => $stripePaymentMethod->billing_details->toArray(),
            'is_default' => $makeDefault,
            'is_active' => true,
        ]);

        if ($makeDefault) {
            $paymentMethod->makeDefault();
        }

        return $paymentMethod;
    }

    /**
     * Créer un remboursement
     */
    public function createRefund(Payment $payment, $amount = null)
    {
        if (!$payment->isStripe() || !$payment->isSuccessful()) {
            throw new Exception('Ce paiement ne peut pas être remboursé');
        }

        $refundAmount = $amount ?? $payment->getRemainingRefundAmount();
        
        $refund = Refund::create([
            'payment_intent' => $payment->payment_intent_id,
            'amount' => $this->convertToStripeAmount($refundAmount),
            'metadata' => [
                'booking_id' => $payment->booking_id,
                'user_id' => $payment->user_id,
            ],
        ]);

        $payment->markAsRefunded($refundAmount);

        return $refund;
    }

    /**
     * Supprimer une méthode de paiement
     */
    public function deletePaymentMethod(PaymentMethod $paymentMethod)
    {
        if (!$paymentMethod->isStripe()) {
            throw new Exception('Cette méthode de paiement n\'est pas gérée par Stripe');
        }

        try {
            $stripePaymentMethod = StripePaymentMethod::retrieve($paymentMethod->provider_id);
            $stripePaymentMethod->detach();
        } catch (Exception $e) {
            // La méthode n'existe peut-être plus chez Stripe
        }

        $paymentMethod->delete();
    }

    /**
     * Convertir un montant en centimes pour Stripe
     */
    private function convertToStripeAmount($amount)
    {
        return (int) ($amount * 100);
    }

    /**
     * Mapper le statut Stripe vers notre statut
     */
    private function mapStripeStatus($stripeStatus)
    {
        $statusMap = [
            'requires_payment_method' => Payment::STATUS_PENDING,
            'requires_confirmation' => Payment::STATUS_PENDING,
            'requires_action' => Payment::STATUS_PENDING,
            'processing' => Payment::STATUS_PROCESSING,
            'succeeded' => Payment::STATUS_SUCCEEDED,
            'canceled' => Payment::STATUS_CANCELED,
        ];

        return $statusMap[$stripeStatus] ?? Payment::STATUS_FAILED;
    }

    /**
     * Mapper le type de méthode de paiement Stripe
     */
    private function mapStripePaymentMethodType($stripeType)
    {
        $typeMap = [
            'card' => PaymentMethod::TYPE_CARD,
            'sepa_debit' => PaymentMethod::TYPE_BANK_ACCOUNT,
        ];

        return $typeMap[$stripeType] ?? PaymentMethod::TYPE_CARD;
    }

    /**
     * Récupérer les détails d'un Payment Intent
     */
    public function getPaymentIntent($paymentIntentId)
    {
        return PaymentIntent::retrieve($paymentIntentId);
    }

    /**
     * Annuler un Payment Intent
     */
    public function cancelPaymentIntent($paymentIntentId)
    {
        $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
        $paymentIntent = $paymentIntent->cancel();

        // Mettre à jour notre enregistrement
        $payment = Payment::where('payment_intent_id', $paymentIntentId)->first();
        if ($payment) {
            $payment->update(['status' => Payment::STATUS_CANCELED]);
        }

        return $paymentIntent;
    }
}
