<?php

namespace App\Services;

use App\Models\Payment;
use App\Models\PaymentMethod;
use App\Models\User;
use App\Models\Booking;
use Illuminate\Support\Facades\Http;
use Exception;

class PayPalService
{
    private $baseUrl;
    private $clientId;
    private $clientSecret;

    public function __construct()
    {
        $this->baseUrl = config('services.paypal.mode') === 'live'
            ? 'https://api.paypal.com'
            : 'https://api.sandbox.paypal.com';

        $this->clientId = config('services.paypal.client_id');
        $this->clientSecret = config('services.paypal.client_secret');
    }

    /**
     * Obtenir un token d'accès PayPal
     */
    private function getAccessToken()
    {
        $response = Http::withBasicAuth($this->clientId, $this->clientSecret)
            ->asForm()
            ->post($this->baseUrl . '/v1/oauth2/token', [
                'grant_type' => 'client_credentials'
            ]);

        if ($response->failed()) {
            throw new Exception('Impossible d\'obtenir le token PayPal');
        }

        return $response->json()['access_token'];
    }

    /**
     * Créer une commande PayPal pour une réservation
     */
    public function createOrder(Booking $booking, $returnUrl, $cancelUrl)
    {
        $token = $this->getAccessToken();

        $orderData = [
            'intent' => 'CAPTURE',
            'purchase_units' => [
                [
                    'reference_id' => "booking_{$booking->id}",
                    'amount' => [
                        'currency_code' => 'EUR',
                        'value' => number_format($booking->total_amount, 2, '.', ''),
                    ],
                    'description' => "Réservation salon #{$booking->id}",
                    'custom_id' => (string) $booking->id,
                ]
            ],
            'application_context' => [
                'return_url' => $returnUrl,
                'cancel_url' => $cancelUrl,
                'brand_name' => config('app.name'),
                'locale' => 'fr-FR',
                'landing_page' => 'BILLING',
                'shipping_preference' => 'NO_SHIPPING',
                'user_action' => 'PAY_NOW',
            ]
        ];

        $response = Http::withToken($token)
            ->post($this->baseUrl . '/v2/checkout/orders', $orderData);

        if ($response->failed()) {
            throw new Exception('Erreur lors de la création de la commande PayPal: ' . $response->body());
        }

        $order = $response->json();

        // Créer l'enregistrement de paiement dans notre base
        $payment = Payment::create([
            'booking_id' => $booking->id,
            'user_id' => $booking->clientId,
            'paypal_order_id' => $order['id'],
            'provider' => Payment::PROVIDER_PAYPAL,
            'status' => $this->mapPayPalStatus($order['status']),
            'amount' => $booking->total_amount,
            'currency' => 'eur',
            'metadata' => [
                'paypal_order' => $order,
            ],
        ]);

        return [
            'order' => $order,
            'payment' => $payment,
        ];
    }

    /**
     * Capturer une commande PayPal
     */
    public function captureOrder($orderId)
    {
        $token = $this->getAccessToken();

        $response = Http::withToken($token)
            ->post($this->baseUrl . "/v2/checkout/orders/{$orderId}/capture");

        if ($response->failed()) {
            // Marquer le paiement comme échoué
            $payment = Payment::where('paypal_order_id', $orderId)->first();
            if ($payment) {
                $payment->markAsFailed($response->body());
            }

            throw new Exception('Erreur lors de la capture PayPal: ' . $response->body());
        }

        $captureData = $response->json();

        // Mettre à jour notre enregistrement de paiement
        $payment = Payment::where('paypal_order_id', $orderId)->first();
        if ($payment) {
            $payment->update([
                'status' => $this->mapPayPalStatus($captureData['status']),
                'metadata' => array_merge($payment->metadata ?? [], [
                    'paypal_capture' => $captureData,
                ]),
            ]);

            if ($captureData['status'] === 'COMPLETED') {
                $payment->markAsPaid();

                // Extraire les frais PayPal si disponibles
                $capture = $captureData['purchase_units'][0]['payments']['captures'][0] ?? null;
                if ($capture && isset($capture['seller_receivable_breakdown'])) {
                    $breakdown = $capture['seller_receivable_breakdown'];
                    $payment->update([
                        'fee_amount' => $breakdown['paypal_fee']['value'] ?? 0,
                        'net_amount' => $breakdown['net_amount']['value'] ?? $payment->amount,
                    ]);
                }
            }
        }

        return $captureData;
    }

    /**
     * Récupérer les détails d'une commande
     */
    public function getOrder($orderId)
    {
        $token = $this->getAccessToken();

        $response = Http::withToken($token)
            ->get($this->baseUrl . "/v2/checkout/orders/{$orderId}");

        if ($response->failed()) {
            throw new Exception('Erreur lors de la récupération de la commande PayPal: ' . $response->body());
        }

        return $response->json();
    }

    /**
     * Créer un remboursement
     */
    public function createRefund(Payment $payment, $amount = null)
    {
        if (!$payment->isPayPal() || !$payment->isSuccessful()) {
            throw new Exception('Ce paiement ne peut pas être remboursé');
        }

        $refundAmount = $amount ?? $payment->getRemainingRefundAmount();

        // Récupérer l'ID de capture depuis les métadonnées
        $captureId = $this->getCaptureIdFromPayment($payment);
        if (!$captureId) {
            throw new Exception('ID de capture PayPal introuvable');
        }

        $token = $this->getAccessToken();

        $refundData = [
            'amount' => [
                'currency_code' => strtoupper($payment->currency),
                'value' => number_format($refundAmount, 2, '.', ''),
            ],
            'note_to_payer' => 'Remboursement de votre réservation',
        ];

        $response = Http::withToken($token)
            ->post($this->baseUrl . "/v2/payments/captures/{$captureId}/refund", $refundData);

        if ($response->failed()) {
            throw new Exception('Erreur lors du remboursement PayPal: ' . $response->body());
        }

        $refundResult = $response->json();
        $payment->markAsRefunded($refundAmount);

        return $refundResult;
    }

    /**
     * Sauvegarder une méthode de paiement PayPal (simulation)
     * Note: PayPal ne permet pas de sauvegarder des méthodes de paiement de la même manière que Stripe
     */
    public function savePaymentMethod(User $user, $paypalAccountEmail, $makeDefault = false)
    {
        $paymentMethod = PaymentMethod::create([
            'user_id' => $user->id,
            'provider' => PaymentMethod::PROVIDER_PAYPAL,
            'provider_id' => 'paypal_' . $user->id . '_' . time(),
            'type' => PaymentMethod::TYPE_PAYPAL_ACCOUNT,
            'billing_details' => [
                'email' => $paypalAccountEmail,
            ],
            'is_default' => $makeDefault,
            'is_active' => true,
        ]);

        if ($makeDefault) {
            $paymentMethod->makeDefault();
        }

        return $paymentMethod;
    }

    /**
     * Mapper le statut PayPal vers notre statut
     */
    private function mapPayPalStatus($paypalStatus)
    {
        $statusMap = [
            'CREATED' => Payment::STATUS_PENDING,
            'SAVED' => Payment::STATUS_PENDING,
            'APPROVED' => Payment::STATUS_PENDING,
            'VOIDED' => Payment::STATUS_CANCELED,
            'COMPLETED' => Payment::STATUS_SUCCEEDED,
            'PAYER_ACTION_REQUIRED' => Payment::STATUS_PENDING,
        ];

        return $statusMap[$paypalStatus] ?? Payment::STATUS_FAILED;
    }

    /**
     * Extraire l'ID de capture depuis les métadonnées du paiement
     */
    private function getCaptureIdFromPayment(Payment $payment)
    {
        $metadata = $payment->metadata;
        
        if (isset($metadata['paypal_capture']['purchase_units'][0]['payments']['captures'][0]['id'])) {
            return $metadata['paypal_capture']['purchase_units'][0]['payments']['captures'][0]['id'];
        }

        return null;
    }

    /**
     * Vérifier si PayPal est configuré
     */
    public function isConfigured()
    {
        return config('services.paypal.client_id') && config('services.paypal.client_secret');
    }

    /**
     * Obtenir l'URL d'approbation depuis une commande
     */
    public function getApprovalUrl($order)
    {
        foreach ($order->links as $link) {
            if ($link->rel === 'approve') {
                return $link->href;
            }
        }
        
        return null;
    }
}
