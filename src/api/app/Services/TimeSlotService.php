<?php

namespace App\Services;

use App\Models\AvailabilityTemplate;
use App\Models\TimeSlot;
use App\Models\SlotException;
use App\Models\Hairdresser;
use App\Models\Salon;
use App\Models\Service;
use App\Models\Booking;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class TimeSlotService
{
    /**
     * Générer les créneaux pour un coiffeur sur une période donnée
     */
    public function generateTimeSlotsForHairdresser(Hairdresser $hairdresser, $startDate, $endDate)
    {
        $generatedSlots = [];
        $current = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);

        while ($current->lte($end)) {
            $dayOfWeek = $current->dayOfWeek;
            
            // Récupérer le template de disponibilité pour ce jour
            $template = $hairdresser->getAvailabilityTemplateForDay($dayOfWeek);
            
            if ($template && $template->is_active) {
                $slots = $this->generateSlotsForDay($template, $current->toDateString());
                $generatedSlots = array_merge($generatedSlots, $slots);
            }

            $current->addDay();
        }

        // Insérer les créneaux en batch pour optimiser les performances
        if (!empty($generatedSlots)) {
            TimeSlot::insert($generatedSlots);
        }

        return count($generatedSlots);
    }

    /**
     * Générer les créneaux pour tous les coiffeurs d'un salon
     */
    public function generateTimeSlotsForSalon(Salon $salon, $startDate, $endDate)
    {
        $totalGenerated = 0;
        
        $hairdressers = $salon->hairdressers()->active()->get();
        
        foreach ($hairdressers as $hairdresser) {
            $generated = $this->generateTimeSlotsForHairdresser($hairdresser, $startDate, $endDate);
            $totalGenerated += $generated;
        }

        return $totalGenerated;
    }

    /**
     * Générer les créneaux pour une journée spécifique
     */
    private function generateSlotsForDay(AvailabilityTemplate $template, $date)
    {
        // Vérifier si des créneaux existent déjà pour cette date
        $existingSlots = TimeSlot::where('hairdresser_id', $template->hairdresser_id)
            ->where('date', $date)
            ->exists();

        if ($existingSlots) {
            return []; // Ne pas générer si des créneaux existent déjà
        }

        $slots = $template->generateTimeSlots($date);
        
        // Appliquer les exceptions pour cette date
        $exceptions = SlotException::where('hairdresser_id', $template->hairdresser_id)
            ->where('date', $date)
            ->active()
            ->get();

        $filteredSlots = [];
        foreach ($slots as $slot) {
            $isBlocked = false;
            
            foreach ($exceptions as $exception) {
                if ($exception->affectsTimeSlot((object) $slot)) {
                    $isBlocked = true;
                    break;
                }
            }

            if (!$isBlocked) {
                $slot['created_at'] = now();
                $slot['updated_at'] = now();
                $filteredSlots[] = $slot;
            }
        }

        return $filteredSlots;
    }

    /**
     * Trouver les créneaux disponibles pour une réservation
     */
    public function findAvailableSlots($salonId, $hairdresserId = null, $date = null, $duration = null, $serviceIds = [])
    {
        $query = TimeSlot::query()
            ->available()
            ->where('salon_id', $salonId)
            ->upcoming()
            ->with(['hairdresser.user']);

        if ($hairdresserId) {
            $query->where('hairdresser_id', $hairdresserId);
        }

        if ($date) {
            $query->where('date', $date);
        }

        // Calculer la durée totale nécessaire si des services sont fournis
        if (!empty($serviceIds)) {
            $totalDuration = $this->calculateTotalServiceDuration($serviceIds);
            $query->where('duration', '>=', $totalDuration);
        } elseif ($duration) {
            $query->where('duration', '>=', $duration);
        }

        return $query->orderBy('date')
                    ->orderBy('start_time')
                    ->get();
    }

    /**
     * Calculer la durée totale des services
     */
    public function calculateTotalServiceDuration(array $serviceIds)
    {
        $services = Service::whereIn('id', $serviceIds)->get();
        
        return $services->sum(function ($service) {
            return $service->duration + 
                   ($service->preparation_time ?? 0) + 
                   ($service->cleanup_time ?? 0);
        });
    }

    /**
     * Réserver un créneau
     */
    public function bookTimeSlot(TimeSlot $timeSlot, Booking $booking)
    {
        return DB::transaction(function () use ($timeSlot, $booking) {
            if (!$timeSlot->canBeBooked()) {
                throw new \Exception('Ce créneau n\'est plus disponible');
            }

            $timeSlot->markAsBooked($booking);
            $booking->assignTimeSlot($timeSlot);

            return true;
        });
    }

    /**
     * Libérer un créneau
     */
    public function releaseTimeSlot(TimeSlot $timeSlot)
    {
        return DB::transaction(function () use ($timeSlot) {
            if ($timeSlot->booking) {
                $timeSlot->booking->releaseTimeSlot();
            }
            
            $timeSlot->markAsAvailable();
            
            return true;
        });
    }

    /**
     * Vérifier la disponibilité pour une réservation
     */
    public function checkAvailability($hairdresserId, $date, $startTime, $duration, $excludeBookingId = null)
    {
        $endTime = Carbon::parse($startTime)->addMinutes($duration)->format('H:i:s');
        
        // Vérifier les créneaux existants
        $conflictingSlots = TimeSlot::where('hairdresser_id', $hairdresserId)
            ->where('date', $date)
            ->where('status', '!=', TimeSlot::STATUS_AVAILABLE)
            ->where(function ($query) use ($startTime, $endTime) {
                $query->where('start_time', '<', $endTime)
                      ->where('end_time', '>', $startTime);
            });

        if ($excludeBookingId) {
            $conflictingSlots->whereHas('booking', function ($query) use ($excludeBookingId) {
                $query->where('id', '!=', $excludeBookingId);
            });
        }

        if ($conflictingSlots->exists()) {
            return false;
        }

        // Vérifier les exceptions
        $hairdresser = Hairdresser::find($hairdresserId);
        if ($hairdresser->hasExceptionForDate($date, $startTime, $endTime)) {
            return false;
        }

        // Vérifier les heures de travail
        return $hairdresser->isAvailableForTimeSlot($date, $startTime, $endTime);
    }

    /**
     * Obtenir les créneaux disponibles pour une semaine
     */
    public function getWeeklyAvailability($salonId, $startDate, $hairdresserId = null, $duration = null)
    {
        $endDate = Carbon::parse($startDate)->addDays(6)->toDateString();
        
        $slots = $this->findAvailableSlots($salonId, $hairdresserId, null, $duration)
            ->whereBetween('date', [$startDate, $endDate])
            ->groupBy('date');

        $weeklyData = [];
        $current = Carbon::parse($startDate);
        
        for ($i = 0; $i < 7; $i++) {
            $dateKey = $current->toDateString();
            $weeklyData[$dateKey] = [
                'date' => $dateKey,
                'day_name' => $current->locale('fr')->dayName,
                'slots' => $slots->get($dateKey, collect())->values()
            ];
            $current->addDay();
        }

        return $weeklyData;
    }

    /**
     * Bloquer des créneaux (maintenance, congés, etc.)
     */
    public function blockTimeSlots($hairdresserId, $startDate, $endDate, $startTime = null, $endTime = null, $reason = null)
    {
        $query = TimeSlot::where('hairdresser_id', $hairdresserId)
            ->whereBetween('date', [$startDate, $endDate])
            ->where('status', TimeSlot::STATUS_AVAILABLE);

        if ($startTime && $endTime) {
            $query->where('start_time', '>=', $startTime)
                  ->where('end_time', '<=', $endTime);
        }

        return $query->update([
            'status' => TimeSlot::STATUS_BLOCKED,
            'notes' => $reason
        ]);
    }

    /**
     * Débloquer des créneaux
     */
    public function unblockTimeSlots($hairdresserId, $startDate, $endDate, $startTime = null, $endTime = null)
    {
        $query = TimeSlot::where('hairdresser_id', $hairdresserId)
            ->whereBetween('date', [$startDate, $endDate])
            ->where('status', TimeSlot::STATUS_BLOCKED);

        if ($startTime && $endTime) {
            $query->where('start_time', '>=', $startTime)
                  ->where('end_time', '<=', $endTime);
        }

        return $query->update([
            'status' => TimeSlot::STATUS_AVAILABLE,
            'notes' => null
        ]);
    }

    /**
     * Nettoyer les anciens créneaux
     */
    public function cleanupOldSlots($beforeDate = null)
    {
        $cutoffDate = $beforeDate ?? Carbon::now()->subDays(30)->toDateString();
        
        return TimeSlot::where('date', '<', $cutoffDate)->delete();
    }

    /**
     * Obtenir les statistiques d'occupation
     */
    public function getOccupancyStats($salonId, $startDate, $endDate, $hairdresserId = null)
    {
        $query = TimeSlot::where('salon_id', $salonId)
            ->whereBetween('date', [$startDate, $endDate]);

        if ($hairdresserId) {
            $query->where('hairdresser_id', $hairdresserId);
        }

        $total = $query->count();
        $booked = $query->where('status', TimeSlot::STATUS_BOOKED)->count();
        $available = $query->where('status', TimeSlot::STATUS_AVAILABLE)->count();
        $blocked = $query->where('status', TimeSlot::STATUS_BLOCKED)->count();

        return [
            'total' => $total,
            'booked' => $booked,
            'available' => $available,
            'blocked' => $blocked,
            'occupancy_rate' => $total > 0 ? round(($booked / $total) * 100, 2) : 0
        ];
    }
}
