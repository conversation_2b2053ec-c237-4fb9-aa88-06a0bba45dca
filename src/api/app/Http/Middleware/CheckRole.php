<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CheckRole
{
    public function handle(Request $request, Closure $next, $role, $guard = null)
    {
        if (!$request->user()) {
            return response()->json([
                'message' => 'Unauthorized access'
            ], 403);
        }

        $roles = is_array($role)
            ? $role
            : explode('|', $role);

        // Vérifier directement le champ 'role' de l'utilisateur
        if (!in_array($request->user()->role, $roles)) {
            return response()->json([
                'message' => 'Unauthorized access'
            ], 403);
        }

        return $next($request);
    }
}