<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Salon;
use App\Models\Service;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ReportController extends Controller
{
    public function getFinancialReport(Request $request)
    {
        $startDate = $request->get('start_date', now()->startOfMonth());
        $endDate = $request->get('end_date', now()->endOfMonth());

        $report = [
            'total_revenue' => Booking::whereBetween('created_at', [$startDate, $endDate])
                                    ->where('status', 'completed')
                                    ->sum('total_amount'),
            'revenue_by_salon' => Salon::withSum(['bookings' => function ($query) use ($startDate, $endDate) {
                                    $query->whereBetween('created_at', [$startDate, $endDate])
                                          ->where('status', 'completed');
                                }], 'total_amount')
                                ->get(),
            'revenue_by_service' => Service::withSum(['bookings' => function ($query) use ($startDate, $endDate) {
                                    $query->whereBetween('created_at', [$startDate, $endDate])
                                          ->where('status', 'completed');
                                }], 'total_amount')
                                ->get(),
            'daily_revenue' => Booking::where('status', 'completed')
                                    ->whereBetween('created_at', [$startDate, $endDate])
                                    ->selectRaw('DATE(created_at) as date, SUM(total_amount) as total')
                                    ->groupBy('date')
                                    ->get(),
        ];

        return response()->json($report);
    }

    public function getBookingReport(Request $request)
    {
        $startDate = $request->get('start_date', now()->startOfMonth());
        $endDate = $request->get('end_date', now()->endOfMonth());

        $report = [
            'total_bookings' => Booking::whereBetween('created_at', [$startDate, $endDate])->count(),
            'bookings_by_status' => Booking::whereBetween('created_at', [$startDate, $endDate])
                                         ->selectRaw('status, COUNT(*) as count')
                                         ->groupBy('status')
                                         ->get(),
            'bookings_by_salon' => Salon::withCount(['bookings' => function ($query) use ($startDate, $endDate) {
                                    $query->whereBetween('created_at', [$startDate, $endDate]);
                                }])
                                ->get(),
            'daily_bookings' => Booking::whereBetween('created_at', [$startDate, $endDate])
                                     ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                                     ->groupBy('date')
                                     ->get(),
        ];

        return response()->json($report);
    }

    public function getCustomerReport(Request $request)
    {
        $report = [
            'total_customers' => User::where('role', 'client')->count(),
            'new_customers' => User::where('role', 'client')
                                 ->where('created_at', '>=', now()->subDays(30))
                                 ->count(),
            'customer_demographics' => User::where('role', 'client')
                                        ->selectRaw('gender, COUNT(*) as count')
                                        ->groupBy('gender')
                                        ->get(),
            'top_customers' => User::where('role', 'client')
                                 ->withCount('bookings')
                                 ->orderBy('bookings_count', 'desc')
                                 ->take(10)
                                 ->get(),
            'customer_retention' => $this->calculateCustomerRetention(),
        ];

        return response()->json($report);
    }

    public function getServiceReport(Request $request)
    {
        $report = [
            'popular_services' => Service::withCount('bookings')
                                      ->orderBy('bookings_count', 'desc')
                                      ->take(10)
                                      ->get(),
            'service_categories' => Service::selectRaw('category, COUNT(*) as count')
                                        ->groupBy('category')
                                        ->get(),
            'average_service_duration' => Service::avg('duration'),
            'service_revenue' => Service::withSum('bookings', 'total_amount')
                                     ->orderBy('bookings_sum_total_amount', 'desc')
                                     ->get(),
        ];

        return response()->json($report);
    }

    private function calculateCustomerRetention()
    {
        $totalCustomers = User::where('role', 'client')->count();
        $repeatCustomers = User::where('role', 'client')
                             ->whereHas('bookings', function ($query) {
                                 $query->havingRaw('COUNT(*) > 1');
                             })
                             ->count();

        return [
            'total_customers' => $totalCustomers,
            'repeat_customers' => $repeatCustomers,
            'retention_rate' => $totalCustomers > 0 ? ($repeatCustomers / $totalCustomers) * 100 : 0,
        ];
    }

    public function exportReport(Request $request)
    {
        $validated = $request->validate([
            'report_type' => 'required|in:financial,booking,customer,service',
            'format' => 'required|in:csv,pdf',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
        ]);

        // Generate report based on type
        switch ($validated['report_type']) {
            case 'financial':
                $data = $this->getFinancialReport($request);
                break;
            case 'booking':
                $data = $this->getBookingReport($request);
                break;
            case 'customer':
                $data = $this->getCustomerReport($request);
                break;
            case 'service':
                $data = $this->getServiceReport($request);
                break;
        }

        // Export logic would go here
        // For now, return the data
        return response()->json([
            'message' => 'Report generated successfully',
            'data' => $data,
        ]);
    }
}