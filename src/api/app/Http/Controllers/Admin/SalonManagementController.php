<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Salon;
use Illuminate\Http\Request;
use App\Http\Resources\SalonResource;

class SalonManagementController extends Controller
{
    public function index(Request $request)
    {
        $query = Salon::query();

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('address', 'like', "%{$search}%");
            });
        }

        if ($request->has('rating')) {
            $query->where('rating', '>=', $request->rating);
        }

        $salons = $query->paginate(10);
        return SalonResource::collection($salons);
    }

    // public function store(Request $request)
    // {
    //     $validated = $request->validate([
    //         'name' => 'required|string|max:255',
    //         'address' => 'required|string',
    //         'hours' => 'required|array',
    //         'contact' => 'required|array',
    //         'contact.phone' => 'required|string',
    //         'contact.email' => 'required|email',
    //         'images' => 'nullable|string|max:255',
    //     ]);

    //     // Si une image est fournie en tant que chemin absolu, elle est copiée
    //     if (!empty($validated['images']) && file_exists($validated['images'])) {
    //         // Générer un nouveau nom de fichier basé sur le nom original
    //         $filename = 's' . basename($validated['images']);

    //         // Définir le chemin de destination
    //         $destinationPath = public_path('storage/salon_photos/' . $filename);

    //         // Créer le dossier s'il n'existe pas
    //         if (!file_exists(dirname($destinationPath))) {
    //             mkdir(dirname($destinationPath), 0755, true);
    //         }

    //         // Copier le fichier
    //         copy($validated['images'], $destinationPath);

    //         // Mettre à jour le chemin de l'image dans les données validées
    //         $validated['images'] = 'salon_photos/' . $filename;
    //     }

    //     // Création du salon
    //     $salon = Salon::create($validated);

    //     return new SalonResource($salon);
    // }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'address' => 'required|string',
            'hours' => 'required|array',
            'contact' => 'required|array',
            'contact.phone' => 'required|string',
            'contact.email' => 'required|email',
            'images' => 'nullable|image|max:2048',
            'managerId' => 'nullable|exists:users,id',
            'ownerId' => 'nullable|exists:users,id',
        ]);

        // Gestion de l'image si elle est fournie
        if ($request->hasFile('images')) {
            $validated['images'] = $request->file('images')->store('salon_photos', 'public');
        }

        // Ajouter l'ownerId si l'utilisateur connecté est un propriétaire
        $currentUser = auth()->user();
        if ($currentUser && $currentUser->role === 'owner') {
            $validated['ownerId'] = $currentUser->id;
        } elseif ($request->has('ownerId')) {
            // Si un ownerId est fourni dans la requête, l'utiliser
            $validated['ownerId'] = $request->input('ownerId');
        }

        // Création du salon avec les données validées
        $salon = Salon::create($validated);

        // Retourne la ressource nouvellement créée
        return new SalonResource($salon);
    }



    public function show(Salon $salon)
    {
        return new SalonResource($salon->load(['services', 'hairdressers', 'reviews', 'owner']));
    }

    public function update(Request $request, Salon $salon)
    {
        $validated = $request->validate([
            'name' => 'sometimes|string|max:255',
            'address' => 'sometimes|string',
            'hours' => 'sometimes|array',
            'contact' => 'sometimes|array',
            'contact.phone' => 'sometimes|string',
            'contact.email' => 'sometimes|email',
            'images' => 'nullable|image|max:2048',
            'managerId' => 'nullable|exists:users,id',
            'ownerId' => 'nullable|exists:users,id',
        ]);

    
        // Gestion de l'image si elle est fournie
        if ($request->hasFile('images')) {
            // Supprimer l'ancienne image si elle existe
            if ($salon->images) {
                \Storage::disk('public')->delete($salon->images);
            }
            $validated['images'] = $request->file('images')->store('salon_photos', 'public');
        }
    
        // Mise à jour du salon avec les données validées
        $salon->update($validated);
    
        // Retourne la ressource mise à jour
        return new SalonResource($salon);
    }
    


    public function destroy(Salon $salon)
    {
        // Vérification des réservations actives
        if ($salon->bookings()->where('status', 'confirmed')->exists()) {
            return response()->json([
                'message' => 'Cannot delete salon with active bookings'
            ], 403);
        }

        // Supprimer les reviews associées au salon
        $salon->reviews()->delete();

        // Supprimer le salon
        $salon->forceDelete();

        return response()->json(['message' => 'Salon deleted successfully']);
    }


    public function getAnalytics(Salon $salon)
    {
        $analytics = [
            'total_bookings' => $salon->bookings()->count(),
            'total_revenue' => $salon->bookings()->sum('total_amount'),
            'average_rating' => $salon->reviews()->avg('rating'),
            'popular_services' => $salon->services()
                ->withCount('bookings')
                ->orderBy('bookings_count', 'desc')
                ->take(5)
                ->get(),
            'top_hairdressers' => $salon->hairdressers()
                ->withCount('bookings')
                ->orderBy('bookings_count', 'desc')
                ->take(5)
                ->get(),
        ];

        return response()->json($analytics);
    }

    public function updateStatus(Request $request, Salon $salon)
    {
        $validated = $request->validate([
            'status' => 'required|in:active,inactive,maintenance',
        ]);

        $salon->update(['status' => $validated['status']]);
        return new SalonResource($salon);
    }
}
