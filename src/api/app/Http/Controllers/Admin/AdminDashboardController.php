<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Salon;
use App\Models\Booking;
use App\Models\Service;
use Illuminate\Http\Request;

class AdminDashboardController extends Controller
{
    public function getStats()
    {
        $stats = [
            'total_users' => User::count(),
            'total_salons' => Salon::count(),
            'total_bookings' => Booking::count(),
            'total_services' => Service::count(),
            'recent_users' => User::latest()->take(5)->get(),
            'recent_bookings' => Booking::with(['client', 'salon', 'hairdresser'])
                                      ->latest()
                                      ->take(5)
                                      ->get(),
            'revenue_stats' => $this->getRevenueStats(),
            'booking_stats' => $this->getBookingStats(),
        ];

        return response()->json($stats);
    }

    private function getRevenueStats()
    {
        // Get monthly revenue for the last 6 months
        $revenue = Booking::where('status', 'completed')
            ->where('created_at', '>=', now()->subMonths(6))
            ->selectRaw('MONTH(created_at) as month, SUM(total_amount) as revenue')
            ->groupBy('month')
            ->get();

        return $revenue;
    }

    private function getBookingStats()
    {
        // Get monthly bookings for the last 6 months
        $bookings = Booking::where('created_at', '>=', now()->subMonths(6))
            ->selectRaw('MONTH(created_at) as month, COUNT(*) as count')
            ->groupBy('month')
            ->get();

        return $bookings;
    }
}