<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Manager;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Http\Resources\UserResource;
use App\Models\Hairdresser;
use Illuminate\Support\Facades\Storage;

class UserManagementController extends Controller
{
    public function index(Request $request)
    {
        $currentUser = auth()->user();
        
        if ($currentUser && $currentUser->role === 'owner') {
            // Pour les propriétaires, filtrer seulement leurs utilisateurs
            $managerIds = Manager::where('owner_id', $currentUser->id)
                ->pluck('manager_id')
                ->toArray();
            
            $query = User::where(function($q) use ($managerIds, $currentUser) {
                $q->whereIn('id', $managerIds) // Managers créés par ce propriétaire
                  ->orWhere(function($subQ) use ($currentUser) {
                      $subQ->where('role', 'hairdresser')
                            ->where('salonId', $currentUser->salonId); // Coiffeurs du salon du propriétaire
                  });
            });
        } else {
            // Pour les admins, pas de filtre
            $query = User::query();
        }

        // Apply filters
        if ($request->has('role')) {
            $query->where('role', $request->role);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('firstName', 'like', "%{$search}%")
                    ->orWhere('lastName', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $users = $query->paginate(10);
        return UserResource::collection($users);
    }

    public function userList()
    {
        $currentUser = auth()->user();
        
        // Si l'utilisateur connecté est un propriétaire, retourner seulement ses managers et coiffeurs
        if ($currentUser && $currentUser->role === 'owner') {
            // Récupérer les IDs des managers créés par ce propriétaire
            $managerIds = Manager::where('owner_id', $currentUser->id)
                ->pluck('manager_id')
                ->toArray();
            
            // Récupérer les utilisateurs qui sont soit des managers de ce propriétaire, soit des coiffeurs
            $users = User::where(function($query) use ($managerIds, $currentUser) {
                $query->whereIn('id', $managerIds) // Managers créés par ce propriétaire
                      ->orWhere(function($q) use ($currentUser) {
                          $q->where('role', 'hairdresser')
                            ->where('salonId', $currentUser->salonId); // Coiffeurs du salon du propriétaire
                      });
            })->get();
            
            // Ajouter l'ownerId pour les managers
            $users->each(function($user) {
                if ($user->role === 'manager') {
                    $managerRelation = Manager::where('manager_id', $user->id)->first();
                    if ($managerRelation) {
                        $user->ownerId = $managerRelation->owner_id;
                    }
                }
            });
            
            return response()->json($users);
        }
        
        // Pour les admins, retourner tous les utilisateurs avec ownerId pour les managers
        $users = User::all();
        $users->each(function($user) {
            if ($user->role === 'manager') {
                $managerRelation = Manager::where('manager_id', $user->id)->first();
                if ($managerRelation) {
                    $user->ownerId = $managerRelation->owner_id;
                }
            }
        });
        
        return response()->json($users);
    }

    public function store(Request $request)
    {
        // Valider les données
        $validated = $request->validate([
            'salonId' => 'nullable|exists:salons,id', // Validation du salonId
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'password' => 'required|min:8',
            'phone' => 'nullable|string',
            'role' => 'required|in:admin,owner,manager,hairdresser,client',
            'gender' => 'required|in:male,female,other',
            'specialties' => 'nullable|array',
            'specialties.*' => 'nullable|string',
            'availability' => 'nullable|array',
            'availability.*.day' => 'required_with:availability|in:Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday',
            'availability.*.open' => 'required_with:availability|date_format:H:i',
            'availability.*.close' => [
                'required_with:availability',
                'date_format:H:i',
                function ($attribute, $value, $fail) use ($request) {
                    $index = explode('.', $attribute)[1];
                    $openTime = $request->input("availability.$index.open");

                    if (strtotime($value) <= strtotime($openTime)) {
                        $fail('The ' . $attribute . ' must be a time after ' . $openTime . '.');
                    }
                },
            ],
            'profileImage' => 'nullable',
            'ownerId' => 'required_if:role,manager|nullable|exists:users,id',
        ]);

        // Hashage du mot de passe
        $validated['password'] = Hash::make($validated['password']);

        // Formatage des disponibilités
        if ($validated['role'] === 'hairdresser' && isset($validated['availability'])) {
            $formattedAvailability = [];
            foreach ($validated['availability'] as $item) {
                if (isset($item['day'], $item['open'], $item['close'])) {
                    $formattedAvailability[$item['day']] = "{$item['open']}-{$item['close']}";
                }
            }
            $validated['availability'] = json_encode($formattedAvailability);
        } else {
            unset($validated['specialties'], $validated['availability']);
        }

        // Formatage des spécialités pour les coiffeurs
        if ($validated['role'] === 'hairdresser' && isset($validated['specialties'])) {
            $specialties = $validated['specialties'];
            if (is_array($specialties)) {
                // Filtrer les valeurs vides et encoder en JSON
                $filteredSpecialties = array_filter($specialties, function($specialty) {
                    return !empty(trim($specialty));
                });
                $validated['specialties'] = !empty($filteredSpecialties) ? json_encode(array_values($filteredSpecialties)) : null;
            } else {
                $validated['specialties'] = json_encode([$specialties]);
            }
        } else {
            unset($validated['specialties']);
        }

        // Upload de l'image et stockage du chemin
        if ($request->hasFile('profileImage')) {
            $path = $request->file('profileImage')->store('uploads/users', 'public');
            $validated['photoUrl'] = '/storage/' . $path;
        } else {
            $validated['photoUrl'] = null; // Si aucune image n'est fournie
        }

        // Ajout du salonId si disponible
        if ($request->filled('salonId')) {
            $validated['salonId'] = $request->salonId;
        } else {
            $validated['salonId'] = null;
        }

        // Ajouter email_verified_at pour les utilisateurs créés par l'admin
        $validated['email_verified_at'] = now();

        // Création de l'utilisateur
        $user = User::create($validated);

        // Si l'utilisateur est un coiffeur, créer une entrée dans la table hairdressers
        if ($validated['role'] === 'hairdresser') {
            Hairdresser::create([
                'userId' => $user->id,
                'salonId' => $validated['salonId'],
                'specialties' => $validated['specialties'] ?? null,
                'availability' => $validated['availability'] ?? null,
                'rating' => 0, // Valeur par défaut
                'active' => true, // Valeur par défaut
            ]);
        }

        // Si l'utilisateur est un manager et que l'utilisateur connecté est un owner, créer une entrée dans la table managers
        $currentUser = auth()->user();
        \Log::info('Utilisateur connecté', [
            'id' => $currentUser ? $currentUser->id : 'non défini',
            'role' => $currentUser ? $currentUser->role : 'non défini',
            'email' => $currentUser ? $currentUser->email : 'non défini'
        ]);
        
        // Logique pour créer la relation manager
        if ($validated['role'] === 'manager') {
            \Log::info('Tentative de création de relation manager', [
                'user_role' => $validated['role'],
                'current_user_role' => $currentUser ? $currentUser->role : 'non défini',
                'current_user_id' => $currentUser ? $currentUser->id : 'non défini',
                'new_user_id' => $user->id
            ]);

            // Si l'utilisateur connecté est un owner, créer la relation
            if ($currentUser && $currentUser->role === 'owner') {
                try {
                    Manager::create([
                        'owner_id' => $currentUser->id,
                        'manager_id' => $user->id,
                    ]);
                    \Log::info('Relation manager créée avec succès pour owner');
                } catch (\Exception $e) {
                    \Log::error('Erreur lors de la création de la relation manager: ' . $e->getMessage());
                }
            } 
            // Si l'utilisateur connecté est un admin, permettre la création sans owner spécifique
            else if ($currentUser && $currentUser->role === 'admin') {
                \Log::info('Admin crée un manager - pas de relation owner-manager automatique');
            }
            else {
                \Log::warning('Utilisateur non autorisé à créer des managers', [
                    'user_role' => $currentUser ? $currentUser->role : 'non défini'
                ]);
            }
        }

        // Logique pour créer la relation propriétaire-gérant
        if ($validated['role'] === 'manager' && $request->filled('ownerId')) {
            $ownerId = $request->input('ownerId');
            
            // Vérifier que le propriétaire existe et a le rôle 'owner'
            $owner = User::where('id', $ownerId)->where('role', 'owner')->first();
            if (!$owner) {
                return response()->json(['error' => 'Le propriétaire sélectionné n\'existe pas ou n\'a pas le bon rôle'], 400);
            }
            
            // Vérifier que le gérant n'est pas déjà associé à un propriétaire
            $existingManager = Manager::where('manager_id', $user->id)->first();
            if ($existingManager) {
                return response()->json(['error' => 'Ce gérant est déjà associé à un propriétaire'], 400);
            }
            
            // Créer la relation propriétaire-gérant
            try {
                Manager::create([
                    'owner_id' => $ownerId,
                    'manager_id' => $user->id,
                ]);
                \Log::info('Relation propriétaire-gérant créée avec succès', [
                    'owner_id' => $ownerId,
                    'manager_id' => $user->id
                ]);
            } catch (\Exception $e) {
                \Log::error('Erreur lors de la création de la relation propriétaire-gérant: ' . $e->getMessage());
                return response()->json(['error' => 'Erreur lors de la création de la relation propriétaire-gérant'], 500);
            }
        }

        return new UserResource($user);
    }

    // Méthode de test pour vérifier la table managers
    public function testManagers()
    {
        try {
            // Vérifier si la table existe
            $tableExists = \Schema::hasTable('managers');
            
            // Récupérer tous les managers
            $managers = Manager::with(['owner', 'manager'])->get();
            
            // Vérifier la structure de la table
            $columns = \Schema::getColumnListing('managers');
            
            return response()->json([
                'table_exists' => $tableExists,
                'columns' => $columns,
                'managers_count' => $managers->count(),
                'managers' => $managers,
                'auth_user' => auth()->user(),
                'auth_user_role' => auth()->user() ? auth()->user()->role : 'non défini'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    // Méthode pour créer manuellement une relation manager (test)
    public function createTestManager(Request $request)
    {
        try {
            $validated = $request->validate([
                'owner_id' => 'required|exists:users,id',
                'manager_id' => 'required|exists:users,id'
            ]);

            $manager = Manager::create([
                'owner_id' => $validated['owner_id'],
                'manager_id' => $validated['manager_id']
            ]);

            return response()->json([
                'success' => true,
                'manager' => $manager,
                'message' => 'Relation manager créée avec succès'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage()
            ], 500);
        }
    }

    // Méthode pour récupérer les gérants libres (non associés à un propriétaire)
    public function getFreeManagers()
    {
        // Récupérer tous les utilisateurs avec le rôle 'manager'
        $allManagers = User::where('role', 'manager')->get();
        
        // Récupérer les IDs des managers déjà associés à un propriétaire
        $assignedManagerIds = Manager::pluck('manager_id')->toArray();
        
        // Filtrer les managers libres
        $freeManagers = $allManagers->filter(function($manager) use ($assignedManagerIds) {
            return !in_array($manager->id, $assignedManagerIds);
        });
        
        return response()->json($freeManagers->values());
    }

    // Méthode pour récupérer les propriétaires existants
    public function getOwners()
    {
        // Récupérer tous les utilisateurs avec le rôle 'owner'
        $owners = User::where('role', 'owner')->get();
        
        return response()->json($owners);
    }

    // Méthode pour récupérer les statistiques des utilisateurs du propriétaire
    public function getOwnerStats()
    {
        $currentUser = auth()->user();
        
        if (!$currentUser || $currentUser->role !== 'owner') {
            return response()->json(['error' => 'Accès non autorisé'], 403);
        }

        // Récupérer les IDs des managers créés par ce propriétaire
        $managerIds = Manager::where('owner_id', $currentUser->id)
            ->pluck('manager_id')
            ->toArray();
        
        // Compter les managers et coiffeurs
        $managerCount = User::whereIn('id', $managerIds)->count();
        $hairdresserCount = User::where('role', 'hairdresser')
            ->where('salonId', $currentUser->salonId)
            ->count();
        
        return response()->json([
            'total_managers' => $managerCount,
            'total_hairdressers' => $hairdresserCount,
            'total_personnel' => $managerCount + $hairdresserCount
        ]);
    }


    public function show(User $user)
    {
        // Charger les relations nécessaires
        $user->load(['bookings', 'reviews']);
        
        // Si c'est un manager, récupérer son propriétaire
        if ($user->role === 'manager') {
            $managerRelation = Manager::where('manager_id', $user->id)->first();
            if ($managerRelation) {
                $user->ownerId = $managerRelation->owner_id;
            }
        }
        
        return new UserResource($user);
    }

    public function update(Request $request, User $user)
    {
        // Règles de validation de base
        $validationRules = [
            'firstName' => 'sometimes|string|max:255',
            'lastName' => 'sometimes|string|max:255',
            'email' => 'sometimes|email|unique:users,email,' . $user->id,
            'password' => 'nullable|min:8',
            'phone' => 'nullable|string',
            'role' => 'sometimes|in:admin,owner,manager,hairdresser,client',
            'gender' => 'sometimes|in:male,female,other',
            'profileImage' => 'nullable',
            'status' => 'sometimes|in:active,inactive',
            'ownerId' => 'nullable|exists:users,id',
        ];

        // Ajouter les règles de validation pour `specialties` et `availability` uniquement si le rôle est `hairdresser`
        if ($request->input('role') === 'hairdresser') {
            $validationRules['specialties'] = 'nullable|string';
            $validationRules['availability'] = 'nullable|array';
            $validationRules['availability.*.day'] = 'required_with:availability|in:Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday';
            $validationRules['availability.*.open'] = 'required_with:availability|date_format:H:i';
            $validationRules['availability.*.close'] = [
                'required_with:availability',
                'date_format:H:i',
                function ($attribute, $value, $fail) use ($request) {
                    $index = explode('.', $attribute)[1];
                    $openTime = $request->input("availability.$index.open");

                    if (strtotime($value) <= strtotime($openTime)) {
                        $fail('The ' . $attribute . ' must be a time after ' . $openTime . '.');
                    }
                },
            ];
        }

        // Valider les données
        $validated = $request->validate($validationRules);

        // Logique de mise à jour
        if ($request->has('password')) {
            $validated['password'] = Hash::make($validated['password']);
        }

        // Gérer `specialties` et `availability` uniquement si le rôle est `hairdresser`
        if ($validated['role'] === 'hairdresser' && isset($validated['availability'])) {
            $formattedAvailability = [];
            foreach ($validated['availability'] as $item) {
                if (isset($item['day'], $item['open'], $item['close'])) {
                    $formattedAvailability[$item['day']] = "{$item['open']}-{$item['close']}";
                }
            }
            $validated['availability'] = json_encode($formattedAvailability);
        } else {
            // Si le rôle n'est pas `hairdresser`, ne pas modifier `specialties` et `availability`
            unset($validated['specialties'], $validated['availability']);
        }

        // Gérer l'image de profil
        if ($request->hasFile('profileImage')) {
            if ($user->photoUrl) {
                Storage::disk('public')->delete(str_replace('/storage/', '', $user->photoUrl));
            }
            $path = $request->file('profileImage')->store('uploads/users', 'public');
            $validated['photoUrl'] = '/storage/' . $path;
        }

        // Mettre à jour l'utilisateur
        $user->update($validated);

        // Gérer la relation manager si le rôle change vers 'manager' et que l'utilisateur connecté est un owner
        if (isset($validated['role']) && $validated['role'] === 'manager' && auth()->user()->role === 'owner') {
            // Vérifier si la relation existe déjà
            $existingManager = Manager::where('owner_id', auth()->id())
                ->where('manager_id', $user->id)
                ->first();

            if (!$existingManager) {
                Manager::create([
                    'owner_id' => auth()->id(),
                    'manager_id' => $user->id,
                ]);
            }
        }

        // Gérer la relation propriétaire-gérant si un ownerId est fourni
        if ($request->filled('ownerId') && isset($validated['role']) && $validated['role'] === 'manager') {
            $ownerId = $request->input('ownerId');
            
            // Vérifier que le propriétaire existe et a le rôle 'owner'
            $owner = User::where('id', $ownerId)->where('role', 'owner')->first();
            if (!$owner) {
                return response()->json(['error' => 'Le propriétaire sélectionné n\'existe pas ou n\'a pas le bon rôle'], 400);
            }
            
            // Supprimer l'ancienne relation si elle existe
            Manager::where('manager_id', $user->id)->delete();
            
            // Créer la nouvelle relation propriétaire-gérant
            try {
                Manager::create([
                    'owner_id' => $ownerId,
                    'manager_id' => $user->id,
                ]);
                \Log::info('Relation propriétaire-gérant mise à jour avec succès', [
                    'owner_id' => $ownerId,
                    'manager_id' => $user->id
                ]);
            } catch (\Exception $e) {
                \Log::error('Erreur lors de la mise à jour de la relation propriétaire-gérant: ' . $e->getMessage());
                return response()->json(['error' => 'Erreur lors de la mise à jour de la relation propriétaire-gérant'], 500);
            }
        }

        return response()->json($user, 200);
    }

    public function destroy(User $user)
    {
        // Check if user can be safely deleted
        if ($user->role === 'admin') {
            return response()->json(['message' => 'Cannot delete admin user'], 403);
        }

        $user->forceDelete();

        return response()->json(['message' => 'User deleted successfully']);
    }

    public function bulkAction(Request $request)
    {
        $validated = $request->validate([
            'action' => 'required|in:delete,activate,deactivate',
            'userIds' => 'required|array',
            'userIds.*' => 'exists:users,id'
        ]);

        $users = User::whereIn('id', $validated['userIds'])->get();

        foreach ($users as $user) {
        switch ($validated['action']) {
                case 'delete':
                    if ($user->role !== 'admin') {
                        $user->forceDelete();
                    }
                    break;
            case 'activate':
                    $user->update(['status' => 'active']);
                break;
            case 'deactivate':
                    $user->update(['status' => 'inactive']);
                break;
            }
        }

        return response()->json(['message' => 'Action effectuée avec succès']);
    }




}
