<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Salon;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SalonManagerController extends Controller
{
    public function index()
    {
        $salons = Salon::with(['manager', 'owner'])->get();
        $managers = User::where('role', 'manager')->get();
        
        return response()->json([
            'salons' => $salons,
            'managers' => $managers
        ]);
    }

    public function assignManager(Request $request)
    {
        $validated = $request->validate([
            'salonId' => 'required|exists:salons,id',
            'managerId' => 'required|exists:users,id'
        ]);

        // Vérifier que l'utilisateur est bien un manager
        $manager = User::find($validated['managerId']);
        if ($manager->role !== 'manager') {
            return response()->json([
                'message' => 'L\'utilisateur sélectionné n\'est pas un manager.'
            ], 400);
        }

        // Vérifier que le salon n'a pas déjà un manager
        $salon = Salon::find($validated['salonId']);
        if ($salon->managerId) {
            return response()->json([
                'message' => 'Ce salon a déjà un manager assigné.'
            ], 400);
        }

        // Vérifier que le manager n'est pas déjà assigné à un autre salon
        $existingManager = Salon::where('managerId', $validated['managerId'])->first();
        if ($existingManager) {
            return response()->json([
                'message' => 'Ce manager est déjà assigné à un autre salon.'
            ], 400);
        }

        $salon->update(['managerId' => $validated['managerId']]);

        return response()->json([
            'message' => 'Manager assigné avec succès au salon.',
            'salon' => $salon->load('manager')
        ]);
    }

    public function removeManager(Request $request)
    {
        $validated = $request->validate([
            'salonId' => 'required|exists:salons,id'
        ]);

        $salon = Salon::find($validated['salonId']);
        $salon->update(['managerId' => null]);

        return response()->json([
            'message' => 'Manager retiré avec succès du salon.'
        ]);
    }

    public function getUnassignedManagers()
    {
        $assignedManagerIds = Salon::whereNotNull('managerId')->pluck('managerId');
        $unassignedManagers = User::where('role', 'manager')
            ->whereNotIn('id', $assignedManagerIds)
            ->get();

        return response()->json($unassignedManagers);
    }

    public function getSalonsWithoutManager()
    {
        $salons = Salon::whereNull('managerId')->get();
        return response()->json($salons);
    }
} 