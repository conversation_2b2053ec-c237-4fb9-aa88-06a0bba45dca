<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Salon;
use Illuminate\Http\Request;
use App\Http\Resources\UserResource;
use Illuminate\Support\Facades\Hash;

class StaffController extends Controller
{
    public function index(Request $request)
    {
        $query = User::where('role', 'hairdresser')
            ->with('salon'); // Charger la relation salon

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('firstName', 'like', "%{$search}%")
                  ->orWhere('lastName', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->has('salon_id') && $request->salon_id !== 'all') {
            $query->where('salonId', $request->salon_id);
        }

        $staff = $query->paginate(10);
        return UserResource::collection($staff);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'password' => 'required|min:8',
            'phone' => 'required|string',
            'gender' => 'required|in:male,female,other',
            'salonId' => 'required|exists:salons,id',
            'specialties' => 'nullable|array',
            'specialties.*' => 'string',
            'availability' => 'nullable|array',
            'availability.*.day' => 'required|string',
            'availability.*.open' => 'required|string',
            'availability.*.close' => 'required|string',
            'profileImage' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $validated['password'] = Hash::make($validated['password']);
        $validated['role'] = 'hairdresser';
        $validated['status'] = 'active';

        // Gérer l'upload de l'image
        if ($request->hasFile('profileImage')) {
            $path = $request->file('profileImage')->store('uploads/users', 'public');
            $validated['photoUrl'] = '/storage/' . $path;
        }

        $staff = User::create($validated);
        return new UserResource($staff);
    }

    public function show(User $staff)
    {
        if ($staff->role !== 'hairdresser') {
            return response()->json([
                'message' => 'Cet utilisateur n\'est pas un coiffeur.'
            ], 404);
        }

        return new UserResource($staff->load('salon'));
    }

    public function update(Request $request, User $staff)
    {
        if ($staff->role !== 'hairdresser') {
            return response()->json([
                'message' => 'Cet utilisateur n\'est pas un coiffeur.'
            ], 404);
        }

        $validated = $request->validate([
            'firstName' => 'sometimes|string|max:255',
            'lastName' => 'sometimes|string|max:255',
            'email' => 'sometimes|email|unique:users,email,' . $staff->id,
            'phone' => 'sometimes|string',
            'gender' => 'sometimes|in:male,female,other',
            'specialties' => 'sometimes|array',
            'specialties.*' => 'string',
            'availability' => 'sometimes|array',
            'availability.*.day' => 'required|string',
            'availability.*.open' => 'required|string',
            'availability.*.close' => 'required|string',
            'status' => 'sometimes|in:active,inactive',
            'salonId' => 'sometimes|exists:salons,id',
            'profileImage' => 'sometimes|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($request->has('password')) {
            $validated['password'] = Hash::make($request->password);
        }

        // Gérer l'upload de l'image
        if ($request->hasFile('profileImage')) {
            // Supprimer l'ancienne image si elle existe
            if ($staff->photoUrl) {
                \Storage::disk('public')->delete(str_replace('/storage/', '', $staff->photoUrl));
            }
            $path = $request->file('profileImage')->store('uploads/users', 'public');
            $validated['photoUrl'] = '/storage/' . $path;
        }

        $staff->update($validated);
        return new UserResource($staff->load('salon'));
    }

    public function destroy(User $staff)
    {
        if ($staff->role !== 'hairdresser') {
            return response()->json([
                'message' => 'Cet utilisateur n\'est pas un coiffeur.'
            ], 404);
        }

        // Vérifier s'il y a des réservations actives
        if ($staff->bookings()->where('status', 'confirmed')->exists()) {
            return response()->json([
                'message' => 'Impossible de supprimer un coiffeur avec des réservations actives.'
            ], 403);
        }

        $staff->delete();
        return response()->json(['message' => 'Coiffeur supprimé avec succès']);
    }

    public function updateAvailability(Request $request, User $staff)
    {
        if ($staff->role !== 'hairdresser') {
            return response()->json([
                'message' => 'Cet utilisateur n\'est pas un coiffeur.'
            ], 404);
        }

        $validated = $request->validate([
            'availability' => 'required|array',
            'availability.*' => 'array',
        ]);

        $staff->update(['availability' => $validated['availability']]);
        return new UserResource($staff->load('salon'));
    }
} 