<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use App\Models\Salon;
use App\Models\Booking;
use App\Models\Service;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OwnerDashboardController extends Controller
{
    public function getStats()
    {
        $user = Auth::user();
        $salonIds = $user->salons()->pluck('_id');

        $stats = [
            'total_revenue' => Booking::whereIn('salonId', $salonIds)
                ->where('status', 'completed')
                ->sum('total_amount'),
            'total_bookings' => Booking::whereIn('salonId', $salonIds)->count(),
            'total_staff' => User::whereIn('salonId', $salonIds)
                ->where('role', 'hairdresser')
                ->count(),
            'average_rating' => Salon::whereIn('_id', $salonIds)->avg('rating'),
            'recent_bookings' => Booking::whereIn('salonId', $salonIds)
                ->with(['client', 'hairdresser', 'services'])
                ->latest()
                ->take(5)
                ->get(),
            'revenue_stats' => $this->getRevenueStats($salonIds),
            'booking_stats' => $this->getBookingStats($salonIds),
        ];

        return response()->json($stats);
    }

    private function getRevenueStats($salonIds)
    {
        return Booking::whereIn('salonId', $salonIds)
            ->where('status', 'completed')
            ->where('created_at', '>=', now()->subMonths(6))
            ->selectRaw('MONTH(created_at) as month, SUM(total_amount) as revenue')
            ->groupBy('month')
            ->get();
    }

    private function getBookingStats($salonIds)
    {
        return Booking::whereIn('salonId', $salonIds)
            ->where('created_at', '>=', now()->subMonths(6))
            ->selectRaw('MONTH(created_at) as month, COUNT(*) as count')
            ->groupBy('month')
            ->get();
    }
}