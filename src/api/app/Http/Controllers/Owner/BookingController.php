<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Salon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Resources\BookingResource;
use App\Notifications\BookingStatusChanged;

class BookingController extends Controller
{
    public function index(Request $request)
    {
        // Récupérer les IDs des salons appartenant au propriétaire connecté
        $ownerSalonIds = Salon::where('ownerId', Auth::user()->id)->pluck('id');
        
        $query = Booking::whereIn('salonId', $ownerSalonIds)
            ->with(['client', 'hairdresser', 'services', 'salon']);

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->whereHas('client', function($clientQuery) use ($search) {
                    $clientQuery->where('firstName', 'like', "%{$search}%")
                               ->orWhere('lastName', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                })
                ->orWhereHas('salon', function($salonQuery) use ($search) {
                    $salonQuery->where('name', 'like', "%{$search}%");
                });
            });
        }

        $bookings = $query->paginate($request->get('limit', 10));
        
        return response()->json([
            'data' => BookingResource::collection($bookings),
            'total' => $bookings->total(),
            'current_page' => $bookings->currentPage(),
            'last_page' => $bookings->lastPage(),
        ]);
    }

    public function show(Booking $booking)
    {
        // Vérifier que le booking appartient à un salon du propriétaire connecté
        $salon = Salon::where('id', $booking->salonId)
            ->where('ownerId', Auth::user()->id)
            ->first();
            
        if (!$salon) {
            return response()->json(['error' => 'Booking non autorisé'], 403);
        }
        
        return new BookingResource($booking->load(['client', 'hairdresser', 'services', 'salon']));
    }

    public function update(Request $request, Booking $booking)
    {
        // Vérifier que le booking appartient à un salon du propriétaire connecté
        $salon = Salon::where('id', $booking->salonId)
            ->where('ownerId', Auth::user()->id)
            ->first();
            
        if (!$salon) {
            return response()->json(['error' => 'Booking non autorisé'], 403);
        }

        $validated = $request->validate([
            'status' => 'sometimes|in:confirmed,cancelled,completed,pending',
            'dateTime' => 'sometimes|date|after:now',
            'hairdresserId' => 'sometimes|exists:users,id',
            'services' => 'sometimes|array',
            'services.*' => 'exists:services,id',
        ]);

        $oldStatus = $booking->status;
        $booking->update($validated);

        if ($booking->status !== $oldStatus) {
            try {
                $booking->client->notify(new BookingStatusChanged($booking));
            } catch (\Exception $e) {
                // Log l'erreur mais ne pas faire échouer la mise à jour
                \Log::error('Erreur lors de l\'envoi de la notification: ' . $e->getMessage());
            }
        }

        return new BookingResource($booking->load(['client', 'salon', 'hairdresser', 'services']));
    }

    public function confirm(Booking $booking)
    {
        // Vérifier que le booking appartient à un salon du propriétaire connecté
        $salon = Salon::where('id', $booking->salonId)
            ->where('ownerId', Auth::user()->id)
            ->first();
            
        if (!$salon) {
            return response()->json(['error' => 'Booking non autorisé'], 403);
        }

        // Debug: Afficher le statut actuel
        \Log::info('Statut actuel du booking ' . $booking->id . ': ' . $booking->status);
        
        // Vérifier que le rendez-vous peut être confirmé (pas déjà confirmé, annulé ou terminé)
        if (in_array($booking->status, ['confirmed', 'cancelled', 'completed'])) {
            return response()->json([
                'message' => 'Ce rendez-vous ne peut pas être confirmé (statut actuel: ' . $booking->status . ')'
            ], 400);
        }

        // Charger les relations nécessaires
        $booking->load(['client', 'salon', 'hairdresser']);

        // Sauvegarder l'ancien statut pour la notification
        $oldStatus = $booking->status;

        // Confirmer le rendez-vous
        $booking->update(['status' => 'confirmed']);

        // Envoyer une notification au client si le statut a changé
        if ($booking->status !== $oldStatus) {
            try {
                $booking->client->notify(new BookingStatusChanged($booking));
            } catch (\Exception $e) {
                // Log l'erreur mais ne pas faire échouer la confirmation
                \Log::error('Erreur lors de l\'envoi de la notification: ' . $e->getMessage());
            }
        }

        return response()->json([
            'message' => 'Rendez-vous confirmé avec succès',
            'booking' => new BookingResource($booking->load(['client', 'salon', 'hairdresser', 'services']))
        ]);
    }

    public function reject(Booking $booking)
    {
        // Vérifier que le booking appartient à un salon du propriétaire connecté
        $salon = Salon::where('id', $booking->salonId)
            ->where('ownerId', Auth::user()->id)
            ->first();
            
        if (!$salon) {
            return response()->json(['error' => 'Booking non autorisé'], 403);
        }

        // Debug: Afficher le statut actuel
        \Log::info('Statut actuel du booking ' . $booking->id . ': ' . $booking->status);
        
        // Vérifier que le rendez-vous peut être refusé (pas déjà confirmé, annulé ou terminé)
        if (in_array($booking->status, ['confirmed', 'cancelled', 'completed'])) {
            return response()->json([
                'message' => 'Ce rendez-vous ne peut pas être refusé (statut actuel: ' . $booking->status . ')'
            ], 400);
        }

        // Charger les relations nécessaires
        $booking->load(['client', 'salon', 'hairdresser']);

        // Sauvegarder l'ancien statut pour la notification
        $oldStatus = $booking->status;

        // Refuser le rendez-vous
        $booking->update(['status' => 'cancelled']);

        // Envoyer une notification au client si le statut a changé
        if ($booking->status !== $oldStatus) {
            try {
                $booking->client->notify(new BookingStatusChanged($booking));
            } catch (\Exception $e) {
                // Log l'erreur mais ne pas faire échouer le refus
                \Log::error('Erreur lors de l\'envoi de la notification: ' . $e->getMessage());
            }
        }

        return response()->json([
            'message' => 'Rendez-vous refusé avec succès',
            'booking' => new BookingResource($booking->load(['client', 'salon', 'hairdresser', 'services']))
        ]);
    }

    public function getAnalytics(Request $request)
    {
        // Récupérer les IDs des salons appartenant au propriétaire connecté
        $ownerSalonIds = Salon::where('ownerId', Auth::user()->id)->pluck('id');

        $analytics = [
            'total_bookings' => Booking::whereIn('salonId', $ownerSalonIds)->count(),
            'completed_bookings' => Booking::whereIn('salonId', $ownerSalonIds)
                ->where('status', 'completed')
                ->count(),
            'cancelled_bookings' => Booking::whereIn('salonId', $ownerSalonIds)
                ->where('status', 'cancelled')
                ->count(),
            'revenue' => Booking::whereIn('salonId', $ownerSalonIds)
                ->where('status', 'completed')
                ->sum('total_amount'),
            'popular_time_slots' => Booking::whereIn('salonId', $ownerSalonIds)
                ->selectRaw('HOUR(dateTime) as hour, COUNT(*) as count')
                ->groupBy('hour')
                ->orderBy('count', 'desc')
                ->get(),
            'booking_trends' => Booking::whereIn('salonId', $ownerSalonIds)
                ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->where('created_at', '>=', now()->subDays(30))
                ->groupBy('date')
                ->get(),
        ];

        return response()->json($analytics);
    }

    /**
     * Méthode temporaire pour réinitialiser les statuts des bookings (pour les tests)
     */
    public function resetBookingStatuses()
    {
        // Récupérer les IDs des salons appartenant au propriétaire connecté
        $ownerSalonIds = Salon::where('ownerId', Auth::user()->id)->pluck('id');
        
        // Réinitialiser tous les bookings en 'pending' pour les tests
        Booking::whereIn('salonId', $ownerSalonIds)
            ->whereIn('status', ['confirmed', 'cancelled'])
            ->update(['status' => 'pending']);
        
        return response()->json([
            'message' => 'Statuts des bookings réinitialisés en "pending"'
        ]);
    }
}