<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Manager;
use App\Http\Resources\UserResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;

class ManagerController extends Controller
{
    public function getOwnerManagers()
    {
        $currentUser = Auth::user();
        
        if ($currentUser->role !== 'owner') {
            return response()->json(['error' => 'Accès non autorisé'], 403);
        }

        // Récupérer tous les managers associés à ce propriétaire
        $managers = Manager::where('owner_id', $currentUser->id)
            ->with(['manager' => function ($query) {
                $query->select('id', 'firstName', 'lastName', 'email', 'phone', 'gender', 'role', 'photoUrl', 'status');
            }])
            ->get()
            ->map(function ($managerRelation) {
                $manager = $managerRelation->manager;
                $manager->ownerId = $managerRelation->owner_id;
                return $manager;
            });

        return response()->json($managers);
    }

    public function storeManager(Request $request)
    {
        $currentUser = Auth::user();
        
        if ($currentUser->role !== 'owner') {
            return response()->json(['error' => 'Accès non autorisé'], 403);
        }

        // Valider les données
        $validated = $request->validate([
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'password' => 'required|min:8',
            'phone' => 'nullable|string',
            'gender' => 'required|in:male,female,other',
            'profileImage' => 'nullable',
        ]);

        // Hashage du mot de passe
        $validated['password'] = Hash::make($validated['password']);
        $validated['role'] = 'manager'; // Forcer le rôle manager
        $validated['email_verified_at'] = now(); // Ajouter email_verified_at pour les utilisateurs créés par l'owner

        // Upload de l'image et stockage du chemin
        if ($request->hasFile('profileImage')) {
            $path = $request->file('profileImage')->store('uploads/users', 'public');
            $validated['photoUrl'] = '/storage/' . $path;
        } else {
            $validated['photoUrl'] = null;
        }

        // Créer l'utilisateur
        $user = User::create($validated);

        // Créer automatiquement la relation propriétaire-gérant
        try {
            Manager::create([
                'owner_id' => $currentUser->id,
                'manager_id' => $user->id,
            ]);
            \Log::info('Relation propriétaire-gérant créée avec succès', [
                'owner_id' => $currentUser->id,
                'manager_id' => $user->id
            ]);
        } catch (\Exception $e) {
            \Log::error('Erreur lors de la création de la relation propriétaire-gérant: ' . $e->getMessage());
            return response()->json(['error' => 'Erreur lors de la création de la relation propriétaire-gérant'], 500);
        }

        return new UserResource($user);
    }

    public function showManager(User $manager)
    {
        $currentUser = Auth::user();
        
        if ($currentUser->role !== 'owner') {
            return response()->json(['error' => 'Accès non autorisé'], 403);
        }

        // Vérifier que le manager appartient bien à ce propriétaire
        $managerRelation = Manager::where('owner_id', $currentUser->id)
            ->where('manager_id', $manager->id)
            ->first();

        if (!$managerRelation) {
            return response()->json(['error' => 'Manager non trouvé'], 404);
        }

        // Charger les relations nécessaires
        $manager->load(['bookings', 'reviews']);
        
        // Ajouter l'ownerId
        $manager->ownerId = $managerRelation->owner_id;
        
        return new UserResource($manager);
    }

    public function updateManager(Request $request, User $manager)
    {
        $currentUser = Auth::user();
        
        if ($currentUser->role !== 'owner') {
            return response()->json(['error' => 'Accès non autorisé'], 403);
        }

        // Vérifier que le manager appartient bien à ce propriétaire
        $managerRelation = Manager::where('owner_id', $currentUser->id)
            ->where('manager_id', $manager->id)
            ->first();

        if (!$managerRelation) {
            return response()->json(['error' => 'Manager non trouvé'], 404);
        }

        // Règles de validation
        $validationRules = [
            'firstName' => 'sometimes|string|max:255',
            'lastName' => 'sometimes|string|max:255',
            'email' => 'sometimes|email|unique:users,email,' . $manager->id,
            'password' => 'nullable|min:8',
            'phone' => 'nullable|string',
            'gender' => 'sometimes|in:male,female,other',
            'profileImage' => 'nullable',
            'status' => 'sometimes|in:active,inactive',
        ];

        // Valider les données
        $validated = $request->validate($validationRules);

        // Logique de mise à jour
        if ($request->has('password')) {
            $validated['password'] = Hash::make($validated['password']);
        }

        // Gérer l'image de profil
        if ($request->hasFile('profileImage')) {
            if ($manager->photoUrl) {
                Storage::disk('public')->delete(str_replace('/storage/', '', $manager->photoUrl));
            }
            $path = $request->file('profileImage')->store('uploads/users', 'public');
            $validated['photoUrl'] = '/storage/' . $path;
        }

        // Mettre à jour l'utilisateur
        $manager->update($validated);

        return response()->json($manager, 200);
    }

    public function destroyManager(User $manager)
    {
        $currentUser = Auth::user();
        
        if ($currentUser->role !== 'owner') {
            return response()->json(['error' => 'Accès non autorisé'], 403);
        }

        // Vérifier que le manager appartient bien à ce propriétaire
        $managerRelation = Manager::where('owner_id', $currentUser->id)
            ->where('manager_id', $manager->id)
            ->first();

        if (!$managerRelation) {
            return response()->json(['error' => 'Manager non trouvé'], 404);
        }

        // Supprimer la relation manager
        $managerRelation->delete();

        // Supprimer l'utilisateur
        $manager->forceDelete();

        return response()->json(['message' => 'Manager supprimé avec succès']);
    }
} 