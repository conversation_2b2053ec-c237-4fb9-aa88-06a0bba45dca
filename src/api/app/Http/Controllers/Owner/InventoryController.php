<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Salon;
use Illuminate\Http\Request;
use App\Http\Resources\ProductResource;

class InventoryController extends Controller
{
    public function index(Request $request)
    {
        $query = Product::whereIn('salonId', Auth::user()->salons()->pluck('_id'));

        if ($request->has('salon_id')) {
            $query->where('salonId', $request->salon_id);
        }

        if ($request->has('category')) {
            $query->where('category', $request->category);
        }

        if ($request->has('low_stock')) {
            $query->whereRaw('stock <= min_stock');
        }

        $products = $query->paginate(10);
        return ProductResource::collection($products);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'category' => 'required|string',
            'stock' => 'required|integer|min:0',
            'min_stock' => 'required|integer|min:0',
            'price' => 'required|numeric|min:0',
            'supplier' => 'required|string',
            'salonId' => 'required|exists:salons,_id',
        ]);

        $this->authorize('manage', Salon::find($validated['salonId']));

        $product = Product::create($validated);
        return new ProductResource($product);
    }

    public function update(Request $request, Product $product)
    {
        $this->authorize('manage', Salon::find($product->salonId));

        $validated = $request->validate([
            'name' => 'sometimes|string|max:255',
            'category' => 'sometimes|string',
            'stock' => 'sometimes|integer|min:0',
            'min_stock' => 'sometimes|integer|min:0',
            'price' => 'sometimes|numeric|min:0',
            'supplier' => 'sometimes|string',
        ]);

        $product->update($validated);
        return new ProductResource($product);
    }

    public function destroy(Product $product)
    {
        $this->authorize('manage', Salon::find($product->salonId));
        $product->delete();
        return response()->json(['message' => 'Product deleted successfully']);
    }

    public function adjustStock(Request $request, Product $product)
    {
        $this->authorize('manage', Salon::find($product->salonId));

        $validated = $request->validate([
            'adjustment' => 'required|integer',
            'reason' => 'required|string',
        ]);

        $newStock = $product->stock + $validated['adjustment'];
        
        if ($newStock < 0) {
            return response()->json([
                'message' => 'Cannot reduce stock below 0'
            ], 400);
        }

        $product->update(['stock' => $newStock]);
        
        // Log the stock adjustment
        $product->stockAdjustments()->create([
            'amount' => $validated['adjustment'],
            'reason' => $validated['reason'],
            'user_id' => Auth::id(),
        ]);

        return new ProductResource($product);
    }

    public function getLowStockAlert()
    {
        $salonIds = Auth::user()->salons()->pluck('_id');

        $lowStock = Product::whereIn('salonId', $salonIds)
            ->whereRaw('stock <= min_stock')
            ->get();

        return response()->json($lowStock);
    }
}