<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class FinancialController extends Controller
{
    public function getFinancialOverview(Request $request)
    {
        $salonIds = Auth::user()->salons()->pluck('_id');
        $startDate = $request->get('start_date', now()->startOfMonth());
        $endDate = $request->get('end_date', now()->endOfMonth());

        $overview = [
            'total_revenue' => Booking::whereIn('salonId', $salonIds)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->where('status', 'completed')
                ->sum('total_amount'),
            'revenue_by_service' => Service::whereIn('salonId', $salonIds)
                ->withSum(['bookings' => function ($query) use ($startDate, $endDate) {
                    $query->whereBetween('created_at', [$startDate, $endDate])
                          ->where('status', 'completed');
                }], 'total_amount')
                ->get(),
            'daily_revenue' => Booking::whereIn('salonId', $salonIds)
                ->where('status', 'completed')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->selectRaw('DATE(created_at) as date, SUM(total_amount) as total')
                ->groupBy('date')
                ->get(),
            'average_transaction' => Booking::whereIn('salonId', $salonIds)
                ->where('status', 'completed')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->avg('total_amount'),
        ];

        return response()->json($overview);
    }

    public function getStaffPerformance(Request $request)
    {
        $salonIds = Auth::user()->salons()->pluck('_id');
        $startDate = $request->get('start_date', now()->startOfMonth());
        $endDate = $request->get('end_date', now()->endOfMonth());

        $performance = Booking::whereIn('salonId', $salonIds)
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->with('hairdresser')
            ->get()
            ->groupBy('hairdresserId')
            ->map(function ($bookings) {
                return [
                    'total_bookings' => $bookings->count(),
                    'total_revenue' => $bookings->sum('total_amount'),
                    'average_rating' => $bookings->avg('rating'),
                ];
            });

        return response()->json($performance);
    }

    public function getOutstandingPayments()
    {
        $salonIds = Auth::user()->salons()->pluck('_id');

        $payments = Booking::whereIn('salonId', $salonIds)
            ->where('payment_status', 'pending')
            ->with(['client', 'services'])
            ->get();

        return response()->json($payments);
    }

    public function generateReport(Request $request)
    {
        $validated = $request->validate([
            'report_type' => 'required|in:revenue,staff,services',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'format' => 'required|in:csv,pdf',
        ]);

        // Report generation logic would go here
        // For now, return the data
        return response()->json([
            'message' => 'Report generated successfully',
            'data' => $this->getFinancialOverview($request),
        ]);
    }
}