<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use App\Models\Service;
use App\Models\Salon;
use Illuminate\Http\Request;
use App\Http\Resources\ServiceResource;
use Illuminate\Support\Facades\Auth;

class ServiceController extends Controller
{
    public function index(Request $request)
    {
        $currentUser = Auth::user();
        
        // Récupérer les services des salons appartenant au propriétaire connecté
        $salonIds = Salon::where('ownerId', $currentUser->id)->pluck('id');
        $query = Service::whereIn('salonId', $salonIds);

        if ($request->has('search')) {
            $search = $request->search;
            $query->where('name', 'like', "%{$search}%");
        }

        if ($request->has('category')) {
            $query->where('category', $request->category);
        }

        $services = $query->get();
        
        return response()->json([
            'services' => $services,
            'count' => $services->count()
        ]);
    }

    public function store(Request $request)
    {
        $currentUser = Auth::user();
        
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'duration' => 'required|integer|min:1',
            'price' => 'required|numeric|min:0',
            'salonId' => 'required|exists:salons,id',
            'category' => 'required|string',
        ]);

        // Vérifier que le salon appartient au propriétaire connecté
        $salon = Salon::where('id', $validated['salonId'])
            ->where('ownerId', $currentUser->id)
            ->first();
            
        if (!$salon) {
            return response()->json(['error' => 'Salon non autorisé'], 403);
        }

        $service = Service::create($validated);
        return new ServiceResource($service);
    }

    public function update(Request $request, Service $service)
    {
        $currentUser = Auth::user();
        
        // Vérifier que le service appartient à un salon du propriétaire connecté
        $salon = Salon::where('id', $service->salonId)
            ->where('ownerId', $currentUser->id)
            ->first();
            
        if (!$salon) {
            return response()->json(['error' => 'Service non autorisé'], 403);
        }

        $validated = $request->validate([
            'name' => 'sometimes|string|max:255',
            'description' => 'sometimes|string',
            'duration' => 'sometimes|integer|min:1',
            'price' => 'sometimes|numeric|min:0',
            'category' => 'sometimes|string',
        ]);

        $service->update($validated);
        return new ServiceResource($service);
    }

    public function destroy(Service $service)
    {
        $currentUser = Auth::user();
        
        // Vérifier que le service appartient à un salon du propriétaire connecté
        $salon = Salon::where('id', $service->salonId)
            ->where('ownerId', $currentUser->id)
            ->first();
            
        if (!$salon) {
            return response()->json(['error' => 'Service non autorisé'], 403);
        }

        if ($service->bookings()->where('dateTime', '>', now())->exists()) {
            return response()->json([
                'message' => 'Cannot delete service with future bookings'
            ], 403);
        }

        $service->delete();
        return response()->json(['message' => 'Service deleted successfully']);
    }

    public function bulkUpdate(Request $request)
    {
        $currentUser = Auth::user();
        
        $validated = $request->validate([
            'services' => 'required|array',
            'services.*.id' => 'required|exists:services,id',
            'services.*.price' => 'required|numeric|min:0',
        ]);

        foreach ($validated['services'] as $serviceData) {
            $service = Service::find($serviceData['id']);
            
            // Vérifier que le service appartient à un salon du propriétaire connecté
            $salon = Salon::where('id', $service->salonId)
                ->where('ownerId', $currentUser->id)
                ->first();
                
            if ($salon) {
                $service->update(['price' => $serviceData['price']]);
            }
        }

        return response()->json(['message' => 'Services updated successfully']);
    }

    public function show(Service $service)
    {
        $currentUser = Auth::user();
        
        // Vérifier que le service appartient à un salon du propriétaire connecté
        $salon = Salon::where('id', $service->salonId)
            ->where('ownerId', $currentUser->id)
            ->first();
            
        if (!$salon) {
            return response()->json(['error' => 'Service non autorisé'], 403);
        }

        return new ServiceResource($service);
    }

    public function getSalons()
    {
        $currentUser = Auth::user();
        
        // Récupérer les salons du propriétaire connecté
        $salons = Salon::where('ownerId', $currentUser->id)
            ->select('id', 'name')
            ->get();
            
        return response()->json($salons);
    }
}