<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use App\Models\Salon;
use App\Models\User;
use App\Models\Manager;
use Illuminate\Http\Request;
use App\Http\Resources\SalonResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class SalonController extends Controller
{
    public function index()
    {
        $currentUser = Auth::user();
        
        // Récupérer les salons du propriétaire connecté
        $salons = Salon::where('ownerId', $currentUser->id)
            ->with(['services', 'hairdressers', 'owner', 'manager'])
            ->get();
            
        return SalonResource::collection($salons);
    }

    public function store(Request $request)
    {
        $currentUser = Auth::user();
        
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'address' => 'required|string',
            'hours' => 'required|array',
            'hours.*.day' => 'required|string',
            'hours.*.open' => 'required|string',
            'hours.*.close' => 'required|string',
            'contact' => 'required|array',
            'contact.phone' => 'required|string',
            'contact.email' => 'required|email',
            'managerId' => 'nullable|exists:users,id',
            'images' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Vérifier que le manager appartient bien à ce propriétaire
        if ($request->filled('managerId')) {
            $managerRelation = Manager::where('owner_id', $currentUser->id)
                ->where('manager_id', $request->input('managerId'))
                ->first();
                
            if (!$managerRelation) {
                return response()->json(['error' => 'Le gérant sélectionné ne vous appartient pas'], 400);
            }
        }

        // Ajouter automatiquement l'ownerId
        $validated['ownerId'] = $currentUser->id;

        // Gérer l'upload d'image
        if ($request->hasFile('images')) {
            $path = $request->file('images')->store('salon_photos', 'public');
            $validated['images'] = $path;
        }

        // Pas besoin de formatage spécial - Laravel gère automatiquement la conversion grâce au cast 'array'

        $salon = Salon::create($validated);
        
        return new SalonResource($salon->load(['services', 'hairdressers', 'owner', 'manager']));
    }

    public function show(Salon $salon)
    {
        $currentUser = Auth::user();
        
        // Vérifier que le salon appartient au propriétaire connecté
        if ($salon->ownerId !== $currentUser->id) {
            return response()->json(['error' => 'Accès non autorisé'], 403);
        }
        
        return new SalonResource($salon->load(['services', 'hairdressers', 'reviews', 'owner', 'manager']));
    }

    public function update(Request $request, Salon $salon)
    {
        $currentUser = Auth::user();
        
        // Vérifier que le salon appartient au propriétaire connecté
        if ($salon->ownerId !== $currentUser->id) {
            return response()->json(['error' => 'Accès non autorisé'], 403);
        }

        $validated = $request->validate([
            'name' => 'sometimes|string|max:255',
            'address' => 'sometimes|string',
            'hours' => 'sometimes|array',
            'hours.*.day' => 'required_with:hours|string',
            'hours.*.open' => 'required_with:hours|string',
            'hours.*.close' => 'required_with:hours|string',
            'contact' => 'sometimes|array',
            'contact.phone' => 'sometimes|string',
            'contact.email' => 'sometimes|email',
            'managerId' => 'nullable|exists:users,id',
            'images' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Vérifier que le manager appartient bien à ce propriétaire
        if ($request->filled('managerId')) {
            $managerRelation = Manager::where('owner_id', $currentUser->id)
                ->where('manager_id', $request->input('managerId'))
                ->first();
                
            if (!$managerRelation) {
                return response()->json(['error' => 'Le gérant sélectionné ne vous appartient pas'], 400);
            }
        }

        // Gérer l'upload d'image
        if ($request->hasFile('images')) {
            // Supprimer l'ancienne image si elle existe
            if ($salon->images) {
                Storage::disk('public')->delete($salon->images);
            }
            $path = $request->file('images')->store('salon_photos', 'public');
            $validated['images'] = $path;
        }

        // Pas besoin de formatage spécial - Laravel gère automatiquement la conversion grâce au cast 'array'

        $salon->update($validated);
        
        return new SalonResource($salon->load(['services', 'hairdressers', 'owner', 'manager']));
    }

    public function destroy(Salon $salon)
    {
        $currentUser = Auth::user();
        
        // Vérifier que le salon appartient au propriétaire connecté
        if ($salon->ownerId !== $currentUser->id) {
            return response()->json(['error' => 'Accès non autorisé'], 403);
        }

        // Vérifier s'il y a des réservations actives
        if ($salon->bookings()->where('status', '!=', 'cancelled')->exists()) {
            return response()->json(['error' => 'Ce salon a des réservations actives et ne peut pas être supprimé'], 403);
        }

        // Supprimer l'image si elle existe
        if ($salon->images) {
            Storage::disk('public')->delete($salon->images);
        }

        $salon->delete();
        
        return response()->json(['message' => 'Salon supprimé avec succès']);
    }

    public function getAnalytics(Salon $salon)
    {
        $currentUser = Auth::user();
        
        // Vérifier que le salon appartient au propriétaire connecté
        if ($salon->ownerId !== $currentUser->id) {
            return response()->json(['error' => 'Accès non autorisé'], 403);
        }

        $analytics = [
            'total_bookings' => $salon->bookings()->count(),
            'total_revenue' => $salon->bookings()->sum('total_amount'),
            'average_rating' => $salon->reviews()->avg('rating'),
            'popular_services' => $salon->services()
                ->withCount('bookings')
                ->orderBy('bookings_count', 'desc')
                ->take(5)
                ->get(),
            'top_hairdressers' => $salon->hairdressers()
                ->withCount('bookings')
                ->orderBy('bookings_count', 'desc')
                ->take(5)
                ->get(),
        ];

        return response()->json($analytics);
    }
}