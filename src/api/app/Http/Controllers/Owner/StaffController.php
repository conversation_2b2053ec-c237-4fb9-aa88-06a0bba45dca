<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Salon;
use App\Models\Hairdresser;
use Illuminate\Http\Request;
use App\Http\Resources\UserResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class StaffController extends Controller
{
    public function index(Request $request)
    {
        // Récupérer les IDs des salons du propriétaire connecté
        $ownerSalonIds = Auth::user()->salons()->pluck('id');
        
        $query = User::where('role', 'hairdresser')
            ->whereIn('salonId', $ownerSalonIds)
            ->with('salon'); // Charger la relation salon

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('firstName', 'like', "%{$search}%")
                  ->orWhere('lastName', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->has('salon_id') && $request->salon_id !== 'all') {
            // Vérifier que le salon appartient bien au propriétaire
            if ($ownerSalonIds->contains($request->salon_id)) {
                $query->where('salonId', $request->salon_id);
            }
        }

        $staff = $query->paginate(10);
        return UserResource::collection($staff);
    }

    public function store(Request $request)
    {
        // return $request->all();
        $validated = $request->validate([
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'password' => 'required|min:8',
            'phone' => 'required|string',
            'salonId' => 'required|exists:salons,id',
            'specialties' => 'nullable|string',
            'availability' => 'nullable',
            'profileImage' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Vérifier que le salon appartient au propriétaire connecté
        $ownerSalonIds = Auth::user()->salons()->pluck('id');
        if (!$ownerSalonIds->contains($validated['salonId'])) {
            return response()->json([
                'message' => 'Vous ne pouvez pas ajouter un coiffeur à un salon qui ne vous appartient pas.'
            ], 403);
        }

        // Ajouter email_verified_at pour les utilisateurs créés par l'admin
        $validated['email_verified_at'] = now();

        // Gérer les disponibilités qui peuvent venir en JSON string ou en array
        if (isset($validated['availability'])) {
            if (is_string($validated['availability'])) {
                // Si c'est une chaîne JSON, la décoder
                $decoded = json_decode($validated['availability'], true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $validated['availability'] = $decoded;
                } else {
                    // Si le JSON est invalide, on supprime le champ
                    unset($validated['availability']);
                }
            }
        }

        // Gérer l'upload d'image de profil
        if ($request->hasFile('profileImage')) {
            $path = $request->file('profileImage')->store('uploads/users', 'public');
            $validated['photoUrl'] = '/storage/' . $path;
        }

        $validated['password'] = Hash::make($validated['password']);
        $validated['role'] = 'hairdresser';
        $validated['status'] = 'active';

        $staff = User::create($validated);
        
        // Créer l'entrée dans la table hairdressers
        $hairdresserData = [
            'userId' => $staff->id,
            'salonId' => $validated['salonId'],
            'specialties' => $validated['specialties'] ?? [],
            'availability' => $validated['availability'] ?? [],
            'rating' => 0,
            'active' => true,
        ];
        
        Hairdresser::create($hairdresserData);
        
        return new UserResource($staff);
    }

    public function show(User $staff)
    {
        // Vérifier que le coiffeur appartient à un salon du propriétaire
        $ownerSalonIds = Auth::user()->salons()->pluck('id');
        if (!$ownerSalonIds->contains($staff->salonId)) {
            return response()->json([
                'message' => 'Accès non autorisé à ce coiffeur.'
            ], 403);
        }

        return new UserResource($staff->load('salon'));
    }

    public function update(Request $request, User $staff)
    {
        // dd($request->all());    
        \Log::info('StaffController update - Méthode appelée', [
            'staff_id' => $staff->id,
            'staff_email' => $staff->email,
            'request_method' => $request->method(),
            'request_url' => $request->url(),
            'content_type' => $request->header('Content-Type')
        ]);
        
        \Log::info('StaffController update - Données reçues:', $request->all());
        
        \Log::info('StaffController update - Données POST:', $request->post());
        
        $ownerSalonIds = Auth::user()->salons()->pluck('id');
        if (!$ownerSalonIds->contains($staff->salonId)) {
            return response()->json([
                'message' => 'Vous ne pouvez pas modifier un coiffeur qui ne travaille pas dans vos salons.'
            ], 403);
        }

        $validated = $request->validate([
            'firstName' => 'sometimes|string|max:255',
            'lastName' => 'sometimes|string|max:255',
            'email' => 'sometimes|email|unique:users,email,' . $staff->id,
            'phone' => 'sometimes|string',
            'specialties' => 'nullable|string',
            'availability' => 'nullable',
            'status' => 'sometimes|in:active,inactive',
            'salonId' => 'sometimes|exists:salons,id',
            'profileImage' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);
        
        \Log::info('StaffController update - Données validées:', $validated);
        \Log::info('StaffController update - Données avant mise à jour:', [
            'id' => $staff->id,
            'firstName' => $staff->firstName,
            'lastName' => $staff->lastName,
            'email' => $staff->email
        ]);

        if (isset($validated['salonId'])) {
            if (!$ownerSalonIds->contains($validated['salonId'])) {
                return response()->json([
                    'message' => 'Vous ne pouvez pas assigner un coiffeur à un salon qui ne vous appartient pas.'
                ], 403);
            }
        }

        if (isset($validated['availability'])) {
            if (is_string($validated['availability'])) {
                $decoded = json_decode($validated['availability'], true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $validated['availability'] = $decoded;
                } else {
                    unset($validated['availability']);
                }
            }
        }

        // Gestion améliorée de l'image de profil
        if ($request->hasFile('profileImage')) {
            // Supprimer l'ancienne image si elle existe
            if ($staff->photoUrl) {
                $oldImagePath = str_replace('/storage/', '', $staff->photoUrl);
                if (\Storage::disk('public')->exists($oldImagePath)) {
                    \Storage::disk('public')->delete($oldImagePath);
                }
            }
            
            // Enregistrer la nouvelle image
            $path = $request->file('profileImage')->store('uploads/users', 'public');
            $validated['photoUrl'] = '/storage/' . $path;
        } else {
            // Conserver l'image existante si aucune nouvelle image n'est fournie
            $validated['photoUrl'] = $staff->photoUrl;
        }

        if ($request->has('password')) {
            $validated['password'] = Hash::make($request->password);
        }

        $staff->update($validated);
        
        // Mettre à jour l'entrée dans la table hairdressers
        $hairdresser = Hairdresser::where('userId', $staff->id)->first();
        if ($hairdresser) {
            $hairdresserData = [];
            
            if (isset($validated['salonId'])) {
                $hairdresserData['salonId'] = $validated['salonId'];
            }
            
            if (isset($validated['specialties'])) {
                $hairdresserData['specialties'] = $validated['specialties'];
            }
            
            if (isset($validated['availability'])) {
                $hairdresserData['availability'] = $validated['availability'];
            }
            
            if (!empty($hairdresserData)) {
                $hairdresser->update($hairdresserData);
            }
        }
        
        \Log::info('StaffController update - Utilisateur mis à jour:', [
            'id' => $staff->id,
            'firstName' => $staff->firstName,
            'lastName' => $staff->lastName,
            'email' => $staff->email,
            'photoUrl' => $staff->photoUrl
        ]);
        
        return new UserResource($staff->load('salon'));
    }

    public function destroy(User $staff)
    {
        // Vérifier que le coiffeur appartient à un salon du propriétaire
        $ownerSalonIds = Auth::user()->salons()->pluck('id');
        if (!$ownerSalonIds->contains($staff->salonId)) {
            return response()->json([
                'message' => 'Vous ne pouvez pas supprimer un coiffeur qui ne travaille pas dans vos salons.'
            ], 403);
        }

        // Vérifier s'il y a des réservations actives
        if ($staff->bookings()->where('status', 'confirmed')->exists()) {
            return response()->json([
                'message' => 'Impossible de supprimer un coiffeur avec des réservations actives.'
            ], 403);
        }

        // Supprimer l'entrée dans la table hairdressers
        $hairdresser = Hairdresser::where('userId', $staff->id)->first();
        if ($hairdresser) {
            $hairdresser->delete();
        }
        

        $staff->delete();
        return response()->json(['message' => 'Coiffeur supprimé avec succès']);
    }

    public function updateAvailability(Request $request, User $staff)
    {
        // Vérifier que le coiffeur appartient à un salon du propriétaire
        $ownerSalonIds = Auth::user()->salons()->pluck('id');
        if (!$ownerSalonIds->contains($staff->salonId)) {
            return response()->json([
                'message' => 'Vous ne pouvez pas modifier les disponibilités d\'un coiffeur qui ne travaille pas dans vos salons.'
            ], 403);
        }

        $validated = $request->validate([
            'availability' => 'required|array',
            'availability.*' => 'array',
        ]);

        $staff->update(['availability' => $validated['availability']]);
        return new UserResource($staff->load('salon'));
    }
}