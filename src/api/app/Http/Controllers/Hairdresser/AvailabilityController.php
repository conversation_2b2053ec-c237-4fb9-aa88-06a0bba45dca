<?php

namespace App\Http\Controllers\Hairdresser;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AvailabilityController extends Controller
{
    public function show()
    {
        $hairdresser = Auth::user();
        return response()->json([
            'availability' => $hairdresser->availability,
            'timeOffRequests' => $hairdresser->timeOffRequests,
        ]);
    }

    public function update(Request $request)
    {
        $validated = $request->validate([
            'availability' => 'required|array',
            'availability.*' => 'array',
            'availability.*.*' => 'array:start,end',
        ]);

        $hairdresser = Auth::user();
        $hairdresser->update(['availability' => $validated['availability']]);

        return response()->json([
            'message' => 'Availability updated successfully',
            'availability' => $hairdresser->availability,
        ]);
    }

    public function requestTimeOff(Request $request)
    {
        $validated = $request->validate([
            'start_date' => 'required|date|after:now',
            'end_date' => 'required|date|after:start_date',
            'reason' => 'required|string',
        ]);

        $timeOff = Auth::user()->timeOffRequests()->create($validated);

        // Notify manager about the time off request
        // Implementation depends on your notification system

        return response()->json($timeOff);
    }

    public function getTimeOffRequests()
    {
        $requests = Auth::user()->timeOffRequests()
            ->orderBy('start_date')
            ->get();

        return response()->json($requests);
    }
}