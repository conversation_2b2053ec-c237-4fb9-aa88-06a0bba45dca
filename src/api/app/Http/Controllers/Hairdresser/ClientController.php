<?php

namespace App\Http\Controllers\Hairdresser;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\ClientNote;
use Illuminate\Http\Request;
use App\Http\Resources\UserResource;
use Illuminate\Support\Facades\Auth;

class ClientController extends Controller
{
    public function index(Request $request)
    {
        $query = User::where('role', 'client')
            ->whereHas('bookings', function ($query) {
                $query->where('hairdresserId', Auth::id());
            });

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('firstName', 'like', "%{$search}%")
                  ->orWhere('lastName', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $clients = $query->paginate(10);
        return UserResource::collection($clients);
    }

    public function show(User $client)
    {
        $clientData = [
            'profile' => new UserResource($client),
            'bookings' => $client->bookings()
                ->where('hairdresserId', Auth::id())
                ->with(['services'])
                ->get(),
            'notes' => ClientNote::where('clientId', $client->id)
                ->where('hairdresserId', Auth::id())
                ->get(),
            'preferences' => $client->preferences()
                ->where('hairdresserId', Auth::id())
                ->first(),
        ];

        return response()->json($clientData);
    }

    public function storeNote(Request $request, User $client)
    {
        $validated = $request->validate([
            'content' => 'required|string',
            'type' => 'required|in:style,preference,allergy,general',
        ]);

        $note = ClientNote::create([
            'clientId' => $client->id,
            'hairdresserId' => Auth::id(),
            'content' => $validated['content'],
            'type' => $validated['type'],
        ]);

        return response()->json($note);
    }

    public function updateNote(Request $request, ClientNote $note)
    {
        $this->authorize('update', $note);

        $validated = $request->validate([
            'content' => 'required|string',
            'type' => 'required|in:style,preference,allergy,general',
        ]);

        $note->update($validated);
        return response()->json($note);
    }

    public function deleteNote(ClientNote $note)
    {
        $this->authorize('delete', $note);
        $note->delete();
        return response()->json(['message' => 'Note deleted successfully']);
    }
}