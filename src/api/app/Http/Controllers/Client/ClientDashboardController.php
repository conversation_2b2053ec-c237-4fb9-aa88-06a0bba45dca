<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ClientDashboardController extends Controller
{
    public function getStats()
    {
        $client = Auth::user();

        $stats = [
            'upcoming_appointments' => Booking::where('clientId', $client->id)
                ->where('dateTime', '>', now())
                ->where('status', 'confirmed')
                ->with(['salon', 'hairdresser', 'services'])
                ->orderBy('dateTime')
                ->take(5)
                ->get(),
            'total_appointments' => Booking::where('clientId', $client->id)->count(),
            'favorite_salons' => $this->getFavoriteSalons(),
            'loyalty_points' => $this->getLoyaltyPoints(),
            'recent_services' => $this->getRecentServices(),
        ];

        return response()->json($stats);
    }

    private function getFavoriteSalons()
    {
        return Booking::where('clientId', Auth::id())
            ->with('salon')
            ->select('salonId')
            ->selectRaw('COUNT(*) as visit_count')
            ->groupBy('salonId')
            ->orderBy('visit_count', 'desc')
            ->take(3)
            ->get();
    }

    private function getLoyaltyPoints()
    {
        $client = Auth::user();
        return [
            'current_points' => $client->loyaltyPoints,
            'points_to_next_level' => $this->calculatePointsToNextLevel($client->loyaltyPoints),
            'expiring_points' => $this->getExpiringPoints(),
        ];
    }

    private function calculatePointsToNextLevel($currentPoints)
    {
        $levels = [
            'bronze' => 0,
            'silver' => 1000,
            'gold' => 2500,
            'platinum' => 5000
        ];

        foreach ($levels as $level => $points) {
            if ($currentPoints < $points) {
                return $points - $currentPoints;
            }
        }

        return 0;
    }

    private function getExpiringPoints()
    {
        return Auth::user()->loyaltyTransactions()
            ->where('expires_at', '>', now())
            ->where('expires_at', '<', now()->addMonths(3))
            ->sum('points');
    }

    private function getRecentServices()
    {
        return Booking::where('clientId', Auth::id())
            ->where('status', 'completed')
            ->with('services')
            ->orderBy('dateTime', 'desc')
            ->take(5)
            ->get()
            ->pluck('services')
            ->flatten();
    }
}