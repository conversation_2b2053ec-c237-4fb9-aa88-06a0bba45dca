<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\LoyaltyTransaction;
use App\Models\LoyaltyReward;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LoyaltyController extends Controller
{
    public function getStatus()
    {
        $user = Auth::user();
        
        $status = [
            'current_points' => $user->loyaltyPoints,
            'lifetime_points' => $user->lifetimeLoyaltyPoints,
            'current_tier' => $this->getCurrentTier($user->loyaltyPoints),
            'next_tier' => $this->getNextTier($user->loyaltyPoints),
            'points_to_next_tier' => $this->getPointsToNextTier($user->loyaltyPoints),
            'expiring_points' => $this->getExpiringPoints(),
            'available_rewards' => $this->getAvailableRewards($user->loyaltyPoints),
        ];

        return response()->json($status);
    }

    public function getTransactionHistory(Request $request)
    {
        $transactions = LoyaltyTransaction::where('userId', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return response()->json($transactions);
    }

    public function redeemReward(Request $request)
    {
        $validated = $request->validate([
            'rewardid' => 'required|exists:loyalty_rewards,id',
        ]);

        $user = Auth::user();
        $reward = LoyaltyReward::find($validated['rewardid']);

        if ($user->loyaltyPoints < $reward->points_required) {
            return response()->json([
                'message' => 'Insufficient points'
            ], 422);
        }

        // Create redemption transaction
        $transaction = LoyaltyTransaction::create([
            'userId' => $user->id,
            'type' => 'redemption',
            'points' => -$reward->points_required,
            'description' => "Redeemed {$reward->name}",
            'rewardId' => $reward->id,
        ]);

        // Update user's points
        $user->deductLoyaltyPoints($reward->points_required);

        // Generate reward code or voucher
        $rewardCode = $this->generateRewardCode();

        return response()->json([
            'message' => 'Reward redeemed successfully',
            'reward_code' => $rewardCode,
            'transaction' => $transaction,
        ]);
    }

    private function getCurrentTier($points)
    {
        $tiers = [
            'bronze' => 0,
            'silver' => 1000,
            'gold' => 2500,
            'platinum' => 5000
        ];

        $currentTier = 'bronze';
        foreach ($tiers as $tier => $requirement) {
            if ($points >= $requirement) {
                $currentTier = $tier;
            } else {
                break;
            }
        }

        return $currentTier;
    }

    private function getNextTier($points)
    {
        $currentTier = $this->getCurrentTier($points);
        $tiers = ['bronze', 'silver', 'gold', 'platinum'];
        $currentIndex = array_search($currentTier, $tiers);
        
        return isset($tiers[$currentIndex + 1]) ? $tiers[$currentIndex + 1] : null;
    }

    private function getPointsToNextTier($points)
    {
        $tiers = [
            'bronze' => 0,
            'silver' => 1000,
            'gold' => 2500,
            'platinum' => 5000
        ];

        foreach ($tiers as $requirement) {
            if ($points < $requirement) {
                return $requirement - $points;
            }
        }

        return 0;
    }

    private function getExpiringPoints()
    {
        return LoyaltyTransaction::where('userId', Auth::id())
            ->where('expires_at', '>', now())
            ->where('expires_at', '<', now()->addMonths(3))
            ->sum('points');
    }

    private function getAvailableRewards($points)
    {
        return LoyaltyReward::where('points_required', '<=', $points)
            ->where('active', true)
            ->get();
    }

    private function generateRewardCode()
    {
        return strtoupper(uniqid('RWD'));
    }
}