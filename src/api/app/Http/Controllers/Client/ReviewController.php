<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Review;
use App\Models\Booking;
use Illuminate\Http\Request;
use App\Http\Resources\ReviewResource;
use Illuminate\Support\Facades\Auth;

class ReviewController extends Controller
{
    public function index()
    {
        $reviews = Review::where('userId', Auth::id())
            ->with(['salon', 'hairdresser'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return ReviewResource::collection($reviews);
    }

    public function store(Request $request, Booking $booking)
    {
        $this->authorize('review', $booking);

        $validated = $request->validate([
            'rating' => 'required|integer|between:1,5',
            'comment' => 'required|string|min:10',
            'hairdresser_rating' => 'required|integer|between:1,5',
            'service_rating' => 'required|integer|between:1,5',
            'ambiance_rating' => 'required|integer|between:1,5',
        ]);

        $review = Review::create([
            'userId' => Auth::id(),
            'salonId' => $booking->salonId,
            'hairdresserId' => $booking->hairdresserId,
            'bookingId' => $booking->id,
            'rating' => $validated['rating'],
            'comment' => $validated['comment'],
            'details' => [
                'hairdresser' => $validated['hairdresser_rating'],
                'service' => $validated['service_rating'],
                'ambiance' => $validated['ambiance_rating'],
            ],
        ]);

        // Award loyalty points for leaving a review
        Auth::user()->addLoyaltyPoints(50, 'Review bonus points');

        return new ReviewResource($review);
    }

    public function update(Request $request, Review $review)
    {
        $this->authorize('update', $review);

        $validated = $request->validate([
            'rating' => 'required|integer|between:1,5',
            'comment' => 'required|string|min:10',
            'hairdresser_rating' => 'required|integer|between:1,5',
            'service_rating' => 'required|integer|between:1,5',
            'ambiance_rating' => 'required|integer|between:1,5',
        ]);

        $review->update([
            'rating' => $validated['rating'],
            'comment' => $validated['comment'],
            'details' => [
                'hairdresser' => $validated['hairdresser_rating'],
                'service' => $validated['service_rating'],
                'ambiance' => $validated['ambiance_rating'],
            ],
        ]);

        return new ReviewResource($review);
    }

    public function destroy(Review $review)
    {
        $this->authorize('delete', $review);
        $review->delete();
        return response()->json(['message' => 'Review deleted successfully']);
    }
}