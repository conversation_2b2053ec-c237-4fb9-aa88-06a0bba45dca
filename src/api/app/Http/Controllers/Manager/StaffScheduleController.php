<?php

namespace App\Http\Controllers\Manager;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Resources\UserResource;
use Illuminate\Support\Facades\Auth;

class StaffScheduleController extends Controller
{
    public function index(Request $request)
    {
        $salon = Auth::user()->managedSalon;
        $date = $request->get('date', today());

        $staff = User::where('salonId', $salon->id)
            ->where('role', 'hairdresser')
            ->with(['bookings' => function ($query) use ($date) {
                $query->whereDate('dateTime', $date)
                      ->with(['client', 'services']);
            }])
            ->get();

        return UserResource::collection($staff);
    }

    public function updateAvailability(Request $request, User $staff)
    {
        $this->authorize('manage', $staff);

        $validated = $request->validate([
            'availability' => 'required|array',
            'availability.*' => 'array',
        ]);

        $staff->update(['availability' => $validated['availability']]);
        return new UserResource($staff);
    }

    public function getWeeklySchedule(Request $request)
    {
        $salon = Auth::user()->managedSalon;
        $startDate = $request->get('start_date', now()->startOfWeek());
        $endDate = $request->get('end_date', now()->endOfWeek());

        $schedule = User::where('salonId', $salon->id)
            ->where('role', 'hairdresser')
            ->with(['bookings' => function ($query) use ($startDate, $endDate) {
                $query->whereBetween('dateTime', [$startDate, $endDate])
                      ->with(['client', 'services']);
            }])
            ->get();

        return response()->json($schedule);
    }

    public function requestTimeOff(Request $request, User $staff)
    {
        $this->authorize('manage', $staff);

        $validated = $request->validate([
            'start_date' => 'required|date|after:now',
            'end_date' => 'required|date|after:start_date',
            'reason' => 'required|string',
        ]);

        $timeOff = $staff->timeOffRequests()->create($validated);

        // Notify relevant parties
        // Implementation depends on your notification system

        return response()->json($timeOff);
    }
}