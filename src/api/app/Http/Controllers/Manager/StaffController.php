<?php

namespace App\Http\Controllers\Manager;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Hairdresser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class StaffController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        $salon = $user->managedSalon;
        
        // Debug: retourner les informations de l'utilisateur
        if (!$salon) {
            return response()->json([
                'message' => 'Vous devez être associé à un salon pour accéder au personnel.',
                'debug' => [
                    'user_id' => $user->id,
                    'user_role' => $user->role,
                    'salon' => $salon
                ]
            ], 403);
        }

        // Récupérer tous les utilisateurs du salon (hairdressers et autres staff)
        $staff = User::where('salonId', $salon->id)
                    ->whereIn('role', ['hairdresser', 'staff'])
                    ->get();

        return response()->json([
            'staff' => $staff,
            'debug' => [
                'salon_id' => $salon->id,
                'salon_name' => $salon->name,
                'staff_count' => $staff->count()
            ]
        ]);
    }

    public function store(Request $request)
    {
        $salon = Auth::user()->managedSalon;
        
        if (!$salon) {
            return response()->json([
                'message' => 'Vous devez être associé à un salon pour ajouter du personnel.'
            ], 403);
        }

        // Debug: afficher les données reçues
        \Log::info('Staff store request data:', $request->all());
        \Log::info('Has file profileImage:', ['hasFile' => $request->hasFile('profileImage')]);

        $validated = $request->validate([
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone' => 'nullable|string|max:20',
            'gender' => 'nullable|in:male,female,other',
            'role' => 'required|in:hairdresser,staff',
            'specialties' => 'nullable|string',
            'availability' => 'nullable|string',
            'profileImage' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max
            'photoUrl' => 'nullable|string', // Ajouter photoUrl
        ]);

        // Gérer l'upload de l'image
        $photoUrl = null;
        if ($request->hasFile('profileImage')) {
            $file = $request->file('profileImage');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('profile_images', $fileName, 'public');
            $photoUrl = '/storage/' . $filePath;
            \Log::info('Image uploaded successfully:', ['fileName' => $fileName, 'filePath' => $filePath, 'photoUrl' => $photoUrl]);
        } elseif ($request->has('photoUrl') && $request->photoUrl) {
            // Si pas de fichier uploadé mais photoUrl fourni
            $photoUrl = $request->photoUrl;
            \Log::info('Using provided photoUrl:', ['photoUrl' => $photoUrl]);
        } else {
            \Log::info('No profileImage file or photoUrl found in request');
        }

        // Ajouter email_verified_at pour les utilisateurs créés par l'admin
        $validated['email_verified_at'] = now();

        // Créer un mot de passe temporaire
        $tempPassword = 'password123'; // En production, générer un mot de passe aléatoire

        $user = User::create([
            'firstName' => $validated['firstName'],
            'lastName' => $validated['lastName'],
            'email' => $validated['email'],
            'phone' => $validated['phone'],
            'gender' => $validated['gender'],
            'role' => $validated['role'],
            'specialties' => $validated['specialties'],
            'availability' => $validated['availability'],
            'password' => Hash::make($tempPassword),
            'salonId' => $salon->id,
            'status' => 'active',
            'photoUrl' => $photoUrl,
        ]);

        \Log::info('User created:', ['user_id' => $user->id, 'photoUrl' => $user->photoUrl]);

        // Créer l'entrée dans la table hairdressers si le rôle est hairdresser
        if ($validated['role'] === 'hairdresser') {
            $hairdresserData = [
                'userId' => $user->id,
                'salonId' => $salon->id,
                'specialties' => $validated['specialties'] ?? [],
                'availability' => $validated['availability'] ?? [],
                'rating' => 0,
                'active' => true,
            ];
            
            Hairdresser::create($hairdresserData);
            \Log::info('Hairdresser entry created:', ['hairdresser_data' => $hairdresserData]);
        }

        return response()->json([
            'message' => 'Personnel ajouté avec succès',
            'user' => $user,
            'tempPassword' => $tempPassword
        ], 201);
    }

    public function show(User $staff)
    {
        $salon = Auth::user()->managedSalon;
        
        if (!$salon || $staff->salonId !== $salon->id) {
            return response()->json([
                'message' => 'Accès non autorisé.'
            ], 403);
        }

        return response()->json($staff);
    }

    public function update(Request $request, User $staff)
    {
        $salon = Auth::user()->managedSalon;
        
        if (!$salon || $staff->salonId !== $salon->id) {
            return response()->json([
                'message' => 'Accès non autorisé.'
            ], 403);
        }

        // Debug: afficher les données reçues
        \Log::info('Staff update request data:', $request->all());
        \Log::info('Has file profileImage:', ['hasFile' => $request->hasFile('profileImage')]);
        \Log::info('Request method:', ['method' => $request->method()]);
        \Log::info('Content-Type:', ['contentType' => $request->header('Content-Type')]);

        $validated = $request->validate([
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => [
                'required',
                'email',
                Rule::unique('users')->ignore($staff->id)
            ],
            'password' => 'nullable|string|min:6', // Changer en nullable
            'phone' => 'nullable|string|max:20',
            'gender' => 'nullable|in:male,female,other',
            'role' => 'required|in:hairdresser,staff',
            'specialties' => 'nullable|string',
            'availability' => 'nullable|string',
            'status' => 'nullable|in:active,inactive',
            'profileImage' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max
            'photoUrl' => 'nullable|string', // Ajouter photoUrl
        ]);

        // Gérer l'upload de l'image
        if ($request->hasFile('profileImage')) {
            $file = $request->file('profileImage');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('profile_images', $fileName, 'public');
            $validated['photoUrl'] = '/storage/' . $filePath;
            \Log::info('Image uploaded successfully in update:', ['fileName' => $fileName, 'filePath' => $filePath, 'photoUrl' => $validated['photoUrl']]);
        } elseif ($request->has('photoUrl') && $request->photoUrl) {
            // Si pas de fichier uploadé mais photoUrl fourni
            // Extraire le chemin relatif de l'URL complète
            $photoUrl = $request->photoUrl;
            if (strpos($photoUrl, '/storage/') !== false) {
                $validated['photoUrl'] = substr($photoUrl, strpos($photoUrl, '/storage/'));
            } else {
                $validated['photoUrl'] = $photoUrl;
            }
            \Log::info('Using provided photoUrl in update:', ['photoUrl' => $validated['photoUrl']]);
        } else {
            \Log::info('No profileImage file or photoUrl found in update request');
        }

        // Hasher le mot de passe s'il est fourni
        if (isset($validated['password'])) {
            $validated['password'] = Hash::make($validated['password']);
        }

        \Log::info('Data to update:', $validated);
        $staff->update($validated);

        // Mettre à jour l'entrée dans la table hairdressers si le rôle est hairdresser
        if ($validated['role'] === 'hairdresser') {
            $hairdresser = Hairdresser::where('userId', $staff->id)->first();
            if ($hairdresser) {
                $hairdresserData = [];
                
                if (isset($validated['specialties'])) {
                    $hairdresserData['specialties'] = $validated['specialties'];
                }
                
                if (isset($validated['availability'])) {
                    $hairdresserData['availability'] = $validated['availability'];
                }
                
                if (!empty($hairdresserData)) {
                    $hairdresser->update($hairdresserData);
                    \Log::info('Hairdresser entry updated:', ['hairdresser_data' => $hairdresserData]);
                }
            } else {
                // Si l'entrée n'existe pas, la créer
                $hairdresserData = [
                    'userId' => $staff->id,
                    'salonId' => $staff->salonId,
                    'specialties' => $validated['specialties'] ?? [],
                    'availability' => $validated['availability'] ?? [],
                    'rating' => 0,
                    'active' => true,
                ];
                
                Hairdresser::create($hairdresserData);
                \Log::info('Hairdresser entry created during update:', ['hairdresser_data' => $hairdresserData]);
            }
        }

        \Log::info('User updated:', ['user_id' => $staff->id, 'photoUrl' => $staff->photoUrl]);

        return response()->json([
            'message' => 'Personnel modifié avec succès',
            'user' => $staff
        ]);
    }

    public function destroy(User $staff)
    {
        $salon = Auth::user()->managedSalon;
        
        if (!$salon || $staff->salonId !== $salon->id) {
            return response()->json([
                'message' => 'Accès non autorisé.'
            ], 403);
        }

        // Vérifier s'il y a des réservations futures
        if ($staff->bookings()->where('dateTime', '>', now())->exists()) {
            return response()->json([
                'message' => 'Impossible de supprimer un membre du personnel avec des réservations futures.'
            ], 403);
        }

        // Supprimer l'entrée dans la table hairdressers si elle existe
        $hairdresser = Hairdresser::where('userId', $staff->id)->first();
        if ($hairdresser) {
            $hairdresser->delete();
            \Log::info('Hairdresser entry deleted:', ['user_id' => $staff->id]);
        }

        $staff->delete();

        return response()->json([
            'message' => 'Personnel supprimé avec succès'
        ]);
    }

    public function updateAvailability(Request $request, User $staff)
    {
        $salon = Auth::user()->managedSalon;
        
        if (!$salon || $staff->salonId !== $salon->id) {
            return response()->json([
                'message' => 'Accès non autorisé.'
            ], 403);
        }

        $validated = $request->validate([
            'availability' => 'required|string',
        ]);

        $staff->update([
            'availability' => $validated['availability']
        ]);

        return response()->json([
            'message' => 'Disponibilité mise à jour avec succès',
            'user' => $staff
        ]);
    }
} 