<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Hairdresser;
use Illuminate\Http\Request;
use App\Http\Resources\HairdresserResource;

class HairdresserController extends Controller
{
    public function index(Request $request)
    {
        $query = Hairdresser::query();

        if ($request->has('salon_id')) {
            $query->where('salonId', $request->input('salon_id'));
        }

        if ($request->has('specialty')) {
            $query->where('specialties', 'array_contains', $request->input('specialty'));
        }

        $hairdressers = $query->paginate(10);
        return HairdresserResource::collection($hairdressers);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'photoUrl' => 'nullable|url',
            'specialties' => 'required|array',
            'specialties.*' => 'string',
            'availability' => 'required|array',
            'salonId' => 'required|exists:salons,_id',
        ]);

        $hairdresser = Hairdresser::create($validated);
        return new HairdresserResource($hairdresser);
    }

    public function show(Hairdresser $hairdresser)
    {
        return new HairdresserResource($hairdresser->load(['salon', 'bookings']));
    }

    public function update(Request $request, Hairdresser $hairdresser)
    {
        $validated = $request->validate([
            'firstName' => 'sometimes|string|max:255',
            'lastName' => 'sometimes|string|max:255',
            'photoUrl' => 'nullable|url',
            'specialties' => 'sometimes|array',
            'specialties.*' => 'string',
            'availability' => 'sometimes|array',
        ]);

        $hairdresser->update($validated);
        return new HairdresserResource($hairdresser);
    }

    public function destroy(Hairdresser $hairdresser)
    {
        $hairdresser->delete();
        return response()->json(['message' => 'Hairdresser deleted successfully']);
    }

    public function availability(Hairdresser $hairdresser)
    {
        return response()->json([
            'availability' => $hairdresser->availability,
        ]);
    }

    public function updateAvailability(Request $request, Hairdresser $hairdresser)
    {
        $validated = $request->validate([
            'availability' => 'required|array',
            'availability.*' => 'array',
        ]);

        $hairdresser->update([
            'availability' => $validated['availability'],
        ]);

        return response()->json([
            'message' => 'Availability updated successfully',
            'availability' => $hairdresser->availability,
        ]);
    }
}