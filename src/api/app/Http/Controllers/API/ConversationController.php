<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Conversation;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ConversationController extends Controller
{
    /**
     * R<PERSON>cupérer toutes les conversations de l'utilisateur connecté
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        $conversations = Conversation::forUser($user->id)
            ->active()
            ->with(['latestMessage.sender', 'salon', 'booking'])
            ->orderBy('last_message_at', 'desc')
            ->paginate(20);

        return response()->json([
            'conversations' => $conversations->items(),
            'pagination' => [
                'current_page' => $conversations->currentPage(),
                'last_page' => $conversations->lastPage(),
                'per_page' => $conversations->perPage(),
                'total' => $conversations->total(),
            ]
        ]);
    }

    /**
     * <PERSON><PERSON>er une nouvelle conversation
     */
    public function store(Request $request)
    {
        $request->validate([
            'participants' => 'required|array|min:1',
            'participants.*' => 'exists:users,id',
            'title' => 'nullable|string|max:255',
            'type' => 'in:private,group,booking_related',
            'booking_id' => 'nullable|exists:bookings,id',
            'salon_id' => 'nullable|exists:salons,id',
        ]);

        $user = Auth::user();
        $participants = $request->participants;

        // Ajouter l'utilisateur connecté aux participants s'il n'y est pas
        if (!in_array($user->id, $participants)) {
            $participants[] = $user->id;
        }

        // Vérifier si une conversation privée existe déjà entre ces participants
        if ($request->type === 'private' && count($participants) === 2) {
            $existingConversation = Conversation::where('type', 'private')
                ->where(function ($query) use ($participants) {
                    $query->whereJsonContains('participants', $participants[0])
                          ->whereJsonContains('participants', $participants[1]);
                })
                ->first();

            if ($existingConversation) {
                return response()->json([
                    'conversation' => $existingConversation->load(['latestMessage.sender', 'salon', 'booking'])
                ]);
            }
        }

        $conversation = Conversation::create([
            'title' => $request->title,
            'type' => $request->type ?? 'private',
            'booking_id' => $request->booking_id,
            'salon_id' => $request->salon_id,
            'participants' => $participants,
            'created_by' => $user->id,
            'is_active' => true,
        ]);

        return response()->json([
            'conversation' => $conversation->load(['latestMessage.sender', 'salon', 'booking'])
        ], 201);
    }

    /**
     * Afficher une conversation spécifique
     */
    public function show(Conversation $conversation)
    {
        $user = Auth::user();

        // Vérifier que l'utilisateur est participant
        if (!$conversation->isParticipant($user->id)) {
            return response()->json(['message' => 'Accès non autorisé'], 403);
        }

        $conversation->load(['messages.sender', 'salon', 'booking']);

        // Marquer les messages comme lus
        $conversation->messages()
            ->where('sender_id', '!=', $user->id)
            ->whereNull('read_at')
            ->update(['read_at' => now()]);

        return response()->json(['conversation' => $conversation]);
    }

    /**
     * Mettre à jour une conversation
     */
    public function update(Request $request, Conversation $conversation)
    {
        $user = Auth::user();

        if (!$conversation->isParticipant($user->id)) {
            return response()->json(['message' => 'Accès non autorisé'], 403);
        }

        $request->validate([
            'title' => 'nullable|string|max:255',
            'is_active' => 'boolean',
        ]);

        $conversation->update($request->only(['title', 'is_active']));

        return response()->json(['conversation' => $conversation]);
    }

    /**
     * Supprimer une conversation (soft delete)
     */
    public function destroy(Conversation $conversation)
    {
        $user = Auth::user();

        if ($conversation->created_by !== $user->id) {
            return response()->json(['message' => 'Seul le créateur peut supprimer cette conversation'], 403);
        }

        $conversation->delete();

        return response()->json(['message' => 'Conversation supprimée avec succès']);
    }

    /**
     * Ajouter un participant à une conversation
     */
    public function addParticipant(Request $request, Conversation $conversation)
    {
        $user = Auth::user();

        if (!$conversation->isParticipant($user->id)) {
            return response()->json(['message' => 'Accès non autorisé'], 403);
        }

        $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        $conversation->addParticipant($request->user_id);

        return response()->json(['message' => 'Participant ajouté avec succès']);
    }

    /**
     * Retirer un participant d'une conversation
     */
    public function removeParticipant(Request $request, Conversation $conversation)
    {
        $user = Auth::user();

        if ($conversation->created_by !== $user->id && $request->user_id !== $user->id) {
            return response()->json(['message' => 'Accès non autorisé'], 403);
        }

        $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        $conversation->removeParticipant($request->user_id);

        return response()->json(['message' => 'Participant retiré avec succès']);
    }

    /**
     * Obtenir les participants d'une conversation
     */
    public function participants(Conversation $conversation)
    {
        $user = Auth::user();

        if (!$conversation->isParticipant($user->id)) {
            return response()->json(['message' => 'Accès non autorisé'], 403);
        }

        $participants = User::whereIn('id', $conversation->participants)
            ->select('id', 'firstName', 'lastName', 'email', 'photoUrl', 'role')
            ->get();

        return response()->json(['participants' => $participants]);
    }

    /**
     * Rechercher des conversations
     */
    public function search(Request $request)
    {
        $user = Auth::user();
        $query = $request->get('q');

        if (!$query) {
            return response()->json(['conversations' => []]);
        }

        $conversations = Conversation::forUser($user->id)
            ->active()
            ->where(function ($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhereHas('messages', function ($messageQuery) use ($query) {
                      $messageQuery->where('content', 'like', "%{$query}%");
                  });
            })
            ->with(['latestMessage.sender', 'salon', 'booking'])
            ->orderBy('last_message_at', 'desc')
            ->limit(10)
            ->get();

        return response()->json(['conversations' => $conversations]);
    }
}
