<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\PaymentMethod;
use App\Models\Booking;
use App\Services\StripeService;
use App\Services\PayPalService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Exception;

class PaymentController extends Controller
{
    protected $stripeService;
    protected $paypalService;

    public function __construct(StripeService $stripeService, PayPalService $paypalService)
    {
        $this->stripeService = $stripeService;
        $this->paypalService = $paypalService;
    }

    /**
     * Récupérer les paiements de l'utilisateur
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        $payments = Payment::where('user_id', $user->id)
            ->with(['booking.salon', 'booking.hairdresser.user'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return response()->json([
            'payments' => $payments->items(),
            'pagination' => [
                'current_page' => $payments->currentPage(),
                'last_page' => $payments->lastPage(),
                'per_page' => $payments->perPage(),
                'total' => $payments->total(),
            ]
        ]);
    }

    /**
     * Créer un Payment Intent Stripe
     */
    public function createStripePaymentIntent(Request $request)
    {
        $request->validate([
            'booking_id' => 'required|exists:bookings,id',
            'payment_method_id' => 'nullable|string',
        ]);

        $user = Auth::user();
        $booking = Booking::findOrFail($request->booking_id);

        // Vérifier que l'utilisateur est propriétaire de la réservation
        if ($booking->clientId !== $user->id) {
            return response()->json(['message' => 'Accès non autorisé'], 403);
        }

        try {
            $result = $this->stripeService->createPaymentIntent($booking, $request->payment_method_id);

            return response()->json([
                'client_secret' => $result['payment_intent']->client_secret,
                'payment_intent_id' => $result['payment_intent']->id,
                'payment' => $result['payment'],
            ]);

        } catch (Exception $e) {
            return response()->json(['message' => $e->getMessage()], 400);
        }
    }

    /**
     * Confirmer un Payment Intent Stripe
     */
    public function confirmStripePayment(Request $request)
    {
        $request->validate([
            'payment_intent_id' => 'required|string',
            'payment_method_id' => 'nullable|string',
        ]);

        try {
            $paymentIntent = $this->stripeService->confirmPaymentIntent(
                $request->payment_intent_id,
                $request->payment_method_id
            );

            return response()->json([
                'payment_intent' => $paymentIntent,
                'status' => $paymentIntent->status,
            ]);

        } catch (Exception $e) {
            return response()->json(['message' => $e->getMessage()], 400);
        }
    }

    /**
     * Créer une commande PayPal
     */
    public function createPayPalOrder(Request $request)
    {
        $request->validate([
            'booking_id' => 'required|exists:bookings,id',
            'return_url' => 'required|url',
            'cancel_url' => 'required|url',
        ]);

        $user = Auth::user();
        $booking = Booking::findOrFail($request->booking_id);

        // Vérifier que l'utilisateur est propriétaire de la réservation
        if ($booking->clientId !== $user->id) {
            return response()->json(['message' => 'Accès non autorisé'], 403);
        }

        try {
            $result = $this->paypalService->createOrder(
                $booking,
                $request->return_url,
                $request->cancel_url
            );

            return response()->json([
                'order_id' => $result['order']['id'],
                'approval_url' => $this->paypalService->getApprovalUrl($result['order']),
                'payment' => $result['payment'],
            ]);

        } catch (Exception $e) {
            return response()->json(['message' => $e->getMessage()], 400);
        }
    }

    /**
     * Capturer une commande PayPal
     */
    public function capturePayPalOrder(Request $request)
    {
        $request->validate([
            'order_id' => 'required|string',
        ]);

        try {
            $captureResult = $this->paypalService->captureOrder($request->order_id);

            return response()->json([
                'capture' => $captureResult,
                'status' => $captureResult['status'],
            ]);

        } catch (Exception $e) {
            return response()->json(['message' => $e->getMessage()], 400);
        }
    }

    /**
     * Afficher un paiement spécifique
     */
    public function show(Payment $payment)
    {
        $user = Auth::user();

        if ($payment->user_id !== $user->id) {
            return response()->json(['message' => 'Accès non autorisé'], 403);
        }

        $payment->load(['booking.salon', 'booking.hairdresser.user']);

        return response()->json(['payment' => $payment]);
    }

    /**
     * Créer un remboursement
     */
    public function refund(Request $request, Payment $payment)
    {
        $request->validate([
            'amount' => 'nullable|numeric|min:0.01',
            'reason' => 'nullable|string|max:500',
        ]);

        $user = Auth::user();

        // Vérifier les permissions (propriétaire du salon ou admin)
        if (!$user->hasRole(['admin', 'owner']) && $payment->booking->salon->ownerId !== $user->id) {
            return response()->json(['message' => 'Accès non autorisé'], 403);
        }

        if (!$payment->canBeRefunded()) {
            return response()->json(['message' => 'Ce paiement ne peut pas être remboursé'], 400);
        }

        try {
            if ($payment->isStripe()) {
                $refund = $this->stripeService->createRefund($payment, $request->amount);
            } elseif ($payment->isPayPal()) {
                $refund = $this->paypalService->createRefund($payment, $request->amount);
            } else {
                return response()->json(['message' => 'Méthode de paiement non supportée'], 400);
            }

            return response()->json([
                'message' => 'Remboursement effectué avec succès',
                'refund' => $refund,
                'payment' => $payment->fresh(),
            ]);

        } catch (Exception $e) {
            return response()->json(['message' => $e->getMessage()], 400);
        }
    }

    /**
     * Récupérer les méthodes de paiement de l'utilisateur
     */
    public function paymentMethods()
    {
        $user = Auth::user();

        $paymentMethods = PaymentMethod::where('user_id', $user->id)
            ->active()
            ->orderBy('is_default', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json(['payment_methods' => $paymentMethods]);
    }

    /**
     * Sauvegarder une méthode de paiement Stripe
     */
    public function saveStripePaymentMethod(Request $request)
    {
        $request->validate([
            'payment_method_id' => 'required|string',
            'make_default' => 'boolean',
        ]);

        $user = Auth::user();

        try {
            $paymentMethod = $this->stripeService->savePaymentMethod(
                $user,
                $request->payment_method_id,
                $request->make_default ?? false
            );

            return response()->json([
                'message' => 'Méthode de paiement sauvegardée avec succès',
                'payment_method' => $paymentMethod,
            ]);

        } catch (Exception $e) {
            return response()->json(['message' => $e->getMessage()], 400);
        }
    }

    /**
     * Supprimer une méthode de paiement
     */
    public function deletePaymentMethod(PaymentMethod $paymentMethod)
    {
        $user = Auth::user();

        if ($paymentMethod->user_id !== $user->id) {
            return response()->json(['message' => 'Accès non autorisé'], 403);
        }

        try {
            if ($paymentMethod->isStripe()) {
                $this->stripeService->deletePaymentMethod($paymentMethod);
            } else {
                $paymentMethod->delete();
            }

            return response()->json(['message' => 'Méthode de paiement supprimée avec succès']);

        } catch (Exception $e) {
            return response()->json(['message' => $e->getMessage()], 400);
        }
    }

    /**
     * Définir une méthode de paiement par défaut
     */
    public function setDefaultPaymentMethod(PaymentMethod $paymentMethod)
    {
        $user = Auth::user();

        if ($paymentMethod->user_id !== $user->id) {
            return response()->json(['message' => 'Accès non autorisé'], 403);
        }

        $paymentMethod->makeDefault();

        return response()->json([
            'message' => 'Méthode de paiement définie par défaut',
            'payment_method' => $paymentMethod,
        ]);
    }
}
