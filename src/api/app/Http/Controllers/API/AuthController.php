<?php

namespace App\Http\Controllers\API;

use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\UserResource;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use App\Notifications\VerifyEmailNotification;

class AuthController extends Controller
{

    public function register(Request $request)
    {
        $request->headers->set('Accept', 'application/json');

        // Vérifier si l'email existe déjà
        $existingUser = User::where('email', $request->email)->first();

        if ($existingUser) {
            return response()->json([
                'message' => 'Cet email est déjà utilisé par un autre compte.',
                'errors' => [
                    'email' => ['Cet email est déjà utilisé.']
                ]
            ], 422);
        }

        // Messages de validation personnalisés
        $messages = [
            'firstName.required' => 'Le prénom est obligatoire.',
            'lastName.required' => 'Le nom est obligatoire.',
            'email.required' => 'L\'adresse email est obligatoire.',
            'email.email' => 'Veuillez entrer une adresse email valide.',
            'email.unique' => 'Cette adresse email est déjà utilisée.',
            'password.required' => 'Le mot de passe est obligatoire.',
            'password.min' => 'Le mot de passe doit contenir au moins :min caractères.',
            'password.confirmed' => 'La confirmation du mot de passe ne correspond pas.',
            'gender.required' => 'Le genre est obligatoire.',
            'gender.in' => 'Le genre sélectionné est invalide.',
        ];

        // Règles de validation
        $validatedData = $request->validate([
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'password' => 'required|string|min:8|confirmed', // Ajout de la règle confirmed
            'phone' => 'nullable|string',
            'gender' => 'required|in:male,female,other',
            'role' => 'nullable|in:admin,owner,manager,hairdresser,client',
        ], $messages);

        // Création de l'utilisateur
        $user = User::create([
            'firstName' => $validatedData['firstName'],
            'lastName' => $validatedData['lastName'],
            'email' => $validatedData['email'],
            'password' => Hash::make($validatedData['password']),
            'phone' => $validatedData['phone'] ?? null,
            'gender' => $validatedData['gender'],
            'role' => 'client',
        ]);

        // Générer un code de vérification
        $verificationCode = mt_rand(100000, 999999); // Code à 6 chiffres

        // Envoyer la notification avec le code
        $user->notify(new VerifyEmailNotification($verificationCode));

        // Optionnel: stocker le code dans la base de données pour vérification ultérieure
        $user->verification_code = $verificationCode;
        $user->save();

        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json([
            'user' => $user,
            'access_token' => $token,
            'token_type' => 'Bearer',
        ], 201);
    }

    public function verifyEmail(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'code' => 'required|numeric'
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user) {
            return response()->json(['message' => 'Utilisateur non trouvé'], 404);
        }

        // Vérifier si le code correspond
        if ($user->verification_code != $request->code) {
            return response()->json(['message' => 'Code de vérification invalide'], 422);
        }

        // Vérifier si le code a expiré (15 minutes)
        if ($user->updated_at->diffInMinutes(now()) > 15) {
            return response()->json(['message' => 'Le code a expiré. Veuillez en demander un nouveau.'], 422);
        }

        // Marquer l'email comme vérifié
        $user->email_verified_at = now();
        $user->verification_code = null;
        $user->save();

        return response()->json(['message' => 'Email vérifié avec succès']);
    }

    public function verifyEmailForgotPassword(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'code' => 'required|numeric'
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user) {
            return response()->json(['message' => 'Utilisateur non trouvé'], 404);
        }

        // Vérification du code sans le supprimer
        if ($user->verification_code != $request->code) {
            return response()->json(['message' => 'Code de vérification invalide'], 422);
        }

        if ($user->updated_at->diffInMinutes(now()) > 15) {
            return response()->json(['message' => 'Le code a expiré'], 422);
        }

        // Ne pas marquer comme vérifié ni supprimer le code ici
        return response()->json([
            'message' => 'Code validé avec succès',
            'valid' => true
        ]);
    }

    public function resendVerificationCode(Request $request)
    {
        $request->validate(['email' => 'required|email']);

        $user = User::where('email', $request->email)->first();

        if (!$user) {
            return response()->json(['message' => 'Utilisateur non trouvé'], 404);
        }

        $verificationCode = mt_rand(100000, 999999);
        $user->verification_code = $verificationCode;
        $user->save();

        $user->notify(new VerifyEmailNotification($verificationCode));

        return response()->json(['message' => 'Un nouveau code a été envoyé']);
    }


    // public function verify(Request $request, $id)
    // {
    //     $user = User::findOrFail($id);

    //     if (! $request->hasValidSignature()) {
    //         return response()->json(['message' => 'Lien de vérification invalide ou expiré'], 401);
    //     }

    //     if ($user->hasVerifiedEmail()) {
    //         return response()->json(['message' => 'Email déjà vérifié'], 400);
    //     }

    //     $user->markEmailAsVerified();

    //     return response()->json(['message' => 'Email vérifié avec succès']);
    // }

    public function login(Request $request)
    {
        $request->headers->set('Accept', 'application/json');

        // Messages de validation personnalisés
        $messages = [
            'email.required' => 'L\'adresse email est obligatoire.',
            'email.email' => 'Veuillez entrer une adresse email valide.',
            'password.required' => 'Le mot de passe est obligatoire.',
        ];

        // Validation des données
        $validatedData = $request->validate([
            'email' => 'required|email',
            'password' => 'required',
            'device_name' => 'nullable|string',
        ], $messages);

        // Récupérer l'utilisateur
        $user = User::where('email', $validatedData['email'])->first();

        // Vérifications avec messages d'erreur clairs
        if (!$user) {
            return response()->json([
                'message' => 'Authentification échouée',
                'errors' => [
                    'email' => ['Aucun compte trouvé avec cette adresse email.']
                ]
            ], 401);
        }

        if (!Hash::check($validatedData['password'], $user->password)) {
            return response()->json([
                'message' => 'Authentification échouée',
                'errors' => [
                    'password' => ['Le mot de passe est incorrect.']
                ]
            ], 401);
        }

        if (!$user->hasVerifiedEmail()) {
            return response()->json([
                'message' => 'Email non vérifié',
                'errors' => [
                    'email' => ['Vous devez vérifier votre email avant de vous connecter.']
                ]
            ], 403);
        }

        if ($user->status !== 'active') {
            return response()->json([
                'message' => 'Compte désactivé',
                'errors' => [
                    'email' => ['Votre compte est désactivé. Contactez le support.']
                ]
            ], 403);
        }

        // Création du token
        $token = $user->createToken($validatedData['device_name'] ?? 'auth_token')->plainTextToken;

        return response()->json([
            'user' => new UserResource($user),
            'access_token' => $token,
            'token_type' => 'Bearer',
        ]);
    }

    public function resetPassword(Request $request)
    {
        // Force la réponse JSON
        $request->headers->set('Accept', 'application/json');

        $messages = [
            'email.required' => 'L\'adresse email est obligatoire.',
            'email.email' => 'Veuillez entrer une adresse email valide.',
            'password.required' => 'Le mot de passe est obligatoire.',
            'password.min' => 'Le mot de passe doit contenir au moins 8 caractères.',
            'password_confirmation.same' => 'Les mots de passe ne correspondent pas.',
            'token.required' => 'Le code de vérification est requis.'
        ];

        // Validation des données
        $validatedData = $request->validate([
            'email' => 'required|email',
            'token' => 'required|string|digits:6', // Validation du format 6 chiffres
            'password' => 'required|min:8',
            'password_confirmation' => 'required|same:password'
        ], $messages);

        // Récupération de l'utilisateur
        $user = User::where('email', $validatedData['email'])->first();

        if (!$user) {
            return response()->json([
                'message' => 'Réinitialisation échouée',
                'errors' => ['email' => ['Aucun compte trouvé avec cette adresse email.']]
            ], 404);
        }

        // Debug des valeurs
        \Log::debug('Tentative de réinitialisation', [
            'user_id' => $user->id,
            'input_token' => $validatedData['token'],
            'stored_code' => $user->verification_code,
            'code_match' => $user->verification_code === $validatedData['token'],
            'last_updated' => $user->updated_at,
            'expired' => $user->updated_at->diffInMinutes(now()) >= 15
        ]);

        // Validation du token
        if (!$this->isValidToken($user, $validatedData['token'])) {
            return response()->json([
                'message' => 'Code invalide ou expiré',
                'errors' => ['token' => ['Le code de vérification est invalide ou a expiré.']]
            ], 401);
        }

        // Mise à jour du mot de passe
        $user->password = Hash::make($validatedData['password']);
        $user->verification_code = null; // Invalidation du code
        $user->email_verified_at = now(); // Marquer l'email comme vérifié
        $user->save();

        // Révoquer tous les tokens existants (sécurité)
        $user->tokens()->delete();

        return response()->json([
            'message' => 'Mot de passe réinitialisé avec succès',
            'user' => new UserResource($user)
        ]);
    }

    protected function isValidToken($user, $token)
    {
        // Vérification basique du format
        if (!preg_match('/^\d{6}$/', $token)) {
            \Log::warning('Format de token invalide', ['token' => $token]);
            return false;
        }

        // Vérification complète
        return $user->verification_code === $token
            && $user->updated_at
            && $user->updated_at->diffInMinutes(now()) < 15;
    }

    public function logout(Request $request)
    {
        // Revoke the current token
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'message' => 'Successfully logged out',
        ]);
    }

    public function user(Request $request)
    {
        return new UserResource($request->user());
    }

    public function refresh(Request $request)
    {
        $user = $request->user();

        // Revoke all tokens and create a new one
        $user->tokens()->delete();
        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json([
            'user' => new UserResource($user),
            'access_token' => $token,
            'token_type' => 'Bearer',
        ]);
    }

    public function updatePassword(Request $request)
    {
        $validated = $request->validate([
            'current_password' => 'required',
            'password' => 'required|string|min:8|confirmed|different:current_password',
        ]);

        $user = $request->user();

        if (!Hash::check($validated['current_password'], $user->password)) {
            throw ValidationException::withMessages([
                'current_password' => ['The provided password is incorrect.'],
            ]);
        }

        $user->update([
            'password' => Hash::make($validated['password']),
        ]);

        // Revoke all tokens
        $user->tokens()->delete();
        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json([
            'message' => 'Password updated successfully',
            'access_token' => $token,
            'token_type' => 'Bearer',
        ]);
    }

    public function updateProfile(Request $request)
    {
        $user = $request->user();

        // Messages de validation personnalisés
        $messages = [
            'firstName.required' => 'Le prénom est obligatoire.',
            'lastName.required' => 'Le nom est obligatoire.',
            'email.required' => 'L\'adresse email est obligatoire.',
            'email.email' => 'Veuillez entrer une adresse email valide.',
            'email.unique' => 'Cette adresse email est déjà utilisée.',
            'phone.string' => 'Le numéro de téléphone doit être une chaîne de caractères.',
            'gender.required' => 'Le genre est obligatoire.',
            'gender.in' => 'Le genre sélectionné est invalide.',
            'photo.image' => 'Le fichier doit être une image.',
            'photo.mimes' => 'L\'image doit être au format : jpeg, png, jpg, gif.',
            'photo.max' => 'L\'image ne doit pas dépasser 2MB.',
        ];

        // Règles de validation
        $rules = [
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'gender' => 'required|in:male,female,other',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ];

        $validatedData = $request->validate($rules, $messages);

        // Vérifier si l'email existe déjà pour un autre utilisateur
        $existingUser = User::where('email', $validatedData['email'])
            ->where('id', '!=', $user->id)
            ->first();

        if ($existingUser) {
            return response()->json([
                'message' => 'Cet email est déjà utilisé par un autre compte.',
                'errors' => [
                    'email' => ['Cet email est déjà utilisé.']
                ]
            ], 422);
        }

        // Gérer l'upload de la photo de profil
        if ($request->hasFile('photo')) {
            // Supprimer l'ancienne photo si elle existe
            if ($user->photoUrl && !str_contains($user->photoUrl, 'unsplash.com')) {
                $oldPhotoPath = str_replace('/storage/', '', $user->photoUrl);
                if (\Storage::disk('public')->exists($oldPhotoPath)) {
                    \Storage::disk('public')->delete($oldPhotoPath);
                }
            }

            // Sauvegarder la nouvelle photo
            $photoPath = $request->file('photo')->store('uploads/users', 'public');
            $validatedData['photoUrl'] = '/storage/' . $photoPath;
        }

        // Mettre à jour l'utilisateur
        $user->update($validatedData);

        return response()->json([
            'message' => 'Profil mis à jour avec succès',
            'user' => new UserResource($user)
        ]);
    }

    public function changePassword(Request $request)
    {
        $messages = [
            'current_password.required' => 'Le mot de passe actuel est obligatoire.',
            'new_password.required' => 'Le nouveau mot de passe est obligatoire.',
            'new_password.min' => 'Le nouveau mot de passe doit contenir au moins 8 caractères.',
            'new_password_confirmation.required' => 'La confirmation du mot de passe est obligatoire.',
            'new_password_confirmation.same' => 'La confirmation du mot de passe ne correspond pas.',
        ];

        $validated = $request->validate([
            'current_password' => 'required',
            'new_password' => 'required|string|min:8',
            'new_password_confirmation' => 'required|same:new_password',
        ], $messages);

        $user = $request->user();

        if (!Hash::check($validated['current_password'], $user->password)) {
            return response()->json([
                'message' => 'Mot de passe incorrect',
                'errors' => [
                    'current_password' => ['Le mot de passe actuel est incorrect.']
                ]
            ], 422);
        }

        $user->update([
            'password' => Hash::make($validated['new_password']),
        ]);

        // Révoquer tous les tokens existants pour la sécurité
        $user->tokens()->delete();
        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json([
            'message' => 'Mot de passe modifié avec succès',
            'access_token' => $token,
            'token_type' => 'Bearer',
        ]);
    }

    public function updatePhoto(Request $request)
    {
        $user = $request->user();

        // Messages de validation personnalisés
        $messages = [
            'photo.required' => 'La photo est obligatoire.',
            'photo.image' => 'Le fichier doit être une image.',
            'photo.mimes' => 'L\'image doit être au format : jpeg, png, jpg, gif.',
            'photo.max' => 'L\'image ne doit pas dépasser 2MB.',
        ];

        // Règles de validation
        $rules = [
            'photo' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ];

        $validatedData = $request->validate($rules, $messages);

        // Gérer l'upload de la photo de profil
        if ($request->hasFile('photo')) {
            // Supprimer l'ancienne photo si elle existe
            if ($user->photoUrl && !str_contains($user->photoUrl, 'unsplash.com')) {
                $oldPhotoPath = str_replace('/storage/', '', $user->photoUrl);
                if (\Storage::disk('public')->exists($oldPhotoPath)) {
                    \Storage::disk('public')->delete($oldPhotoPath);
                }
            }

            // Sauvegarder la nouvelle photo
            $photoPath = $request->file('photo')->store('uploads/users', 'public');
            $validatedData['photoUrl'] = '/storage/' . $photoPath;
        }

        // Mettre à jour l'utilisateur
        $user->update($validatedData);

        return response()->json([
            'message' => 'Photo de profil mise à jour avec succès',
            'user' => new UserResource($user)
        ]);
    }
}
