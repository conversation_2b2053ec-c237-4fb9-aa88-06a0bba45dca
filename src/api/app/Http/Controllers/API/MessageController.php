<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Message;
use App\Models\Conversation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class MessageController extends Controller
{
    /**
     * Récupérer les messages d'une conversation
     */
    public function index(Request $request, Conversation $conversation)
    {
        $user = Auth::user();

        if (!$conversation->isParticipant($user->id)) {
            return response()->json(['message' => 'Accès non autorisé'], 403);
        }

        $messages = $conversation->messages()
            ->with('sender:id,firstName,lastName,photoUrl')
            ->orderBy('created_at', 'desc')
            ->paginate(50);

        return response()->json([
            'messages' => $messages->items(),
            'pagination' => [
                'current_page' => $messages->currentPage(),
                'last_page' => $messages->lastPage(),
                'per_page' => $messages->perPage(),
                'total' => $messages->total(),
            ]
        ]);
    }

    /**
     * Envoyer un nouveau message
     */
    public function store(Request $request, Conversation $conversation)
    {
        $user = Auth::user();

        if (!$conversation->isParticipant($user->id)) {
            return response()->json(['message' => 'Accès non autorisé'], 403);
        }

        $request->validate([
            'content' => 'required_without:file|string|max:5000',
            'type' => 'in:text,image,file,system',
            'file' => 'nullable|file|max:10240', // 10MB max
        ]);

        $messageData = [
            'conversation_id' => $conversation->id,
            'sender_id' => $user->id,
            'content' => $request->content ?? '',
            'type' => $request->type ?? Message::TYPE_TEXT,
        ];

        // Gestion des fichiers
        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('messages', $fileName, 'public');

            $messageData['file_path'] = $filePath;
            $messageData['file_name'] = $file->getClientOriginalName();
            $messageData['file_type'] = $file->getMimeType();
            $messageData['file_size'] = $file->getSize();

            // Déterminer le type de message basé sur le type de fichier
            if (str_starts_with($file->getMimeType(), 'image/')) {
                $messageData['type'] = Message::TYPE_IMAGE;
            } else {
                $messageData['type'] = Message::TYPE_FILE;
            }
        }

        $message = Message::create($messageData);

        // Mettre à jour le timestamp de la conversation
        $conversation->updateLastMessageTime();

        return response()->json([
            'message' => $message->load('sender:id,firstName,lastName,photoUrl')
        ], 201);
    }

    /**
     * Afficher un message spécifique
     */
    public function show(Message $message)
    {
        $user = Auth::user();

        if (!$message->conversation->isParticipant($user->id)) {
            return response()->json(['message' => 'Accès non autorisé'], 403);
        }

        return response()->json([
            'message' => $message->load('sender:id,firstName,lastName,photoUrl')
        ]);
    }

    /**
     * Modifier un message
     */
    public function update(Request $request, Message $message)
    {
        $user = Auth::user();

        if ($message->sender_id !== $user->id) {
            return response()->json(['message' => 'Vous ne pouvez modifier que vos propres messages'], 403);
        }

        if ($message->type !== Message::TYPE_TEXT) {
            return response()->json(['message' => 'Seuls les messages texte peuvent être modifiés'], 400);
        }

        $request->validate([
            'content' => 'required|string|max:5000',
        ]);

        $message->edit($request->content);

        return response()->json([
            'message' => $message->load('sender:id,firstName,lastName,photoUrl')
        ]);
    }

    /**
     * Supprimer un message
     */
    public function destroy(Message $message)
    {
        $user = Auth::user();

        if ($message->sender_id !== $user->id) {
            return response()->json(['message' => 'Vous ne pouvez supprimer que vos propres messages'], 403);
        }

        // Supprimer le fichier associé s'il existe
        if ($message->file_path) {
            Storage::disk('public')->delete($message->file_path);
        }

        $message->delete();

        return response()->json(['message' => 'Message supprimé avec succès']);
    }

    /**
     * Marquer un message comme lu
     */
    public function markAsRead(Message $message)
    {
        $user = Auth::user();

        if (!$message->conversation->isParticipant($user->id)) {
            return response()->json(['message' => 'Accès non autorisé'], 403);
        }

        $message->markAsRead($user->id);

        return response()->json(['message' => 'Message marqué comme lu']);
    }

    /**
     * Marquer tous les messages d'une conversation comme lus
     */
    public function markAllAsRead(Conversation $conversation)
    {
        $user = Auth::user();

        if (!$conversation->isParticipant($user->id)) {
            return response()->json(['message' => 'Accès non autorisé'], 403);
        }

        $conversation->messages()
            ->where('sender_id', '!=', $user->id)
            ->whereNull('read_at')
            ->update(['read_at' => now()]);

        return response()->json(['message' => 'Tous les messages marqués comme lus']);
    }
}
