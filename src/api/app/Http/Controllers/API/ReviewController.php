<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Review;
use Illuminate\Http\Request;
use App\Http\Resources\ReviewResource;

class ReviewController extends Controller
{
    public function index(Request $request)
    {
        $query = Review::query();

        if ($request->has('salon_id')) {
            $query->where('salonId', $request->input('salon_id'));
        }

        if ($request->has('rating')) {
            $query->where('rating', '>=', $request->input('rating'));
        }

        if ($request->has('source')) {
            $query->where('source', $request->input('source'));
        }

        $reviews = $query->with(['user', 'salon'])->latest('date')->paginate(10);
        return ReviewResource::collection($reviews);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'salonId' => 'required|exists:salons,_id',
            'rating' => 'required|integer|between:1,5',
            'comment' => 'required|string',
            'source' => 'required|in:google,trustpilot',
            'date' => 'required|date',
        ]);

        $validated['userId'] = auth()->id();

        $review = Review::create($validated);
        return new ReviewResource($review->load(['user', 'salon']));
    }

    public function show(Review $review)
    {
        return new ReviewResource($review->load(['user', 'salon']));
    }

    public function update(Request $request, Review $review)
    {
        $this->authorize('update', $review);

        $validated = $request->validate([
            'rating' => 'sometimes|integer|between:1,5',
            'comment' => 'sometimes|string',
        ]);

        $review->update($validated);
        return new ReviewResource($review->load(['user', 'salon']));
    }

    public function destroy(Review $review)
    {
        $this->authorize('delete', $review);

        $review->delete();
        return response()->json(['message' => 'Review deleted successfully']);
    }
}