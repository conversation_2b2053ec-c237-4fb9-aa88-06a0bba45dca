<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\Booking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Stripe\Webhook;
use Stripe\Exception\SignatureVerificationException;
use Exception;

class WebhookController extends Controller
{
    /**
     * Gérer les webhooks Stripe
     */
    public function stripeWebhook(Request $request)
    {
        $payload = $request->getContent();
        $sigHeader = $request->header('Stripe-Signature');
        $endpointSecret = config('services.stripe.webhook.secret');

        try {
            $event = Webhook::constructEvent($payload, $sigHeader, $endpointSecret);
        } catch (SignatureVerificationException $e) {
            Log::error('Stripe webhook signature verification failed: ' . $e->getMessage());
            return response()->json(['error' => 'Invalid signature'], 400);
        } catch (Exception $e) {
            Log::error('Stripe webhook error: ' . $e->getMessage());
            return response()->json(['error' => 'Webhook error'], 400);
        }

        // Gérer les différents types d'événements
        switch ($event->type) {
            case 'payment_intent.succeeded':
                $this->handlePaymentIntentSucceeded($event->data->object);
                break;

            case 'payment_intent.payment_failed':
                $this->handlePaymentIntentFailed($event->data->object);
                break;

            case 'payment_intent.canceled':
                $this->handlePaymentIntentCanceled($event->data->object);
                break;

            case 'charge.dispute.created':
                $this->handleChargeDisputeCreated($event->data->object);
                break;

            default:
                Log::info('Unhandled Stripe webhook event: ' . $event->type);
        }

        return response()->json(['status' => 'success']);
    }

    /**
     * Gérer les webhooks PayPal
     */
    public function paypalWebhook(Request $request)
    {
        $payload = $request->all();

        // Vérifier la signature PayPal (optionnel mais recommandé)
        if (!$this->verifyPayPalWebhook($request)) {
            Log::error('PayPal webhook signature verification failed');
            return response()->json(['error' => 'Invalid signature'], 400);
        }

        $eventType = $payload['event_type'] ?? null;

        switch ($eventType) {
            case 'CHECKOUT.ORDER.APPROVED':
                $this->handlePayPalOrderApproved($payload);
                break;

            case 'PAYMENT.CAPTURE.COMPLETED':
                $this->handlePayPalCaptureCompleted($payload);
                break;

            case 'PAYMENT.CAPTURE.DENIED':
                $this->handlePayPalCaptureDenied($payload);
                break;

            default:
                Log::info('Unhandled PayPal webhook event: ' . $eventType);
        }

        return response()->json(['status' => 'success']);
    }

    /**
     * Gérer le succès d'un Payment Intent Stripe
     */
    private function handlePaymentIntentSucceeded($paymentIntent)
    {
        $payment = Payment::where('payment_intent_id', $paymentIntent->id)->first();

        if ($payment && !$payment->isSuccessful()) {
            $payment->markAsPaid();

            // Mettre à jour le statut de la réservation
            $booking = $payment->booking;
            if ($booking && $booking->status === 'pending_payment') {
                $booking->update(['status' => 'confirmed']);
            }

            Log::info("Payment succeeded for booking #{$booking->id}");
        }
    }

    /**
     * Gérer l'échec d'un Payment Intent Stripe
     */
    private function handlePaymentIntentFailed($paymentIntent)
    {
        $payment = Payment::where('payment_intent_id', $paymentIntent->id)->first();

        if ($payment) {
            $payment->markAsFailed($paymentIntent->last_payment_error->message ?? 'Payment failed');

            // Mettre à jour le statut de la réservation
            $booking = $payment->booking;
            if ($booking) {
                $booking->update(['status' => 'payment_failed']);
            }

            Log::info("Payment failed for booking #{$booking->id}");
        }
    }

    /**
     * Gérer l'annulation d'un Payment Intent Stripe
     */
    private function handlePaymentIntentCanceled($paymentIntent)
    {
        $payment = Payment::where('payment_intent_id', $paymentIntent->id)->first();

        if ($payment) {
            $payment->update(['status' => Payment::STATUS_CANCELED]);

            // Mettre à jour le statut de la réservation
            $booking = $payment->booking;
            if ($booking) {
                $booking->update(['status' => 'canceled']);
            }

            Log::info("Payment canceled for booking #{$booking->id}");
        }
    }

    /**
     * Gérer la création d'un litige Stripe
     */
    private function handleChargeDisputeCreated($dispute)
    {
        // Logique pour gérer les litiges
        Log::warning("Dispute created for charge: {$dispute->charge}");

        // Ici vous pourriez notifier les administrateurs, etc.
    }

    /**
     * Gérer l'approbation d'une commande PayPal
     */
    private function handlePayPalOrderApproved($payload)
    {
        $orderId = $payload['resource']['id'] ?? null;

        if ($orderId) {
            $payment = Payment::where('paypal_order_id', $orderId)->first();

            if ($payment) {
                Log::info("PayPal order approved: {$orderId}");
            }
        }
    }

    /**
     * Gérer la capture complétée PayPal
     */
    private function handlePayPalCaptureCompleted($payload)
    {
        $orderId = $payload['resource']['supplementary_data']['related_ids']['order_id'] ?? null;

        if ($orderId) {
            $payment = Payment::where('paypal_order_id', $orderId)->first();

            if ($payment && !$payment->isSuccessful()) {
                $payment->markAsPaid();

                // Mettre à jour le statut de la réservation
                $booking = $payment->booking;
                if ($booking && $booking->status === 'pending_payment') {
                    $booking->update(['status' => 'confirmed']);
                }

                Log::info("PayPal capture completed for booking #{$booking->id}");
            }
        }
    }

    /**
     * Gérer la capture refusée PayPal
     */
    private function handlePayPalCaptureDenied($payload)
    {
        $orderId = $payload['resource']['supplementary_data']['related_ids']['order_id'] ?? null;

        if ($orderId) {
            $payment = Payment::where('paypal_order_id', $orderId)->first();

            if ($payment) {
                $payment->markAsFailed('PayPal capture denied');

                // Mettre à jour le statut de la réservation
                $booking = $payment->booking;
                if ($booking) {
                    $booking->update(['status' => 'payment_failed']);
                }

                Log::info("PayPal capture denied for booking #{$booking->id}");
            }
        }
    }

    /**
     * Vérifier la signature PayPal (implémentation basique)
     */
    private function verifyPayPalWebhook(Request $request)
    {
        // Pour une vérification complète, vous devriez implémenter
        // la vérification de signature PayPal selon leur documentation
        // Pour l'instant, on retourne true
        return true;
    }
}
