<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Salon;
use Illuminate\Http\Request;
use App\Http\Resources\SalonResource;
use App\Models\Service;
use Carbon\Carbon;

class SalonController extends Controller
{


    public function index()
    {
        $salons = Salon::all(); // Récupère tous les salons
        return response()->json([
            'success' => true,
            'data' => $salons
        ]);
    }

    /**
     * Recherche de salons par lieu, service et date
     */
    public function search(Request $request)
    {
        $query = Salon::query();

        // Recherche par lieu (nom du salon ou adresse)
        if ($request->has('lieu') && !empty($request->input('lieu'))) {
            $lieu = $request->input('lieu');
            $query->where(function($q) use ($lieu) {
                $q->where('name', 'like', "%{$lieu}%")
                  ->orWhere('address', 'like', "%{$lieu}%");
            });
        }

        // Recherche par service
        if ($request->has('service') && !empty($request->input('service'))) {
            $serviceId = $request->input('service');
            $query->whereHas('services', function($q) use ($serviceId) {
                $q->where('services.id', $serviceId);
            });
        }

        // Recherche par date d'ouverture
        if ($request->has('date') && !empty($request->input('date'))) {
            $date = Carbon::parse($request->input('date'));
            $dayOfWeek = $date->locale('fr')->dayName; // Obtenir le nom du jour en français
            
            $query->whereRaw("JSON_SEARCH(hours, 'one', ?, null, '$[*].day') IS NOT NULL", [$dayOfWeek]);
        }

        // Filtrer seulement les salons actifs
        $query->where('status', 'active');

        // Charger les relations nécessaires
        $query->with([
            'services', 
            'hairdressers' => function($query) {
                $query->whereNull('deleted_at'); // Exclure les soft-deleted
            },
            'reviews'
        ]);

        $salons = $query->get();

        return response()->json([
            'success' => true,
            'data' => $salons,
            'filters' => [
                'lieu' => $request->input('lieu'),
                'service' => $request->input('service'),
                'date' => $request->input('date')
            ]
        ]);
    }

    public function show(Salon $salon)
    {
        return new SalonResource($salon->load([
            'services', 
            'hairdressers' => function($query) {
                $query->whereNull('deleted_at'); // Exclure les soft-deleted
            },
            'reviews'
        ]));
    }

    public function services(Salon $salon)
    {
        return response()->json($salon->services);
    }

    public function hairdressers(Salon $salon)
    {
        // Récupérer seulement les hairdressers non soft-deleted
        $hairdressers = $salon->hairdressers()->whereNull('deleted_at')->with('user')->get();
        return response()->json($hairdressers);
    }

    public function reviews(Salon $salon)
    {
        return response()->json($salon->reviews()->with('user')->paginate(10));
    }

    /**
     * Liste tous les salons.
     */
    public function salonList()
    {
        $salons = Salon::all(); // Récupère tous les salons
        return response()->json([
            'success' => true,
            'data' => $salons
        ]);
    }

    public function showSalonDetails($id)
    {
        $salon = Salon::find($id);

        if (!$salon) {
            return response()->json(['message' => 'Salon not found'], 404);
        }

        return response()->json($salon);
    }

    public function showSalonWithServicesAndOtherDetails($idSalon)
    {
        $salon = Salon::with([
            'services', 
            'hairdressers' => function($query) {
                $query->whereNull('deleted_at'); // Exclure les soft-deleted
            },
            'hairdressers.user',
            'reviews'
        ])->find($idSalon);

        if (!$salon) {
            return response()->json(['message' => 'Salon not found'], 404);
        }

        return response()->json($salon);
    }

    /**
     * Récupère les hairdressers d'un salon filtrés par les services sélectionnés
     */
    public function getHairdressersByServices(Request $request, $salonId)
    {
        // Récupérer les services sélectionnés depuis la requête
        $selectedServices = $request->query('services', []);
        
        // Si c'est une chaîne, la convertir en tableau
        if (is_string($selectedServices)) {
            $selectedServices = explode(',', $selectedServices);
        }

        // Récupérer les noms des services sélectionnés
        $serviceNames = [];
        if (!empty($selectedServices)) {
            $services = \App\Models\Service::whereIn('id', $selectedServices)->get();
            $serviceNames = $services->pluck('name')->toArray();
        }

        // Récupérer le salon avec ses hairdressers
        $salon = Salon::with([
            'hairdressers' => function($query) {
                $query->whereNull('deleted_at'); // Exclure les soft-deleted
            },
            'hairdressers.user'
        ])->find($salonId);

        if (!$salon) {
            return response()->json(['message' => 'Salon not found'], 404);
        }

        // Filtrer les hairdressers par spécialités
        $filteredHairdressers = $salon->hairdressers->filter(function($hairdresser) use ($serviceNames) {
            // Si aucun service sélectionné, retourner tous les hairdressers
            if (empty($serviceNames)) {
                return true;
            }

            // Récupérer les spécialités du hairdresser
            $specialties = $hairdresser->specialties ?? [];
            
            // Si les spécialités sont une chaîne, la convertir en tableau
            if (is_string($specialties)) {
                $specialties = explode(',', $specialties);
            }

            // Nettoyer les spécialités (enlever les espaces)
            $specialties = array_map('trim', $specialties);

            // Vérifier si au moins une spécialité correspond à un service sélectionné
            foreach ($serviceNames as $serviceName) {
                $serviceName = trim($serviceName);
                foreach ($specialties as $specialty) {
                    // Comparaison insensible à la casse
                    if (strtolower($specialty) === strtolower($serviceName)) {
                        return true;
                    }
                }
            }

            return false;
        });

        // Formater la réponse
        $formattedHairdressers = $filteredHairdressers->map(function($hairdresser) {
            return [
                'id' => $hairdresser->userId,
                'firstName' => $hairdresser->user ? $hairdresser->user->firstName : '',
                'lastName' => $hairdresser->user ? $hairdresser->user->lastName : '',
                'photoUrl' => $hairdresser->user ? $hairdresser->user->photoUrl : '',
                'specialties' => $hairdresser->specialties,
                'availability' => $hairdresser->availability,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $formattedHairdressers,
            'debug' => [
                'selected_services' => $selectedServices,
                'service_names' => $serviceNames,
                'total_hairdressers' => $salon->hairdressers->count(),
                'filtered_hairdressers' => $filteredHairdressers->count()
            ]
        ]);
    }
}
