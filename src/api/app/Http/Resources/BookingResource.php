<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BookingResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'clientId' => $this->clientId,
            'salonId' => $this->salonId,
            'hairdresserId' => $this->hairdresserId,
            'dateTime' => $this->dateTime,
            'status' => $this->status,
            'total_amount' => $this->total_amount,
            'loyalty_points_earned' => $this->loyalty_points_earned,
            'rating' => $this->rating,
            'cancellation_reason' => $this->cancellation_reason,
            'notes' => $this->notes,
            'client' => new UserResource($this->whenLoaded('client')),
            'salon' => new SalonResource($this->whenLoaded('salon')),
            'hairdresser' => new HairdresserResource($this->whenLoaded('hairdresser')),
            'services' => ServiceResource::collection($this->whenLoaded('services')),
            'review' => new ReviewResource($this->whenLoaded('review')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            // Informations formatées pour l'affichage
            'formatted_date' => $this->dateTime ? $this->dateTime->format('d/m/Y') : null,
            'formatted_time' => $this->dateTime ? $this->dateTime->format('H:i:s') : null,
            'status_label' => $this->getStatusLabel(),
            'can_be_cancelled' => $this->canBeCancelled(),
        ];
    }

    private function getStatusLabel(): string
    {
        $labels = [
            'pending' => 'En attente',
            'confirmed' => 'Confirmé',
            'completed' => 'Terminé',
            'cancelled' => 'Annulé'
        ];

        return $labels[$this->status] ?? $this->status;
    }
}