<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ServiceResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'duration' => $this->duration,
            'price' => $this->price,
            'description' => $this->description,
            'category' => $this->category,
            'salonId' => $this->salonId,
            'salon' => new SalonResource($this->whenLoaded('salon')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}