<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class HairdresserResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'userId' => $this->userId,
            'firstName' => $this->user ? $this->user->firstName : '',
            'lastName' => $this->user ? $this->user->lastName : '',
            'photoUrl' => $this->user ? $this->user->photoUrl : '',
            'specialties' => $this->specialties,
            'availability' => $this->availability,
            'salonId' => $this->salonId,
            'user' => $this->whenLoaded('user', function () {
                return [
                    'id' => $this->user->id,
                    'firstName' => $this->user->firstName,
                    'lastName' => $this->user->lastName,
                    'email' => $this->user->email,
                    'photoUrl' => $this->user->photoUrl,
                ];
            }),
            'salon' => new SalonResource($this->whenLoaded('salon')),
            'bookings' => BookingResource::collection($this->whenLoaded('bookings')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}