<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Hairdresser extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'userId',
        'salonId',
        'specialties',
        'availability',
        'rating',
        'active',
    ];

    protected $casts = [
        'specialties' => 'array',
        'availability' => 'array',
        'rating' => 'decimal:2',
        'active' => 'boolean',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'userId');
    }

    public function salon()
    {
        return $this->belongsTo(Salon::class, 'salonId');
    }

    public function bookings()
    {
        return $this->hasMany(Booking::class, 'hairdresserId');
    }

    public function timeOffRequests()
    {
        return $this->hasMany(TimeOffRequest::class, 'hairdresserId');
    }

    public function clientNotes()
    {
        return $this->hasMany(ClientNote::class, 'hairdresserId');
    }

    public function availabilityTemplates()
    {
        return $this->hasMany(AvailabilityTemplate::class, 'hairdresser_id');
    }

    public function timeSlots()
    {
        return $this->hasMany(TimeSlot::class, 'hairdresser_id');
    }

    public function slotExceptions()
    {
        return $this->hasMany(SlotException::class, 'hairdresser_id');
    }

    public function calculateRating()
    {
        $this->rating = $this->bookings()->avg('rating') ?? 0;
        $this->save();
    }

    public function isAvailable($dateTime)
    {
        // Check if the datetime falls within availability
        // and there are no existing bookings or time off requests
        return true; // Implementation needed
    }

    /**
     * Méthodes pour le système de créneaux
     */
    public function getAvailableTimeSlots($date, $duration = null)
    {
        $query = $this->timeSlots()
            ->available()
            ->forDate($date)
            ->upcoming()
            ->orderBy('start_time');

        if ($duration) {
            $query->where('duration', '>=', $duration);
        }

        return $query->get();
    }

    public function hasAvailabilityTemplateForDay($dayOfWeek)
    {
        return $this->availabilityTemplates()
            ->active()
            ->forDay($dayOfWeek)
            ->exists();
    }

    public function getAvailabilityTemplateForDay($dayOfWeek)
    {
        return $this->availabilityTemplates()
            ->active()
            ->forDay($dayOfWeek)
            ->first();
    }

    public function hasExceptionForDate($date, $startTime = null, $endTime = null)
    {
        $query = $this->slotExceptions()
            ->active()
            ->forDate($date);

        if ($startTime && $endTime) {
            $query->inTimeRange($startTime, $endTime);
        }

        return $query->exists();
    }

    public function getExceptionsForDate($date)
    {
        return $this->slotExceptions()
            ->active()
            ->forDate($date)
            ->get();
    }

    public function isAvailableForTimeSlot($date, $startTime, $endTime)
    {
        // Vérifier s'il y a un template de disponibilité pour ce jour
        $dayOfWeek = \Carbon\Carbon::parse($date)->dayOfWeek;
        $template = $this->getAvailabilityTemplateForDay($dayOfWeek);

        if (!$template) {
            return false;
        }

        // Vérifier si l'heure est dans les heures de travail
        $workStart = \Carbon\Carbon::parse($template->start_time);
        $workEnd = \Carbon\Carbon::parse($template->end_time);
        $slotStart = \Carbon\Carbon::parse($startTime);
        $slotEnd = \Carbon\Carbon::parse($endTime);

        if ($slotStart->lt($workStart) || $slotEnd->gt($workEnd)) {
            return false;
        }

        // Vérifier s'il y a des exceptions
        if ($this->hasExceptionForDate($date, $startTime, $endTime)) {
            return false;
        }

        // Vérifier s'il y a déjà une réservation
        $existingBooking = $this->timeSlots()
            ->booked()
            ->forDate($date)
            ->where(function ($query) use ($startTime, $endTime) {
                $query->where('start_time', '<', $endTime)
                      ->where('end_time', '>', $startTime);
            })
            ->exists();

        return !$existingBooking;
    }
}
