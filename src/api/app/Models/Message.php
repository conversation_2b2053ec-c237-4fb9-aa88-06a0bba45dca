<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Message extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'conversation_id',
        'sender_id',
        'content',
        'type',
        'file_path',
        'file_name',
        'file_type',
        'file_size',
        'read_at',
        'read_by',
        'is_edited',
        'edited_at',
    ];

    protected $casts = [
        'read_at' => 'datetime',
        'read_by' => 'array',
        'is_edited' => 'boolean',
        'edited_at' => 'datetime',
        'file_size' => 'integer',
    ];

    const TYPE_TEXT = 'text';
    const TYPE_IMAGE = 'image';
    const TYPE_FILE = 'file';
    const TYPE_SYSTEM = 'system';

    /**
     * Relations
     */
    public function conversation()
    {
        return $this->belongsTo(Conversation::class);
    }

    public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    /**
     * Scopes
     */
    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Méthodes utilitaires
     */
    public function markAsRead($userId = null)
    {
        if ($this->type === self::TYPE_SYSTEM) {
            return;
        }

        if ($this->conversation->type === Conversation::TYPE_PRIVATE) {
            $this->update(['read_at' => now()]);
        } else {
            // Pour les conversations de groupe
            $readBy = $this->read_by ?? [];
            if ($userId && !in_array($userId, $readBy)) {
                $readBy[] = $userId;
                $this->update(['read_by' => $readBy]);
            }
        }
    }

    public function isReadBy($userId)
    {
        if ($this->conversation->type === Conversation::TYPE_PRIVATE) {
            return $this->read_at !== null;
        }

        return in_array($userId, $this->read_by ?? []);
    }

    public function edit($newContent)
    {
        $this->update([
            'content' => $newContent,
            'is_edited' => true,
            'edited_at' => now(),
        ]);
    }

    public function getFileUrl()
    {
        if ($this->file_path) {
            return asset('storage/' . $this->file_path);
        }
        return null;
    }

    public function isImage()
    {
        return $this->type === self::TYPE_IMAGE;
    }

    public function isFile()
    {
        return $this->type === self::TYPE_FILE;
    }

    public function getFormattedFileSize()
    {
        if (!$this->file_size) return null;

        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }
}
