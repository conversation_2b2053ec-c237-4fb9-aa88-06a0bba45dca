<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Review extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'userId',
        'salonId',
        'bookingId',
        'rating',
        'comment',
        'source',
        'details',
        'verified',
    ];

    protected $casts = [
        'rating' => 'decimal:2',
        'details' => 'array',
        'verified' => 'boolean',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'userId');
    }

    public function salon()
    {
        return $this->belongsTo(Salon::class, 'salonId');
    }

    public function booking()
    {
        return $this->belongsTo(Booking::class, 'bookingId');
    }

    protected static function booted()
    {
        static::created(function ($review) {
            $review->salon->calculateRating();
            $review->booking->hairdresser->calculateRating();
        });

        static::updated(function ($review) {
            $review->salon->calculateRating();
            $review->booking->hairdresser->calculateRating();
        });
    }
}