<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class AvailabilityTemplate extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'salon_id',
        'hairdresser_id',
        'name',
        'day_of_week',
        'start_time',
        'end_time',
        'slot_duration',
        'buffer_time',
        'max_advance_booking',
        'is_active',
    ];

    protected $casts = [
        'day_of_week' => 'integer',
        'slot_duration' => 'integer',
        'buffer_time' => 'integer',
        'max_advance_booking' => 'integer',
        'is_active' => 'boolean',
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
    ];

    /**
     * Relations
     */
    public function salon()
    {
        return $this->belongsTo(Salon::class);
    }

    public function hairdresser()
    {
        return $this->belongsTo(Hairdresser::class);
    }

    public function timeSlots()
    {
        return $this->hasMany(TimeSlot::class);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForDay($query, $dayOfWeek)
    {
        return $query->where('day_of_week', $dayOfWeek);
    }

    public function scopeForHairdresser($query, $hairdresserId)
    {
        return $query->where('hairdresser_id', $hairdresserId);
    }

    public function scopeForSalon($query, $salonId)
    {
        return $query->where('salon_id', $salonId);
    }

    /**
     * Méthodes utilitaires
     */
    public function getDayName()
    {
        $days = [
            0 => 'Dimanche',
            1 => 'Lundi',
            2 => 'Mardi',
            3 => 'Mercredi',
            4 => 'Jeudi',
            5 => 'Vendredi',
            6 => 'Samedi',
        ];

        return $days[$this->day_of_week] ?? 'Inconnu';
    }

    public function getTotalWorkingMinutes()
    {
        $start = Carbon::parse($this->start_time);
        $end = Carbon::parse($this->end_time);
        
        return $end->diffInMinutes($start);
    }

    public function getMaxSlotsCount()
    {
        $totalMinutes = $this->getTotalWorkingMinutes();
        $slotWithBuffer = $this->slot_duration + $this->buffer_time;
        
        return floor($totalMinutes / $slotWithBuffer);
    }

    public function generateTimeSlots($date)
    {
        $slots = [];
        $current = Carbon::parse($date . ' ' . $this->start_time);
        $end = Carbon::parse($date . ' ' . $this->end_time);
        
        while ($current->copy()->addMinutes($this->slot_duration)->lte($end)) {
            $slotEnd = $current->copy()->addMinutes($this->slot_duration);
            
            $slots[] = [
                'availability_template_id' => $this->id,
                'hairdresser_id' => $this->hairdresser_id,
                'salon_id' => $this->salon_id,
                'date' => $date,
                'start_time' => $current->format('H:i:s'),
                'end_time' => $slotEnd->format('H:i:s'),
                'duration' => $this->slot_duration,
                'status' => 'available',
            ];
            
            // Ajouter le buffer time pour le prochain créneau
            $current->addMinutes($this->slot_duration + $this->buffer_time);
        }
        
        return $slots;
    }

    public function isValidForDate($date)
    {
        $dayOfWeek = Carbon::parse($date)->dayOfWeek;
        return $this->day_of_week === $dayOfWeek && $this->is_active;
    }
}
