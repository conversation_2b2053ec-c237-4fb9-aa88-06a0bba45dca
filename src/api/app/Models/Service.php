<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Service extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'salonId',
        'name',
        'description',
        'duration',
        'price',
        'category',
        'active',
    ];

    protected $casts = [
        'duration' => 'integer',
        'price' => 'decimal:2',
        'active' => 'boolean',
    ];

    public function salon()
    {
        return $this->belongsTo(Salon::class, 'salonId');
    }

    // public function bookings()
    // {
    //     return $this->belongsToMany(Booking::class, 'booking_services')
    //         ->withPivot('price_at_time')
    //         ->withTimestamps();
    // }

    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    public function services()
    {
        return $this->belongsToMany(Service::class, 'booking_services', 'booking_id', 'serviceId')
            ->withPivot('price_at_time')
            ->withTimestamps();
    }

    public function bookings()
    {
        return $this->belongsToMany(Booking::class, 'booking_services', 'serviceId', 'bookingId')
            ->withPivot('price_at_time')
            ->withTimestamps();
    }
}
