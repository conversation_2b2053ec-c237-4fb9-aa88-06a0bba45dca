<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class LoyaltyReward extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'points_required',
        'value',
        'active',
        'valid_from',
        'valid_until',
        'quantity_available',
        'quantity_redeemed',
    ];

    protected $casts = [
        'points_required' => 'integer',
        'value' => 'decimal:2',
        'active' => 'boolean',
        'valid_from' => 'datetime',
        'valid_until' => 'datetime',
        'quantity_available' => 'integer',
        'quantity_redeemed' => 'integer',
    ];

    public function transactions()
    {
        return $this->hasMany(LoyaltyTransaction::class, 'rewardId');
    }

    public function isAvailable()
    {
        if (!$this->active) return false;
        if ($this->valid_from && $this->valid_from->isFuture()) return false;
        if ($this->valid_until && $this->valid_until->isPast()) return false;
        if ($this->quantity_available && $this->quantity_redeemed >= $this->quantity_available) return false;
        
        return true;
    }
}