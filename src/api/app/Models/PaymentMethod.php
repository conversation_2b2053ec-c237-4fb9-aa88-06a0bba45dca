<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PaymentMethod extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'provider',
        'provider_id',
        'type',
        'last_four',
        'brand',
        'exp_month',
        'exp_year',
        'country',
        'billing_details',
        'is_default',
        'is_active',
    ];

    protected $casts = [
        'billing_details' => 'array',
        'is_default' => 'boolean',
        'is_active' => 'boolean',
    ];

    const PROVIDER_STRIPE = 'stripe';
    const PROVIDER_PAYPAL = 'paypal';

    const TYPE_CARD = 'card';
    const TYPE_PAYPAL_ACCOUNT = 'paypal_account';
    const TYPE_BANK_ACCOUNT = 'bank_account';

    /**
     * Relations
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    public function scopeByProvider($query, $provider)
    {
        return $query->where('provider', $provider);
    }

    public function scopeCards($query)
    {
        return $query->where('type', self::TYPE_CARD);
    }

    /**
     * Méthodes utilitaires
     */
    public function isCard()
    {
        return $this->type === self::TYPE_CARD;
    }

    public function isPayPalAccount()
    {
        return $this->type === self::TYPE_PAYPAL_ACCOUNT;
    }

    public function isBankAccount()
    {
        return $this->type === self::TYPE_BANK_ACCOUNT;
    }

    public function isStripe()
    {
        return $this->provider === self::PROVIDER_STRIPE;
    }

    public function isPayPal()
    {
        return $this->provider === self::PROVIDER_PAYPAL;
    }

    public function makeDefault()
    {
        // Retirer le statut par défaut des autres méthodes de paiement
        $this->user->paymentMethods()
            ->where('id', '!=', $this->id)
            ->update(['is_default' => false]);

        $this->update(['is_default' => true]);
    }

    public function getDisplayName()
    {
        if ($this->isCard()) {
            return ucfirst($this->brand) . ' •••• ' . $this->last_four;
        }

        if ($this->isPayPalAccount()) {
            return 'PayPal';
        }

        return 'Méthode de paiement';
    }

    public function isExpired()
    {
        if (!$this->isCard()) {
            return false;
        }

        $currentYear = (int) date('Y');
        $currentMonth = (int) date('m');

        $expYear = (int) $this->exp_year;
        $expMonth = (int) $this->exp_month;

        return ($expYear < $currentYear) || ($expYear === $currentYear && $expMonth < $currentMonth);
    }

    public function getExpirationDate()
    {
        if (!$this->isCard()) {
            return null;
        }

        return $this->exp_month . '/' . substr($this->exp_year, -2);
    }

    public function deactivate()
    {
        $this->update(['is_active' => false]);
    }

    public function activate()
    {
        $this->update(['is_active' => true]);
    }
}
