<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class TimeSlot extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'availability_template_id',
        'hairdresser_id',
        'salon_id',
        'date',
        'start_time',
        'end_time',
        'duration',
        'status',
        'booking_id',
        'notes',
    ];

    protected $casts = [
        'date' => 'date',
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
        'duration' => 'integer',
    ];

    const STATUS_AVAILABLE = 'available';
    const STATUS_BOOKED = 'booked';
    const STATUS_BLOCKED = 'blocked';
    const STATUS_MAINTENANCE = 'maintenance';

    /**
     * Relations
     */
    public function availabilityTemplate()
    {
        return $this->belongsTo(AvailabilityTemplate::class);
    }

    public function hairdresser()
    {
        return $this->belongsTo(Hairdresser::class);
    }

    public function salon()
    {
        return $this->belongsTo(Salon::class);
    }

    public function booking()
    {
        return $this->belongsTo(Booking::class);
    }

    /**
     * Scopes
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', self::STATUS_AVAILABLE);
    }

    public function scopeBooked($query)
    {
        return $query->where('status', self::STATUS_BOOKED);
    }

    public function scopeForDate($query, $date)
    {
        return $query->where('date', $date);
    }

    public function scopeForDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    public function scopeForHairdresser($query, $hairdresserId)
    {
        return $query->where('hairdresser_id', $hairdresserId);
    }

    public function scopeForSalon($query, $salonId)
    {
        return $query->where('salon_id', $salonId);
    }

    public function scopeInTimeRange($query, $startTime, $endTime)
    {
        return $query->where('start_time', '>=', $startTime)
                    ->where('end_time', '<=', $endTime);
    }

    public function scopeUpcoming($query)
    {
        $now = now();
        return $query->where('date', '>', $now->toDateString())
                    ->orWhere(function ($q) use ($now) {
                        $q->where('date', $now->toDateString())
                          ->where('start_time', '>', $now->toTimeString());
                    });
    }

    /**
     * Méthodes utilitaires
     */
    public function getFullDateTime()
    {
        return Carbon::parse($this->date . ' ' . $this->start_time);
    }

    public function getFullEndDateTime()
    {
        return Carbon::parse($this->date . ' ' . $this->end_time);
    }

    public function isAvailable()
    {
        return $this->status === self::STATUS_AVAILABLE;
    }

    public function isBooked()
    {
        return $this->status === self::STATUS_BOOKED;
    }

    public function isBlocked()
    {
        return $this->status === self::STATUS_BLOCKED;
    }

    public function isPast()
    {
        return $this->getFullDateTime()->isPast();
    }

    public function isFuture()
    {
        return $this->getFullDateTime()->isFuture();
    }

    public function canBeBooked()
    {
        return $this->isAvailable() && $this->isFuture();
    }

    public function markAsBooked(Booking $booking)
    {
        $this->update([
            'status' => self::STATUS_BOOKED,
            'booking_id' => $booking->id,
        ]);
    }

    public function markAsAvailable()
    {
        $this->update([
            'status' => self::STATUS_AVAILABLE,
            'booking_id' => null,
        ]);
    }

    public function block($reason = null)
    {
        $this->update([
            'status' => self::STATUS_BLOCKED,
            'notes' => $reason,
            'booking_id' => null,
        ]);
    }

    public function getDurationInHours()
    {
        return round($this->duration / 60, 2);
    }

    public function getFormattedTimeRange()
    {
        return Carbon::parse($this->start_time)->format('H:i') . ' - ' . 
               Carbon::parse($this->end_time)->format('H:i');
    }

    /**
     * Vérifier si ce créneau peut accueillir une durée donnée
     */
    public function canAccommodateDuration($requiredDuration)
    {
        return $this->duration >= $requiredDuration;
    }

    /**
     * Vérifier les conflits avec d'autres créneaux
     */
    public function hasConflictWith($startTime, $endTime, $date = null)
    {
        $checkDate = $date ?? $this->date;
        
        if ($checkDate !== $this->date) {
            return false;
        }

        $thisStart = Carbon::parse($this->start_time);
        $thisEnd = Carbon::parse($this->end_time);
        $checkStart = Carbon::parse($startTime);
        $checkEnd = Carbon::parse($endTime);

        return $checkStart->lt($thisEnd) && $checkEnd->gt($thisStart);
    }
}
