<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'salonId',
        'name',
        'category',
        'stock',
        'min_stock',
        'price',
        'supplier',
        'description',
        'sku',
        'active',
    ];

    protected $casts = [
        'stock' => 'integer',
        'min_stock' => 'integer',
        'price' => 'decimal:2',
        'active' => 'boolean',
    ];

    public function salon()
    {
        return $this->belongsTo(Salon::class, 'salonId');
    }

    public function stockAdjustments()
    {
        return $this->hasMany(StockAdjustment::class, 'productId');
    }

    public function needsRestock()
    {
        return $this->stock <= $this->min_stock;
    }
}