<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ClientNote extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'clientId',
        'hairdresserId',
        'content',
        'type',
    ];

    public function client()
    {
        return $this->belongsTo(User::class, 'clientId');
    }

    public function hairdresser()
    {
        return $this->belongsTo(Hairdresser::class, 'hairdresserId');
    }
}