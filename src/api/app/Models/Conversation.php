<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Conversation extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title',
        'type',
        'booking_id',
        'salon_id',
        'participants',
        'created_by',
        'last_message_at',
        'is_active',
    ];

    protected $casts = [
        'participants' => 'array',
        'last_message_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    const TYPE_PRIVATE = 'private';
    const TYPE_GROUP = 'group';
    const TYPE_BOOKING_RELATED = 'booking_related';

    /**
     * Relations
     */
    public function messages()
    {
        return $this->hasMany(Message::class)->orderBy('created_at', 'asc');
    }

    public function latestMessage()
    {
        return $this->hasOne(Message::class)->latestOfMany();
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function booking()
    {
        return $this->belongsTo(Booking::class);
    }

    public function salon()
    {
        return $this->belongsTo(Salon::class);
    }

    /**
     * Scopes
     */
    public function scopeForUser($query, $userId)
    {
        return $query->whereJsonContains('participants', $userId);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Méthodes utilitaires
     */
    public function addParticipant($userId)
    {
        $participants = $this->participants ?? [];
        if (!in_array($userId, $participants)) {
            $participants[] = $userId;
            $this->update(['participants' => $participants]);
        }
    }

    public function removeParticipant($userId)
    {
        $participants = $this->participants ?? [];
        $participants = array_filter($participants, fn($id) => $id != $userId);
        $this->update(['participants' => array_values($participants)]);
    }

    public function isParticipant($userId)
    {
        return in_array($userId, $this->participants ?? []);
    }

    public function getOtherParticipants($currentUserId)
    {
        return User::whereIn('id', array_filter($this->participants ?? [], fn($id) => $id != $currentUserId))->get();
    }

    public function updateLastMessageTime()
    {
        $this->update(['last_message_at' => now()]);
    }

    public function getUnreadCount($userId)
    {
        return $this->messages()
            ->where('sender_id', '!=', $userId)
            ->whereNull('read_at')
            ->count();
    }
}
