<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Database\Eloquent\SoftDeletes;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles, SoftDeletes;

    protected $fillable = [
        'salonId',
        'firstName',
        'lastName',
        'email',
        'password',
        'phone',
        'gender',
        'photoUrl',
        'status',
        'loyaltyPoints',
        'lifetimeLoyaltyPoints',
        'role', // Champ rôle
        'specialties', // Ajout du champ specialties
        'availability', // Ajout du champ availability
        'email_verified_at', // Ajout du champ email_verified_at
    ];
    
    

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'loyaltyPoints' => 'integer',
        'lifetimeLoyaltyPoints' => 'integer',
        'specialties' => 'array',
        'availability' => 'array',
    ];

    public function bookings()
    {
        return $this->hasMany(Booking::class, 'clientId');
    }

    public function hairdresser()
    {
        return $this->hasOne(Hairdresser::class, 'userId');
    }

    public function reviews()
    {
        return $this->hasMany(Review::class, 'userId');
    }

    public function loyaltyTransactions()
    {
        return $this->hasMany(LoyaltyTransaction::class, 'userId');
    }

    public function preferences()
    {
        return $this->hasMany(ClientPreference::class, 'userId');
    }

    public function managedSalon()
    {
        return $this->hasOne(Salon::class, 'managerId');
    }

    public function salon()
    {
        return $this->belongsTo(Salon::class, 'salonId');
    }

    public function ownedSalons()
    {
        return $this->hasMany(Salon::class, 'ownerId');
    }

    public function salons()
    {
        return $this->ownedSalons();
    }

    /**
     * Relations avec la table managers
     */
    public function managedBy()
    {
        return $this->hasMany(Manager::class, 'manager_id');
    }

    public function managers()
    {
        return $this->hasMany(Manager::class, 'owner_id');
    }

    public function addLoyaltyPoints(int $points, string $description)
    {
        $this->increment('loyaltyPoints', $points);
        $this->increment('lifetimeLoyaltyPoints', $points);

        return $this->loyaltyTransactions()->create([
            'type' => 'earned',
            'points' => $points,
            'description' => $description,
            'expires_at' => now()->addMonths(12),
        ]);
    }

    public function deductLoyaltyPoints(int $points)
    {
        if ($this->loyaltyPoints >= $points) {
            $this->decrement('loyaltyPoints', $points);
            return true;
        }
        return false;
    }
}