<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Booking extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'clientId',
        'salonId',
        'hairdresserId',
        'dateTime',
        'end_time',
        'time_slot_id',
        'total_duration',
        'status',
        'total_amount',
        'loyalty_points_earned',
        'rating',
        'cancellation_reason',
        'notes',
    ];

    protected $casts = [
        'id' => 'integer',
        'dateTime' => 'datetime',
        'end_time' => 'datetime',
        'total_duration' => 'integer',
        'total_amount' => 'decimal:2',
        'rating' => 'decimal:2',
        'loyalty_points_earned' => 'integer',
    ];

    public function client()
    {
        return $this->belongsTo(User::class, 'clientId');
    }

    // public function salon()
    // {
    //     return $this->belongsTo(Salon::class, 'salonId');
    // }

    public function salon()
    {
        return $this->belongsTo(Salon::class, 'salonId');
    }

    public function hairdresser()
    {
        return $this->belongsTo(Hairdresser::class, 'hairdresserId');
    }

    // public function services()
    // {
    //     return $this->belongsToMany(Service::class, 'booking_services')
    //         ->withPivot('price_at_time')
    //         ->withTimestamps();
    // }



    public function review()
    {
        return $this->hasOne(Review::class, 'bookingId');
    }

    public function calculateTotal()
    {
        $this->total_amount = $this->services->sum('pivot.price_at_time');
        $this->save();
    }

    public function canBeCancelled()
    {
        // Si la relation salon n'est pas chargée, la charger
        if (!$this->relationLoaded('salon')) {
            $this->load('salon');
        }

        // Si pas de salon, utiliser 24h par défaut
        $cancellationHours = $this->salon->cancellation_hours ?? 24;
        $cancellationDeadline = $this->dateTime->subHours($cancellationHours);

        return now()->lt($cancellationDeadline);
    }

    public function services()
    {
        return $this->belongsToMany(Service::class, 'booking_services', 'bookingId', 'serviceId')
            ->withPivot('price_at_time')
            ->withTimestamps();
    }

    public function timeSlot()
    {
        return $this->belongsTo(TimeSlot::class, 'time_slot_id');
    }

    /**
     * Méthodes pour le nouveau système de créneaux
     */
    public function calculateTotalDuration()
    {
        $totalDuration = $this->services->sum(function ($service) {
            return $service->duration + $service->preparation_time + $service->cleanup_time;
        });

        $this->total_duration = $totalDuration;
        $this->save();

        return $totalDuration;
    }

    public function calculateEndTime()
    {
        if ($this->dateTime && $this->total_duration) {
            $this->end_time = $this->dateTime->copy()->addMinutes($this->total_duration);
            $this->save();
        }

        return $this->end_time;
    }

    public function isUsingTimeSlots()
    {
        return !is_null($this->time_slot_id);
    }

    public function getStartDateTime()
    {
        if ($this->isUsingTimeSlots() && $this->timeSlot) {
            return $this->timeSlot->getFullDateTime();
        }

        return $this->dateTime;
    }

    public function getEndDateTime()
    {
        if ($this->end_time) {
            return $this->end_time;
        }

        if ($this->isUsingTimeSlots() && $this->timeSlot) {
            return $this->timeSlot->getFullEndDateTime();
        }

        return $this->dateTime ? $this->dateTime->copy()->addMinutes($this->total_duration ?? 60) : null;
    }

    public function canBeCancelledWithTimeSlots()
    {
        $startDateTime = $this->getStartDateTime();

        if (!$startDateTime) {
            return false;
        }

        // Si la relation salon n'est pas chargée, la charger
        if (!$this->relationLoaded('salon')) {
            $this->load('salon');
        }

        // Si pas de salon, utiliser 24h par défaut
        $cancellationHours = $this->salon->cancellation_hours ?? 24;
        $cancellationDeadline = $startDateTime->copy()->subHours($cancellationHours);

        return now()->lt($cancellationDeadline);
    }

    public function releaseTimeSlot()
    {
        if ($this->timeSlot) {
            $this->timeSlot->markAsAvailable();
            $this->time_slot_id = null;
            $this->save();
        }
    }

    public function assignTimeSlot(TimeSlot $timeSlot)
    {
        if ($timeSlot->canBeBooked()) {
            $timeSlot->markAsBooked($this);
            $this->time_slot_id = $timeSlot->id;
            $this->dateTime = $timeSlot->getFullDateTime();
            $this->end_time = $timeSlot->getFullEndDateTime();
            $this->save();

            return true;
        }

        return false;
    }

    // Relations pour les paiements et la messagerie
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    public function successfulPayment()
    {
        return $this->hasOne(Payment::class)->where('status', 'succeeded');
    }

    public function conversations()
    {
        return $this->hasMany(Conversation::class);
    }
}
