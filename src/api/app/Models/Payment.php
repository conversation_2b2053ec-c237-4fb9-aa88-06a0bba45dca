<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Payment extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'booking_id',
        'user_id',
        'payment_intent_id',
        'paypal_order_id',
        'provider',
        'status',
        'amount',
        'currency',
        'fee_amount',
        'net_amount',
        'metadata',
        'failure_reason',
        'paid_at',
        'refunded_at',
        'refunded_amount',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'fee_amount' => 'decimal:2',
        'net_amount' => 'decimal:2',
        'refunded_amount' => 'decimal:2',
        'metadata' => 'array',
        'paid_at' => 'datetime',
        'refunded_at' => 'datetime',
    ];

    const PROVIDER_STRIPE = 'stripe';
    const PROVIDER_PAYPAL = 'paypal';

    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_SUCCEEDED = 'succeeded';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELED = 'canceled';
    const STATUS_REFUNDED = 'refunded';

    /**
     * Relations
     */
    public function booking()
    {
        return $this->belongsTo(Booking::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scopes
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', self::STATUS_SUCCEEDED);
    }

    public function scopeByProvider($query, $provider)
    {
        return $query->where('provider', $provider);
    }

    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    public function scopeFailed($query)
    {
        return $query->where('status', self::STATUS_FAILED);
    }

    /**
     * Méthodes utilitaires
     */
    public function isSuccessful()
    {
        return $this->status === self::STATUS_SUCCEEDED;
    }

    public function isPending()
    {
        return $this->status === self::STATUS_PENDING;
    }

    public function isFailed()
    {
        return $this->status === self::STATUS_FAILED;
    }

    public function isRefunded()
    {
        return $this->status === self::STATUS_REFUNDED;
    }

    public function canBeRefunded()
    {
        return $this->isSuccessful() && $this->refunded_amount < $this->amount;
    }

    public function getRemainingRefundAmount()
    {
        return $this->amount - $this->refunded_amount;
    }

    public function markAsPaid()
    {
        $this->update([
            'status' => self::STATUS_SUCCEEDED,
            'paid_at' => now(),
        ]);
    }

    public function markAsFailed($reason = null)
    {
        $this->update([
            'status' => self::STATUS_FAILED,
            'failure_reason' => $reason,
        ]);
    }

    public function markAsRefunded($amount = null)
    {
        $refundAmount = $amount ?? $this->getRemainingRefundAmount();

        $this->update([
            'status' => self::STATUS_REFUNDED,
            'refunded_at' => now(),
            'refunded_amount' => $this->refunded_amount + $refundAmount,
        ]);
    }

    public function getFormattedAmount()
    {
        return number_format($this->amount, 2) . ' ' . strtoupper($this->currency);
    }

    public function isStripe()
    {
        return $this->provider === self::PROVIDER_STRIPE;
    }

    public function isPayPal()
    {
        return $this->provider === self::PROVIDER_PAYPAL;
    }
}
