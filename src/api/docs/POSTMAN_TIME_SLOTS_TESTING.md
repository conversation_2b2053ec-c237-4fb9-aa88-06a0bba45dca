# Guide de Test Postman - Système de Créneaux Horaires

## 📋 Table des Matières
1. [Configuration Initiale](#configuration-initiale)
2. [Tests Client](#tests-client)
3. [Tests Coiffeur](#tests-coiffeur)
4. [Tests Manager](#tests-manager)
5. [Tests Propriétaire](#tests-propriétaire)
6. [Scénarios de Test Complets](#scénarios-de-test-complets)

## 🔧 Configuration Initiale

### Variables d'Environnement Postman
Créez un environnement avec ces variables :

```json
{
  "base_url": "http://localhost:8000/api",
  "client_token": "",
  "hairdresser_token": "",
  "manager_token": "",
  "owner_token": "",
  "salon_id": "",
  "hairdresser_id": "",
  "time_slot_id": "",
  "booking_id": ""
}
```

### Headers Globaux
```
Content-Type: application/json
Accept: application/json
Authorization: Bearer {{token}}
```

## 👤 Tests Client

### 1. O<PERSON><PERSON><PERSON> les Créneaux Disponibles

**GET** `{{base_url}}/client/time-slots/available`

**Query Parameters:**
```
salon_id: {{salon_id}}
hairdresser_id: {{hairdresser_id}} (optionnel)
date: 2024-12-25 (optionnel)
duration: 60 (optionnel)
services: [1,2] (optionnel)
```

**Test Script:**
```javascript
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

pm.test("Response has success true", function () {
    const jsonData = pm.response.json();
    pm.expect(jsonData.success).to.eql(true);
});

pm.test("Response contains slots data", function () {
    const jsonData = pm.response.json();
    pm.expect(jsonData.data).to.have.property('slots');
    pm.expect(jsonData.data).to.have.property('total');
});

// Sauvegarder le premier time_slot_id pour les tests suivants
pm.test("Save time_slot_id", function () {
    const jsonData = pm.response.json();
    if (jsonData.data.slots.length > 0) {
        pm.environment.set("time_slot_id", jsonData.data.slots[0].id);
    }
});
```

### 2. Disponibilité Hebdomadaire

**GET** `{{base_url}}/client/time-slots/weekly`

**Query Parameters:**
```
salon_id: {{salon_id}}
hairdresser_id: {{hairdresser_id}} (optionnel)
start_date: 2024-12-23
duration: 60 (optionnel)
```

**Test Script:**
```javascript
pm.test("Weekly data has 7 days", function () {
    const jsonData = pm.response.json();
    pm.expect(Object.keys(jsonData.data)).to.have.lengthOf(7);
});

pm.test("Each day has required structure", function () {
    const jsonData = pm.response.json();
    Object.values(jsonData.data).forEach(day => {
        pm.expect(day).to.have.property('date');
        pm.expect(day).to.have.property('day_name');
        pm.expect(day).to.have.property('slots');
    });
});
```

### 3. Coiffeurs Disponibles

**GET** `{{base_url}}/client/time-slots/hairdressers`

**Query Parameters:**
```
salon_id: {{salon_id}}
date: 2024-12-25
duration: 60 (optionnel)
services: [1,2] (optionnel)
```

### 4. Vérifier Disponibilité d'un Créneau

**POST** `{{base_url}}/client/time-slots/check-availability`

**Body (JSON):**
```json
{
  "time_slot_id": {{time_slot_id}},
  "services": [1, 2]
}
```

**Test Script:**
```javascript
pm.test("Slot is available", function () {
    const jsonData = pm.response.json();
    pm.expect(jsonData.available).to.eql(true);
});
```

### 5. Calculer Durée des Services

**POST** `{{base_url}}/client/time-slots/calculate-duration`

**Body (JSON):**
```json
{
  "services": [1, 2, 3]
}
```

### 6. Créer une Réservation avec Créneau

**POST** `{{base_url}}/client/appointments/add-boking`

**Body (JSON):**
```json
{
  "salonId": {{salon_id}},
  "hairdresserId": {{hairdresser_id}},
  "time_slot_id": {{time_slot_id}},
  "services": [1, 2]
}
```

**Test Script:**
```javascript
pm.test("Booking created successfully", function () {
    const jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property('id');
    pm.environment.set("booking_id", jsonData.id);
});
```

## ✂️ Tests Coiffeur

### 1. Obtenir Templates de Disponibilité

**GET** `{{base_url}}/hairdresser/time-slots/templates`

**Headers:**
```
Authorization: Bearer {{hairdresser_token}}
```

### 2. Créer/Modifier Template

**POST** `{{base_url}}/hairdresser/time-slots/templates`

**Body (JSON):**
```json
{
  "name": "Horaires d'été",
  "day_of_week": 1,
  "start_time": "09:00",
  "end_time": "18:00",
  "slot_duration": 30,
  "buffer_time": 15,
  "max_advance_booking": 30,
  "is_active": true
}
```

### 3. Obtenir Planning

**GET** `{{base_url}}/hairdresser/time-slots/schedule`

**Query Parameters:**
```
start_date: 2024-12-23
end_date: 2024-12-29
```

### 4. Créer Exception

**POST** `{{base_url}}/hairdresser/time-slots/exceptions`

**Body (JSON):**
```json
{
  "date": "2024-12-25",
  "start_time": "12:00",
  "end_time": "13:00",
  "type": "lunch",
  "reason": "Pause déjeuner",
  "recurring": false
}
```

### 5. Bloquer/Débloquer Créneaux

**POST** `{{base_url}}/hairdresser/time-slots/toggle-status`

**Body (JSON):**
```json
{
  "time_slot_ids": [1, 2, 3],
  "action": "block",
  "reason": "Maintenance"
}
```

## 👔 Tests Manager

### 1. Vue d'Ensemble du Salon

**GET** `{{base_url}}/manager/time-slots/overview`

**Headers:**
```
Authorization: Bearer {{manager_token}}
```

**Query Parameters:**
```
start_date: 2024-12-23
end_date: 2024-12-29
```

### 2. Générer Créneaux

**POST** `{{base_url}}/manager/time-slots/generate`

**Body (JSON):**
```json
{
  "start_date": "2024-12-30",
  "end_date": "2025-01-05",
  "hairdresser_ids": [1, 2]
}
```

### 3. Configurer Templates pour Coiffeur

**POST** `{{base_url}}/manager/time-slots/hairdressers/{{hairdresser_id}}/templates`

**Body (JSON):**
```json
{
  "templates": [
    {
      "day_of_week": 1,
      "start_time": "09:00",
      "end_time": "18:00",
      "slot_duration": 30,
      "buffer_time": 15,
      "max_advance_booking": 30,
      "is_active": true
    },
    {
      "day_of_week": 2,
      "start_time": "09:00",
      "end_time": "18:00",
      "slot_duration": 30,
      "buffer_time": 15,
      "max_advance_booking": 30,
      "is_active": true
    }
  ]
}
```

### 4. Blocage en Masse

**POST** `{{base_url}}/manager/time-slots/bulk-block`

**Body (JSON):**
```json
{
  "hairdresser_id": {{hairdresser_id}},
  "start_date": "2024-12-25",
  "end_date": "2024-12-25",
  "start_time": "12:00",
  "end_time": "13:00",
  "reason": "Pause déjeuner"
}
```

### 5. Copier Templates

**POST** `{{base_url}}/manager/time-slots/copy-templates`

**Body (JSON):**
```json
{
  "source_hairdresser_id": 1,
  "target_hairdresser_ids": [2, 3, 4]
}
```

## 🏢 Tests Propriétaire

### 1. Vue d'Ensemble Multi-Salons

**GET** `{{base_url}}/owner/time-slots/overview`

**Headers:**
```
Authorization: Bearer {{owner_token}}
```

### 2. Configuration Templates par Défaut

**POST** `{{base_url}}/owner/time-slots/salons/{{salon_id}}/setup-defaults`

**Body (JSON):**
```json
{
  "working_days": [1, 2, 3, 4, 5, 6],
  "start_time": "09:00",
  "end_time": "18:00",
  "slot_duration": 30,
  "buffer_time": 15,
  "max_advance_booking": 30
}
```

### 3. Statistiques Comparatives

**GET** `{{base_url}}/owner/time-slots/comparative-stats`

**Query Parameters:**
```
start_date: 2024-12-01
end_date: 2024-12-31
metric: occupancy_rate
```

## 🧪 Scénarios de Test Complets

### Scénario 1: Réservation Complète

1. **Obtenir salons disponibles**
2. **Obtenir coiffeurs du salon**
3. **Obtenir services disponibles**
4. **Calculer durée totale des services**
5. **Obtenir créneaux disponibles**
6. **Vérifier disponibilité du créneau choisi**
7. **Créer la réservation**
8. **Vérifier que le créneau n'est plus disponible**

### Scénario 2: Gestion Coiffeur

1. **Connexion coiffeur**
2. **Obtenir templates actuels**
3. **Modifier horaires de travail**
4. **Créer exception pour congé**
5. **Vérifier planning mis à jour**
6. **Bloquer créneaux manuellement**

### Scénario 3: Administration Manager

1. **Connexion manager**
2. **Vue d'ensemble salon**
3. **Générer créneaux pour la semaine**
4. **Configurer templates pour nouveau coiffeur**
5. **Copier templates vers autres coiffeurs**
6. **Obtenir statistiques détaillées**

### Variables de Test Recommandées

```json
{
  "test_salon_id": "1",
  "test_hairdresser_id": "1",
  "test_service_ids": [1, 2],
  "test_date": "2024-12-25",
  "test_start_time": "14:00",
  "test_duration": "60"
}
```

### Scripts de Validation Globaux

```javascript
// À ajouter dans les Tests de Collection
pm.test("Response time is less than 2000ms", function () {
    pm.expect(pm.response.responseTime).to.be.below(2000);
});

pm.test("Response has correct content type", function () {
    pm.expect(pm.response.headers.get("Content-Type")).to.include("application/json");
});

pm.test("No server errors", function () {
    pm.response.to.not.have.status(500);
});
```

Ce guide vous permettra de tester complètement le système de créneaux horaires avec Postman !
