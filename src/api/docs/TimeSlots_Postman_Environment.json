{"id": "time-slots-environment", "name": "Time Slots - Local Development", "values": [{"key": "base_url", "value": "http://localhost:8000/api", "description": "URL de base de l'API", "enabled": true}, {"key": "client_token", "value": "", "description": "Token d'authentification client", "enabled": true}, {"key": "hairdresser_token", "value": "", "description": "Token d'authentification coiffeur", "enabled": true}, {"key": "manager_token", "value": "", "description": "Token d'authentification manager", "enabled": true}, {"key": "owner_token", "value": "", "description": "Token d'authentification propriétaire", "enabled": true}, {"key": "salon_id", "value": "1", "description": "ID du salon de test", "enabled": true}, {"key": "hairdresser_id", "value": "1", "description": "ID du coiffeur de test", "enabled": true}, {"key": "time_slot_id", "value": "", "description": "ID du créneau sélectionné (auto-rempli)", "enabled": true}, {"key": "booking_id", "value": "", "description": "ID de la réservation créée (auto-rempli)", "enabled": true}, {"key": "service_id_1", "value": "1", "description": "ID du premier service de test", "enabled": true}, {"key": "service_id_2", "value": "2", "description": "ID du deuxième service de test", "enabled": true}, {"key": "test_date", "value": "2024-12-25", "description": "Date de test pour les créneaux", "enabled": true}, {"key": "test_start_date", "value": "2024-12-23", "description": "Date de début pour les tests de période", "enabled": true}, {"key": "test_end_date", "value": "2024-12-29", "description": "Date de fin pour les tests de période", "enabled": true}, {"key": "test_duration", "value": "60", "description": "Durée de test en minutes", "enabled": true}, {"key": "client_email", "value": "<EMAIL>", "description": "Email du client de test", "enabled": true}, {"key": "client_password", "value": "password", "description": "Mot de passe du client de test", "enabled": true}, {"key": "hairdresser_email", "value": "<EMAIL>", "description": "<PERSON><PERSON> du coiffeur de test", "enabled": true}, {"key": "hairdresser_password", "value": "password", "description": "Mot de passe du coiffeur de test", "enabled": true}, {"key": "manager_email", "value": "<EMAIL>", "description": "<PERSON>ail du manager de test", "enabled": true}, {"key": "manager_password", "value": "password", "description": "Mot de passe du manager de test", "enabled": true}], "_postman_variable_scope": "environment"}