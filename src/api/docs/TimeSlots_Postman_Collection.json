{"info": {"name": "Système de Créneaux Horaires - Tests", "description": "Collection complète pour tester le système de créneaux horaires", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api"}, {"key": "salon_id", "value": "1"}, {"key": "hairdresser_id", "value": "1"}, {"key": "time_slot_id", "value": ""}, {"key": "booking_id", "value": ""}], "item": [{"name": "🔐 Authentication", "item": [{"name": "Login Client", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Login successful', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('token');", "    pm.environment.set('client_token', jsonData.token);", "});"]}}]}]}, {"name": "👤 Client - Time Slots", "item": [{"name": "<PERSON><PERSON><PERSON><PERSON> Disponibles", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{client_token}}"}], "url": {"raw": "{{base_url}}/client/time-slots/available?salon_id={{salon_id}}&hairdresser_id={{hairdresser_id}}", "host": ["{{base_url}}"], "path": ["client", "time-slots", "available"], "query": [{"key": "salon_id", "value": "{{salon_id}}"}, {"key": "hairdresser_id", "value": "{{hairdresser_id}}"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success true', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "});", "", "pm.test('Save first time_slot_id', function () {", "    const jsonData = pm.response.json();", "    if (jsonData.data.slots.length > 0) {", "        pm.environment.set('time_slot_id', jsonData.data.slots[0].id);", "    }", "});"]}}]}, {"name": "Disponibilité Hebdomadaire", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{client_token}}"}], "url": {"raw": "{{base_url}}/client/time-slots/weekly?salon_id={{salon_id}}&start_date=2024-12-23", "host": ["{{base_url}}"], "path": ["client", "time-slots", "weekly"], "query": [{"key": "salon_id", "value": "{{salon_id}}"}, {"key": "start_date", "value": "2024-12-23"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Weekly data has 7 days', function () {", "    const jsonData = pm.response.json();", "    pm.expect(Object.keys(jsonData.data)).to.have.lengthOf(7);", "});"]}}]}, {"name": "Vérifier Disponibilité Créneau", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{client_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"time_slot_id\": {{time_slot_id}},\n  \"services\": [1, 2]\n}"}, "url": {"raw": "{{base_url}}/client/time-slots/check-availability", "host": ["{{base_url}}"], "path": ["client", "time-slots", "check-availability"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Availability check successful', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('available');", "});"]}}]}, {"name": "Calculer Durée Services", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{client_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"services\": [1, 2, 3]\n}"}, "url": {"raw": "{{base_url}}/client/time-slots/calculate-duration", "host": ["{{base_url}}"], "path": ["client", "time-slots", "calculate-duration"]}}}, {"name": "Créer Réservation avec Créneau", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{client_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"salonId\": {{salon_id}},\n  \"hairdresserId\": {{hairdresser_id}},\n  \"time_slot_id\": {{time_slot_id}},\n  \"services\": [1, 2]\n}"}, "url": {"raw": "{{base_url}}/client/appointments/add-boking", "host": ["{{base_url}}"], "path": ["client", "appointments", "add-boking"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Booking created successfully', function () {", "    pm.response.to.have.status(201);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.environment.set('booking_id', jsonData.id);", "});"]}}]}]}, {"name": "✂️ Co<PERSON>eur - Time Slots", "item": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{hairdresser_token}}"}], "url": {"raw": "{{base_url}}/hairdresser/time-slots/templates", "host": ["{{base_url}}"], "path": ["hairdresser", "time-slots", "templates"]}}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{hairdresser_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Horaires standard\",\n  \"day_of_week\": 1,\n  \"start_time\": \"09:00\",\n  \"end_time\": \"18:00\",\n  \"slot_duration\": 30,\n  \"buffer_time\": 15,\n  \"max_advance_booking\": 30,\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/hairdresser/time-slots/templates", "host": ["{{base_url}}"], "path": ["hairdresser", "time-slots", "templates"]}}}, {"name": "Obtenir Planning", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{hairdresser_token}}"}], "url": {"raw": "{{base_url}}/hairdresser/time-slots/schedule?start_date=2024-12-23&end_date=2024-12-29", "host": ["{{base_url}}"], "path": ["hairdresser", "time-slots", "schedule"], "query": [{"key": "start_date", "value": "2024-12-23"}, {"key": "end_date", "value": "2024-12-29"}]}}}, {"name": "Créer Exception", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{hairdresser_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"date\": \"2024-12-25\",\n  \"start_time\": \"12:00\",\n  \"end_time\": \"13:00\",\n  \"type\": \"lunch\",\n  \"reason\": \"Pause déjeuner\",\n  \"recurring\": false\n}"}, "url": {"raw": "{{base_url}}/hairdresser/time-slots/exceptions", "host": ["{{base_url}}"], "path": ["hairdresser", "time-slots", "exceptions"]}}}]}, {"name": "👔 Manager - Time Slots", "item": [{"name": "Vue d'Ensemble Salon", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{manager_token}}"}], "url": {"raw": "{{base_url}}/manager/time-slots/overview?start_date=2024-12-23&end_date=2024-12-29", "host": ["{{base_url}}"], "path": ["manager", "time-slots", "overview"], "query": [{"key": "start_date", "value": "2024-12-23"}, {"key": "end_date", "value": "2024-12-29"}]}}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{manager_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"start_date\": \"2024-12-30\",\n  \"end_date\": \"2025-01-05\",\n  \"hairdresser_ids\": [{{hairdresser_id}}]\n}"}, "url": {"raw": "{{base_url}}/manager/time-slots/generate", "host": ["{{base_url}}"], "path": ["manager", "time-slots", "generate"]}}}, {"name": "Statistiques Détaillées", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{manager_token}}"}], "url": {"raw": "{{base_url}}/manager/time-slots/stats?start_date=2024-12-01&end_date=2024-12-31", "host": ["{{base_url}}"], "path": ["manager", "time-slots", "stats"], "query": [{"key": "start_date", "value": "2024-12-01"}, {"key": "end_date", "value": "2024-12-31"}]}}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Script global pré-requête", "console.log('Executing request to: ' + pm.request.url);"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Tests globaux", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "pm.test('Response has correct content type', function () {", "    if (pm.response.headers.get('Content-Type')) {", "        pm.expect(pm.response.headers.get('Content-Type')).to.include('application/json');", "    }", "});", "", "pm.test('No server errors', function () {", "    pm.response.to.not.have.status(500);", "});"]}}]}