#!/bin/bash

# Script de test automatisé pour le système de créneaux horaires
# Util<PERSON> (CLI de Postman) pour exécuter les tests

set -e  # Arrêter en cas d'erreur

echo "🚀 Démarrage des tests automatisés du système de créneaux horaires"
echo "=================================================================="

# Configuration
COLLECTION_FILE="TimeSlots_Postman_Collection.json"
ENVIRONMENT_FILE="TimeSlots_Postman_Environment.json"
REPORT_DIR="test_reports"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Vérifier que Newman est installé
if ! command -v newman &> /dev/null; then
    echo "❌ Newman n'est pas installé. Installation..."
    npm install -g newman
    npm install -g newman-reporter-html
fi

# Créer le dossier de rapports
mkdir -p $REPORT_DIR

echo "📋 Vérification des prérequis..."

# Vérifier que les fichiers existent
if [ ! -f "$COLLECTION_FILE" ]; then
    echo "❌ Collection Postman non trouvée: $COLLECTION_FILE"
    exit 1
fi

if [ ! -f "$ENVIRONMENT_FILE" ]; then
    echo "❌ Environnement Postman non trouvé: $ENVIRONMENT_FILE"
    exit 1
fi

# Vérifier que le serveur Laravel est démarré
echo "🔍 Vérification du serveur Laravel..."
if ! curl -s http://localhost:8000/api/health > /dev/null 2>&1; then
    echo "⚠️  Le serveur Laravel ne semble pas démarré sur http://localhost:8000"
    echo "   Démarrez-le avec: php artisan serve"
    
    # Optionnel: démarrer automatiquement le serveur
    read -p "Voulez-vous que je démarre le serveur automatiquement? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🚀 Démarrage du serveur Laravel..."
        cd .. && php artisan serve &
        SERVER_PID=$!
        sleep 5  # Attendre que le serveur démarre
        echo "✅ Serveur démarré (PID: $SERVER_PID)"
    else
        exit 1
    fi
fi

echo "✅ Serveur Laravel accessible"

# Préparer la base de données
echo "🗄️  Préparation de la base de données..."
cd .. && php artisan migrate:fresh --seed > /dev/null 2>&1
echo "✅ Base de données préparée"

# Générer des créneaux de test
echo "🕐 Génération des créneaux de test..."
cd .. && php artisan timeslots:generate --days=30 --force > /dev/null 2>&1
echo "✅ Créneaux générés"

# Retourner au dossier docs
cd docs

echo "🧪 Exécution des tests..."
echo "========================"

# Exécuter les tests avec Newman
newman run "$COLLECTION_FILE" \
    --environment "$ENVIRONMENT_FILE" \
    --reporters cli,html \
    --reporter-html-export "$REPORT_DIR/report_$TIMESTAMP.html" \
    --delay-request 500 \
    --timeout-request 10000 \
    --insecure \
    --color on

TEST_EXIT_CODE=$?

# Générer un rapport de synthèse
echo ""
echo "📊 Génération du rapport de synthèse..."

cat > "$REPORT_DIR/summary_$TIMESTAMP.md" << EOF
# Rapport de Test - Système de Créneaux Horaires

**Date:** $(date)
**Durée:** Voir rapport HTML
**Status:** $([ $TEST_EXIT_CODE -eq 0 ] && echo "✅ SUCCÈS" || echo "❌ ÉCHEC")

## Fichiers Générés

- Rapport HTML: \`report_$TIMESTAMP.html\`
- Rapport de synthèse: \`summary_$TIMESTAMP.md\`

## Tests Exécutés

- 🔐 Tests d'authentification
- 👤 Tests client (créneaux disponibles, réservations)
- ✂️ Tests coiffeur (templates, planning)
- 👔 Tests manager (administration)

## Commandes de Vérification

\`\`\`bash
# Vérifier l'état du système
php artisan timeslots:validate

# Voir les statistiques
php artisan tinker
>>> \App\Models\TimeSlot::count()
>>> \App\Models\Booking::count()
\`\`\`

## Prochaines Étapes

$([ $TEST_EXIT_CODE -eq 0 ] && echo "✅ Tous les tests sont passés. Le système est prêt pour la production." || echo "❌ Certains tests ont échoué. Vérifiez le rapport HTML pour plus de détails.")
EOF

echo "✅ Rapport de synthèse généré: $REPORT_DIR/summary_$TIMESTAMP.md"

# Nettoyer si le serveur a été démarré automatiquement
if [ ! -z "$SERVER_PID" ]; then
    echo "🛑 Arrêt du serveur Laravel..."
    kill $SERVER_PID
fi

# Afficher les résultats
echo ""
echo "🎉 Tests terminés!"
echo "=================="
echo "📁 Rapports disponibles dans: $REPORT_DIR/"
echo "📄 Rapport HTML: $REPORT_DIR/report_$TIMESTAMP.html"
echo "📋 Synthèse: $REPORT_DIR/summary_$TIMESTAMP.md"

if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo "✅ Tous les tests sont passés avec succès!"
    echo "🚀 Le système de créneaux horaires est prêt pour la production."
else
    echo "❌ Certains tests ont échoué."
    echo "🔍 Consultez le rapport HTML pour plus de détails."
    exit 1
fi

echo ""
echo "💡 Pour ouvrir le rapport HTML:"
echo "   - Linux/Mac: open $REPORT_DIR/report_$TIMESTAMP.html"
echo "   - Windows: start $REPORT_DIR/report_$TIMESTAMP.html"
