# Script PowerShell pour tester le système de créneaux horaires
# U<PERSON><PERSON> (CLI de Postman) pour exécuter les tests

param(
    [switch]$SkipSetup,
    [switch]$OpenReport
)

Write-Host "🚀 Démarrage des tests automatisés du système de créneaux horaires" -ForegroundColor Green
Write-Host "==================================================================" -ForegroundColor Green

# Configuration
$CollectionFile = "TimeSlots_Postman_Collection.json"
$EnvironmentFile = "TimeSlots_Postman_Environment.json"
$ReportDir = "test_reports"
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"

# Créer le dossier de rapports
if (!(Test-Path $ReportDir)) {
    New-Item -ItemType Directory -Path $ReportDir | Out-Null
}

Write-Host "📋 Vérification des prérequis..." -ForegroundColor Yellow

# Vérifier que Newman est installé
try {
    newman --version | Out-Null
} catch {
    Write-Host "❌ Newman n'est pas installé. Installation..." -ForegroundColor Red
    npm install -g newman
    npm install -g newman-reporter-html
}

# Vérifier que les fichiers existent
if (!(Test-Path $CollectionFile)) {
    Write-Host "❌ Collection Postman non trouvée: $CollectionFile" -ForegroundColor Red
    exit 1
}

if (!(Test-Path $EnvironmentFile)) {
    Write-Host "❌ Environnement Postman non trouvé: $EnvironmentFile" -ForegroundColor Red
    exit 1
}

# Vérifier que le serveur Laravel est démarré
Write-Host "🔍 Vérification du serveur Laravel..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/api/health" -TimeoutSec 5 -ErrorAction Stop
    Write-Host "✅ Serveur Laravel accessible" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Le serveur Laravel ne semble pas démarré sur http://localhost:8000" -ForegroundColor Yellow
    Write-Host "   Démarrez-le avec: php artisan serve" -ForegroundColor Yellow
    
    $response = Read-Host "Voulez-vous que je démarre le serveur automatiquement? (y/n)"
    if ($response -eq "y" -or $response -eq "Y") {
        Write-Host "🚀 Démarrage du serveur Laravel..." -ForegroundColor Yellow
        Set-Location ".."
        $serverProcess = Start-Process -FilePath "php" -ArgumentList "artisan", "serve" -PassThru -WindowStyle Hidden
        Start-Sleep -Seconds 5
        Set-Location "docs"
        Write-Host "✅ Serveur démarré (PID: $($serverProcess.Id))" -ForegroundColor Green
    } else {
        exit 1
    }
}

if (!$SkipSetup) {
    # Préparer la base de données
    Write-Host "🗄️  Préparation de la base de données..." -ForegroundColor Yellow
    Set-Location ".."
    php artisan migrate:fresh --seed | Out-Null
    Write-Host "✅ Base de données préparée" -ForegroundColor Green

    # Générer des créneaux de test
    Write-Host "🕐 Génération des créneaux de test..." -ForegroundColor Yellow
    php artisan timeslots:generate --days=30 --force | Out-Null
    Write-Host "✅ Créneaux générés" -ForegroundColor Green

    # Retourner au dossier docs
    Set-Location "docs"
}

Write-Host "🧪 Exécution des tests..." -ForegroundColor Yellow
Write-Host "========================" -ForegroundColor Yellow

# Exécuter les tests avec Newman
$newmanArgs = @(
    "run", $CollectionFile,
    "--environment", $EnvironmentFile,
    "--reporters", "cli,html",
    "--reporter-html-export", "$ReportDir/report_$Timestamp.html",
    "--delay-request", "500",
    "--timeout-request", "10000",
    "--insecure",
    "--color", "on"
)

$testResult = & newman @newmanArgs
$testExitCode = $LASTEXITCODE

# Générer un rapport de synthèse
Write-Host ""
Write-Host "📊 Génération du rapport de synthèse..." -ForegroundColor Yellow

$summaryContent = @"
# Rapport de Test - Système de Créneaux Horaires

**Date:** $(Get-Date)
**Status:** $(if ($testExitCode -eq 0) { "✅ SUCCÈS" } else { "❌ ÉCHEC" })

## Fichiers Générés

- Rapport HTML: ``report_$Timestamp.html``
- Rapport de synthèse: ``summary_$Timestamp.md``

## Tests Exécutés

- 🔐 Tests d'authentification
- 👤 Tests client (créneaux disponibles, réservations)
- ✂️ Tests coiffeur (templates, planning)
- 👔 Tests manager (administration)

## Commandes de Vérification

``````powershell
# Vérifier l'état du système
php artisan timeslots:validate

# Voir les statistiques
php artisan tinker
>>> \App\Models\TimeSlot::count()
>>> \App\Models\Booking::count()
``````

## Prochaines Étapes

$(if ($testExitCode -eq 0) { "✅ Tous les tests sont passés. Le système est prêt pour la production." } else { "❌ Certains tests ont échoué. Vérifiez le rapport HTML pour plus de détails." })
"@

$summaryContent | Out-File -FilePath "$ReportDir/summary_$Timestamp.md" -Encoding UTF8

Write-Host "✅ Rapport de synthèse généré: $ReportDir/summary_$Timestamp.md" -ForegroundColor Green

# Nettoyer si le serveur a été démarré automatiquement
if ($serverProcess) {
    Write-Host "🛑 Arrêt du serveur Laravel..." -ForegroundColor Yellow
    Stop-Process -Id $serverProcess.Id -Force
}

# Afficher les résultats
Write-Host ""
Write-Host "🎉 Tests terminés!" -ForegroundColor Green
Write-Host "=================="
Write-Host "📁 Rapports disponibles dans: $ReportDir/"
Write-Host "📄 Rapport HTML: $ReportDir/report_$Timestamp.html"
Write-Host "📋 Synthèse: $ReportDir/summary_$Timestamp.md"

if ($testExitCode -eq 0) {
    Write-Host "✅ Tous les tests sont passés avec succès!" -ForegroundColor Green
    Write-Host "🚀 Le système de créneaux horaires est prêt pour la production." -ForegroundColor Green
} else {
    Write-Host "❌ Certains tests ont échoué." -ForegroundColor Red
    Write-Host "🔍 Consultez le rapport HTML pour plus de détails." -ForegroundColor Yellow
}

# Ouvrir le rapport automatiquement si demandé
if ($OpenReport) {
    Write-Host "📖 Ouverture du rapport HTML..." -ForegroundColor Yellow
    Start-Process "$ReportDir/report_$Timestamp.html"
}

Write-Host ""
Write-Host "💡 Pour ouvrir le rapport HTML manuellement:" -ForegroundColor Cyan
Write-Host "   start $ReportDir/report_$Timestamp.html" -ForegroundColor Cyan

if ($testExitCode -ne 0) {
    exit 1
}
