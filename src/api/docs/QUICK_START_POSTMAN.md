# 🚀 Guide de Démarrage Rapide - Tests Postman

## 📥 Installation et Configuration

### 1. Importer la Collection et l'Environnement

1. **Ouvrir <PERSON>**
2. **Importer la collection** :
   - Cliquer sur "Import"
   - Sélectionner le fichier `TimeSlots_Postman_Collection.json`
3. **Importer l'environnement** :
   - Cliquer sur "Import"
   - Sélectionner le fichier `TimeSlots_Postman_Environment.json`
4. **Sélectionner l'environnement** :
   - Dans le coin supérieur droit, sélectionner "Time Slots - Local Development"

### 2. Préparer les Données de Test

Avant de lancer les tests, assurez-vous d'avoir :

```bash
# 1. Exécuter les migrations
php artisan migrate

# 2. Initialiser le système avec des données de test
php artisan db:seed --class=TimeSlotSeeder

# 3. Créer des utilisateurs de test (si pas déjà fait)
php artisan tinker
```

<PERSON><PERSON>, créer les utilisateurs :
```php
// Créer un client
$client = \App\Models\User::factory()->create([
    'email' => '<EMAIL>',
    'password' => bcrypt('password'),
    'role' => 'client'
]);

// Créer un coiffeur
$hairdresserUser = \App\Models\User::factory()->create([
    'email' => '<EMAIL>',
    'password' => bcrypt('password'),
    'role' => 'hairdresser'
]);

$hairdresser = \App\Models\Hairdresser::factory()->create([
    'userId' => $hairdresserUser->id,
    'salonId' => 1
]);

// Créer un manager
$manager = \App\Models\User::factory()->create([
    'email' => '<EMAIL>',
    'password' => bcrypt('password'),
    'role' => 'manager'
]);
```

### 3. Générer des Créneaux de Test

```bash
# Générer des créneaux pour les 30 prochains jours
php artisan timeslots:generate --days=30

# Vérifier que tout est correct
php artisan timeslots:validate
```

## 🧪 Ordre de Test Recommandé

### Phase 1: Tests de Base

1. **🔐 Authentication**
   - Login Client
   - Login Hairdresser (si configuré)
   - Login Manager (si configuré)

2. **👤 Client - Consultation**
   - Obtenir Créneaux Disponibles
   - Disponibilité Hebdomadaire
   - Calculer Durée Services

3. **👤 Client - Réservation**
   - Vérifier Disponibilité Créneau
   - Créer Réservation avec Créneau

### Phase 2: Tests Avancés

4. **✂️ Coiffeur - Gestion**
   - Obtenir Templates
   - Créer Template
   - Obtenir Planning
   - Créer Exception

5. **👔 Manager - Administration**
   - Vue d'Ensemble Salon
   - Générer Créneaux
   - Statistiques Détaillées

## 🔧 Variables à Personnaliser

Avant de lancer les tests, vérifiez ces variables dans l'environnement :

| Variable | Valeur par Défaut | Description |
|----------|-------------------|-------------|
| `base_url` | `http://localhost:8000/api` | URL de votre API |
| `salon_id` | `1` | ID d'un salon existant |
| `hairdresser_id` | `1` | ID d'un coiffeur existant |
| `test_date` | `2024-12-25` | Date future pour les tests |
| `client_email` | `<EMAIL>` | Email du client de test |

## 📋 Checklist Pré-Tests

- [ ] Serveur Laravel démarré (`php artisan serve`)
- [ ] Base de données migrée
- [ ] Données de test créées (seeders)
- [ ] Créneaux générés
- [ ] Utilisateurs de test créés
- [ ] Collection et environnement importés
- [ ] Variables d'environnement configurées

## 🎯 Tests Critiques à Valider

### ✅ Tests de Fonctionnement

1. **Disponibilité des créneaux** : Vérifier que l'API retourne des créneaux
2. **Réservation** : Créer une réservation et vérifier qu'elle fonctionne
3. **Validation** : Tenter de réserver un créneau déjà pris
4. **Calcul de durée** : Vérifier que les durées sont correctement calculées

### ⚠️ Tests d'Erreur

1. **Salon inexistant** : Tester avec `salon_id=99999`
2. **Créneau inexistant** : Tester avec `time_slot_id=99999`
3. **Services inexistants** : Tester avec des IDs de services invalides
4. **Dates passées** : Tenter de réserver dans le passé

## 🐛 Dépannage

### Erreur 500 - Erreur Serveur
```bash
# Vérifier les logs Laravel
tail -f storage/logs/laravel.log

# Vérifier la configuration
php artisan config:cache
php artisan route:cache
```

### Erreur 404 - Route Non Trouvée
```bash
# Vérifier les routes
php artisan route:list | grep time-slots
```

### Erreur 422 - Validation
- Vérifier que les IDs existent dans la base de données
- Vérifier le format des dates (YYYY-MM-DD)
- Vérifier que les services appartiennent au salon

### Pas de Créneaux Disponibles
```bash
# Générer des créneaux
php artisan timeslots:generate --force

# Vérifier les templates
php artisan tinker
>>> \App\Models\AvailabilityTemplate::count()
```

## 📊 Interprétation des Résultats

### Réponse Typique - Créneaux Disponibles
```json
{
  "success": true,
  "data": {
    "slots": [
      {
        "id": 1,
        "date": "2024-12-25",
        "start_time": "09:00:00",
        "end_time": "09:30:00",
        "duration": 30,
        "status": "available"
      }
    ],
    "total": 1
  }
}
```

### Réponse Typique - Réservation Créée
```json
{
  "id": 1,
  "clientId": 1,
  "salonId": 1,
  "hairdresserId": 1,
  "time_slot_id": 1,
  "dateTime": "2024-12-25T09:00:00.000000Z",
  "end_time": "2024-12-25T09:30:00.000000Z",
  "total_duration": 30,
  "status": "pending"
}
```

## 🔄 Tests Automatisés

Pour exécuter tous les tests automatiquement :

1. **Sélectionner la collection** "Système de Créneaux Horaires - Tests"
2. **Cliquer sur "Run"**
3. **Configurer l'exécution** :
   - Sélectionner tous les dossiers
   - Délai entre requêtes : 500ms
   - Nombre d'itérations : 1
4. **Lancer** et observer les résultats

## 📈 Métriques de Performance

Surveillez ces métriques pendant les tests :

- **Temps de réponse** : < 2000ms (configuré dans les tests globaux)
- **Taux de succès** : 100% pour les tests valides
- **Erreurs 500** : 0 (aucune erreur serveur)

## 🎉 Validation Finale

Si tous les tests passent, votre système de créneaux horaires est prêt pour la production !

### Tests de Charge (Optionnel)

Pour tester la performance :
1. Augmenter le nombre d'itérations à 10-50
2. Réduire le délai entre requêtes à 100ms
3. Observer les temps de réponse et erreurs
