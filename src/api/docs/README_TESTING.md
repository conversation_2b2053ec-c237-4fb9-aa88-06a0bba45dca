# 📚 Documentation de Test - Système de Créneaux Horaires

## 📁 Fichiers de Test Disponibles

| Fichier | Description | Usage |
|---------|-------------|-------|
| `POSTMAN_TIME_SLOTS_TESTING.md` | Guide complet de test Postman | Documentation détaillée |
| `TimeSlots_Postman_Collection.json` | Collection Postman exportable | Import dans Postman |
| `TimeSlots_Postman_Environment.json` | Environnement Postman | Variables de test |
| `QUICK_START_POSTMAN.md` | Guide de démarrage rapide | Setup initial |
| `run_postman_tests.sh` | Script automatisé Linux/Mac | Tests en ligne de commande |
| `run_postman_tests.ps1` | Script automatisé Windows | Tests PowerShell |

## 🚀 Méthodes de Test

### 1. Test Manuel avec Postman (Recommandé pour le développement)

**Avantages :** Interface graphique, debugging facile, tests interactifs

```bash
# 1. Importer les fichiers dans Postman
# 2. Configurer l'environnement
# 3. Exécuter les tests un par un ou en collection
```

**Étapes :**
1. Ouvrir Postman
2. Importer `TimeSlots_Postman_Collection.json`
3. Importer `TimeSlots_Postman_Environment.json`
4. Sélectionner l'environnement "Time Slots - Local Development"
5. Suivre le guide `QUICK_START_POSTMAN.md`

### 2. Test Automatisé avec Newman (Recommandé pour CI/CD)

**Avantages :** Automatisation, rapports HTML, intégration CI/CD

#### Linux/Mac :
```bash
chmod +x run_postman_tests.sh
./run_postman_tests.sh
```

#### Windows :
```powershell
# Exécuter en tant qu'administrateur
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\run_postman_tests.ps1

# Avec ouverture automatique du rapport
.\run_postman_tests.ps1 -OpenReport

# Sans setup de la base de données (si déjà fait)
.\run_postman_tests.ps1 -SkipSetup
```

### 3. Test Unitaire Laravel (Développement)

```bash
# Tests unitaires des modèles et services
php artisan test --filter TimeSlot

# Tests d'intégration des API
php artisan test --filter TimeSlotApi
```

## 🔧 Configuration Initiale

### Prérequis

1. **Laravel configuré et fonctionnel**
2. **Base de données configurée**
3. **Node.js installé** (pour Newman)
4. **Postman installé** (pour tests manuels)

### Installation Newman (CLI Postman)

```bash
# Installation globale
npm install -g newman
npm install -g newman-reporter-html

# Vérification
newman --version
```

### Préparation des Données

```bash
# 1. Migrations
php artisan migrate

# 2. Données de test
php artisan db:seed --class=TimeSlotSeeder

# 3. Créneaux de test
php artisan timeslots:generate --days=30

# 4. Validation
php artisan timeslots:validate
```

## 📊 Types de Tests Inclus

### 🔐 Tests d'Authentification
- Login client, coiffeur, manager, propriétaire
- Gestion des tokens
- Validation des permissions

### 👤 Tests Client
- **Consultation :**
  - Créneaux disponibles
  - Disponibilité hebdomadaire
  - Coiffeurs disponibles
  - Calcul de durée des services

- **Réservation :**
  - Vérification de disponibilité
  - Création de réservation
  - Validation des conflits

### ✂️ Tests Coiffeur
- **Gestion des horaires :**
  - Templates de disponibilité
  - Planning personnel
  - Exceptions (congés, pauses)

- **Administration :**
  - Blocage/déblocage de créneaux
  - Statistiques personnelles

### 👔 Tests Manager
- **Vue d'ensemble salon :**
  - Disponibilité globale
  - Statistiques détaillées

- **Administration :**
  - Génération de créneaux
  - Configuration des templates
  - Gestion en masse

### 🏢 Tests Propriétaire
- **Multi-salons :**
  - Vue comparative
  - Configuration globale
  - Statistiques avancées

## 🎯 Scénarios de Test Critiques

### Scénario 1 : Réservation Normale
1. Client consulte les créneaux disponibles
2. Sélectionne un créneau
3. Vérifie la disponibilité
4. Crée la réservation
5. Vérifie que le créneau n'est plus disponible

### Scénario 2 : Gestion des Conflits
1. Deux clients tentent de réserver le même créneau
2. Le premier réussit
3. Le second reçoit une erreur
4. Le système propose des alternatives

### Scénario 3 : Administration Coiffeur
1. Coiffeur configure ses horaires
2. Crée une exception pour congé
3. Vérifie que les créneaux sont bloqués
4. Consulte ses statistiques

## 📈 Métriques de Performance

### Temps de Réponse Attendus
- **Consultation créneaux :** < 500ms
- **Création réservation :** < 1000ms
- **Génération créneaux :** < 2000ms
- **Statistiques :** < 1500ms

### Indicateurs de Qualité
- **Taux de succès :** 100% pour les tests valides
- **Erreurs 500 :** 0
- **Cohérence des données :** 100%

## 🐛 Dépannage

### Problèmes Courants

#### Newman non trouvé
```bash
npm install -g newman
```

#### Serveur Laravel non démarré
```bash
php artisan serve
```

#### Base de données vide
```bash
php artisan migrate:fresh --seed
php artisan timeslots:generate --days=30
```

#### Erreurs de permissions (Windows)
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Logs et Debugging

```bash
# Logs Laravel
tail -f storage/logs/laravel.log

# Validation du système
php artisan timeslots:validate

# État de la base de données
php artisan tinker
>>> \App\Models\TimeSlot::count()
>>> \App\Models\Booking::count()
>>> \App\Models\AvailabilityTemplate::count()
```

## 📋 Checklist de Validation

### Avant Production
- [ ] Tous les tests Postman passent
- [ ] Tests unitaires Laravel passent
- [ ] Performance acceptable (< 2s)
- [ ] Pas d'erreurs 500
- [ ] Validation des données OK
- [ ] Tests de charge réussis

### Après Déploiement
- [ ] Tests de fumée sur l'environnement de production
- [ ] Monitoring des performances
- [ ] Vérification des logs
- [ ] Tests utilisateur finaux

## 🔄 Intégration CI/CD

### GitHub Actions (Exemple)
```yaml
name: Time Slots Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.1
      - name: Install dependencies
        run: composer install
      - name: Run Newman tests
        run: |
          npm install -g newman
          cd docs
          ./run_postman_tests.sh
```

## 📞 Support

En cas de problème avec les tests :

1. **Vérifier les prérequis** (serveur, base de données, Newman)
2. **Consulter les logs** Laravel et Newman
3. **Valider les données** avec `php artisan timeslots:validate`
4. **Réinitialiser** si nécessaire avec les seeders

Le système de test est conçu pour être robuste et informatif. Les rapports HTML générés contiennent tous les détails nécessaires pour identifier et résoudre les problèmes.
