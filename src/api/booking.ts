import type { Booking } from '../types';

const API_URL = '/api';

// Fonction pour créer une réservation
export async function createBooking(bookingData: Partial<Booking>): Promise<Booking> {
  try {
    const response = await fetch(`${API_URL}/bookings`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
      },
      body: JSON.stringify(bookingData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Erreur lors de la création de la réservation');
    }

    return await response.json();
  } catch (error: any) {
    throw new Error(error.message || 'Erreur réseau lors de la création de la réservation');
  }
}

// Fonction pour vérifier la disponibilité
export async function checkAvailability(hairdresserId: string, dateTime: string): Promise<boolean> {
  try {
    const response = await fetch(
      `${API_URL}/availability?hairdresserId=${hairdresserId}&dateTime=${dateTime}`,
      {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error('Erreur lors de la vérification de la disponibilité');
    }

    const { available } = await response.json();
    return available;
  } catch (error: any) {
    throw new Error(error.message || 'Erreur réseau lors de la vérification de la disponibilité');
  }
}

// Types spécifiques liés aux formulaires et erreurs
export interface BookingFormData {
  salonId: string;
  hairdresserId: string;
  services: string[];
  dateTime: string;
}

export interface TimeSlot {
  start: string;
  end: string;
  available: boolean;
}

export interface BookingError {
  message: string;
  field?: string;
}

