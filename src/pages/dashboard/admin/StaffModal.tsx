import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import toast from 'react-hot-toast';
import { BASE_URL, IMG_URL } from '../../../context/url';
import { X, Plus, Calendar, Clock } from 'lucide-react';
import ReactCrop, { Crop, PixelCrop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';

type Availability = {
  day: string;
  start: string;
  end: string;
};

type StaffModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onStaffAdded: () => void;
  editingStaff?: StaffMember | null;
};

type StaffMember = {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  gender?: string;
  role: string;
  specialties?: string;
  salonId?: number;
  photoUrl?: string;
  availability?: string;
  status?: string;
};

const daysOfWeek = [
  '<PERSON><PERSON>', '<PERSON><PERSON>', 'Merc<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Dimanche'
];

const StaffModal: React.FC<StaffModalProps> = ({ isOpen, onClose, onStaffAdded, editingStaff }) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    phone: '',
    gender: 'male',
    salonId: '',
    photoUrl: null as File | string | null,
  });

  const [specialties, setSpecialties] = useState<string[]>([]);
  const [newSpecialty, setNewSpecialty] = useState('');
  const [availability, setAvailability] = useState<Availability[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [salons, setSalons] = useState<{id: number, name: string}[]>([]);
  const [isLoadingSalons, setIsLoadingSalons] = useState(false);

  // États pour le cropper
  const [crop, setCrop] = useState<Crop>();
  const [showCropper, setShowCropper] = useState(false);
  const [imageToCrop, setImageToCrop] = useState<string | null>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const [completedCrop, setCompletedCrop] = useState<PixelCrop | null>(null);
  const [zoom, setZoom] = useState(1);

  const hours = Array.from({ length: 24 }, (_, i) => 
    i.toString().padStart(2, '0') + ':00'
  );

  useEffect(() => {
    if (isOpen) {
      fetchSalons();
      
      // Réinitialiser les états du cropper lors de l'ouverture du modal
      setImageToCrop(null);
      setCompletedCrop(null);
      setZoom(1);
      setCrop(undefined);

      if (editingStaff) {
        setFormData({
          firstName: editingStaff.firstName || '',
          lastName: editingStaff.lastName || '',
          email: editingStaff.email || '',
          password: '',
          phone: editingStaff.phone || '',
          gender: editingStaff.gender || 'male',
          salonId: editingStaff.salonId?.toString() || '',
          photoUrl: editingStaff.photoUrl || null,
        });
        setSpecialties(editingStaff.specialties ? editingStaff.specialties.split(',').map(s => s.trim()) : []);
        
        if (editingStaff.availability) {
          try {
            const parsed = JSON.parse(editingStaff.availability);
            const formattedAvailability = Object.entries(parsed).map(([day, timeRange]: [string, any]) => {
              if (typeof timeRange === 'string' && timeRange.includes('-')) {
                const [start, end] = timeRange.split('-');
                return {
                  day: day.charAt(0).toUpperCase() + day.slice(1),
                  start,
                  end,
                };
              }
              return null;
            }).filter((item): item is Availability => item !== null);
            setAvailability(formattedAvailability);
          } catch {
            setAvailability([]);
          }
        } else {
          setAvailability([]);
        }
      } else {
        // Réinitialiser le formData si on n'est pas en mode édition
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          password: '',
          phone: '',
          gender: 'male',
          salonId: '',
          photoUrl: null,
        });
        setSpecialties([]);
        setAvailability([]);
      }
    } else {
      // Réinitialiser les états quand le modal se ferme
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        password: '',
        phone: '',
        gender: 'male',
        salonId: '',
        photoUrl: null,
      });
      setSpecialties([]);
      setAvailability([]);
      setIsSubmitting(false);
    }
  }, [isOpen, editingStaff]);

  // Initialiser le crop lorsque l'image à rogner est définie
  useEffect(() => {
    if (imageToCrop) {
      const initialCrop: Crop = {
        unit: '%',
        width: 90,
        height: 90,
        x: 5,
        y: 5,
      };
      setCrop(initialCrop);
      setShowCropper(true);
    }
  }, [imageToCrop]);

  const fetchSalons = async () => {
    try {
      setIsLoadingSalons(true);
      const token = localStorage.getItem('auth_token');
      const response = await axios.get(`${BASE_URL}/admin/salons`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      setSalons(response.data.data || response.data);
    } catch (error) {
      console.error("Erreur lors de la récupération des salons :", error);
      toast.error("Impossible de charger les salons.");
    } finally {
      setIsLoadingSalons(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleAddSpecialty = () => {
    if (newSpecialty.trim() && !specialties.includes(newSpecialty.trim())) {
      setSpecialties([...specialties, newSpecialty.trim()]);
      setNewSpecialty('');
    }
  };

  const handleRemoveSpecialty = (index: number) => {
    setSpecialties(specialties.filter((_, i) => i !== index));
  };

  const handleSpecialtyKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddSpecialty();
    }
  };

  const handleAvailabilityChange = (index: number, field: keyof Availability, value: string) => {
    const updatedAvailability = [...availability];
    updatedAvailability[index][field] = value;
    setAvailability(updatedAvailability);
  };

  const addAvailabilityField = () => {
    setAvailability([...availability, { day: 'Lundi', start: '', end: '' }]);
  };

  const removeAvailabilityField = (index: number) => {
    setAvailability(availability.filter((_, i) => i !== index));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (!file.type.startsWith('image/')) {
        toast.error('Le fichier doit être une image.');
        return;
      }

      const validTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif'];
      if (!validTypes.includes(file.type)) {
        toast.error('Le fichier doit être de type JPEG, PNG, JPG ou GIF.');
        return;
      }

      // Créer une URL pour l'image
      const imageUrl = URL.createObjectURL(file);
      setImageToCrop(imageUrl);
    }
  };

  const onCropComplete = (crop: PixelCrop) => {
    setCompletedCrop(crop);
  };

  const handleCropSave = () => {
    if (completedCrop && imageToCrop && imageRef.current) {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (ctx) {
        const image = imageRef.current;
        const scaleX = image.naturalWidth / image.width;
        const scaleY = image.naturalHeight / image.height;
        
        canvas.width = completedCrop.width;
        canvas.height = completedCrop.height;
        
        ctx.drawImage(
          image,
          completedCrop.x * scaleX,
          completedCrop.y * scaleY,
          completedCrop.width * scaleX,
          completedCrop.height * scaleY,
          0,
          0,
          completedCrop.width,
          completedCrop.height
        );
        
        canvas.toBlob((blob) => {
          if (blob) {
            const file = new File([blob], 'profile-image.jpg', { type: 'image/jpeg' });
            // Créer une URL pour afficher l'image rognée
            const croppedImageUrl = URL.createObjectURL(blob);
            setFormData({ ...formData, photoUrl: file });
            setShowCropper(false);
            setImageToCrop(null);
            setCompletedCrop(null);
            setZoom(1);
            setCrop(undefined);
          }
        }, 'image/jpeg');
      }
    }
  };

  const handleCropCancel = () => {
    setShowCropper(false);
    setImageToCrop(null);
    setCompletedCrop(null);
    setZoom(1);
    setCrop(undefined);
    
    // Réinitialiser l'input file
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    const formDataObject = new FormData();
    formDataObject.append('firstName', formData.firstName);
    formDataObject.append('lastName', formData.lastName);
    formDataObject.append('email', formData.email);
    formDataObject.append('password', formData.password);
    formDataObject.append('phone', formData.phone);
    formDataObject.append('role', 'hairdresser'); // Ajouter le rôle hairdresser
    formDataObject.append('gender', formData.gender);
    
    // Ajouter salonId si disponible
    if (formData.salonId) {
      formDataObject.append('salonId', formData.salonId);
    }
    
    // Ajouter les spécialités comme un tableau
    if (specialties.length > 0) {
      specialties.forEach((specialty, index) => {
        formDataObject.append(`specialties[${index}]`, specialty);
      });
    }
    
    // Ajouter les disponibilités uniquement si il y en a
    if (availability.length > 0) {
      availability.forEach((slot, index) => {
        if (slot.start && slot.end) {
          // Convertir le jour français en anglais
          const dayInEnglish = {
            'Lundi': 'Monday',
            'Mardi': 'Tuesday',
            'Mercredi': 'Wednesday',
            'Jeudi': 'Thursday',
            'Vendredi': 'Friday',
            'Samedi': 'Saturday',
            'Dimanche': 'Sunday',
          }[slot.day] || slot.day;
          
          formDataObject.append(`availability[${index}][day]`, dayInEnglish);
          formDataObject.append(`availability[${index}][open]`, slot.start);
          formDataObject.append(`availability[${index}][close]`, slot.end);
        }
      });
    }

    // Ajouter l'image de profil si présente
    if (formData.photoUrl) {
      formDataObject.append('profileImage', formData.photoUrl);
    }

    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        toast.error("Aucun jeton d'authentification trouvé.");
        return;
      }

      let response;
      if (editingStaff) {
        response = await axios.post(
          `${BASE_URL}/admin/staff/update-staff/${editingStaff.id}`,
          formDataObject,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'multipart/form-data',
            },
          }
        );
      } else {
        response = await axios.post(`${BASE_URL}/admin/staff/add-staff`, formDataObject, {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'multipart/form-data',
          },
        });
      }

      if (response.status === 200 || response.status === 201) {
        toast.success(
          editingStaff ? 'Staff mis à jour avec succès.' : 'Staff ajouté avec succès.'
        );
        onStaffAdded();
        onClose();
      } else {
        toast.error('Une erreur inattendue s\'est produite.');
      }
    } catch (error: any) {
      console.error("Erreur lors de l'ajout ou de la mise à jour du staff :", error);
      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error(
          editingStaff
            ? "Impossible de mettre à jour le staff."
            : "Impossible d'ajouter le staff."
        );
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-gray-900">
            {editingStaff ? 'Modifier le Staff' : 'Ajouter un Staff'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Informations de base */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
                Prénom
              </label>
              <input
                type="text"
                id="firstName"
                name="firstName"
                value={formData.firstName}
                onChange={handleChange}
                required
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
            </div>

            <div>
              <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
                Nom
              </label>
              <input
                type="text"
                id="lastName"
                name="lastName"
                value={formData.lastName}
                onChange={handleChange}
                required
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Email
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                Mot de passe
              </label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                required
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
            </div>

            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                Téléphone
              </label>
              <input
                type="text"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                required
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
            </div>

            <div>
              <label htmlFor="gender" className="block text-sm font-medium text-gray-700 mb-2">
                Genre
              </label>
              <select
                id="gender"
                name="gender"
                value={formData.gender}
                onChange={handleChange}
                required
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="male">Homme</option>
                <option value="female">Femme</option>
                <option value="other">Autre</option>
              </select>
            </div>

            <div>
              <label htmlFor="salonId" className="block text-sm font-medium text-gray-700 mb-2">
                Salon
              </label>
              <select
                id="salonId"
                name="salonId"
                value={formData.salonId}
                onChange={handleChange}
                required
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="">Sélectionnez un salon</option>
                {isLoadingSalons ? (
                  <option value="" disabled>Chargement...</option>
                ) : (
                  salons.map((salon) => (
                    <option key={salon.id} value={salon.id}>
                      {salon.name}
                    </option>
                  ))
                )}
              </select>
            </div>
          </div>

          {/* Photo de profil */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Photo de profil
            </label>
            <input
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
            />
            {formData.photoUrl && (
              <div className="mt-2">
                <img
                  src={
                    typeof formData.photoUrl === 'string'
                      ? `${IMG_URL}${formData.photoUrl}`
                      : URL.createObjectURL(formData.photoUrl)
                  }
                  alt="Photo de profil"
                  className="h-20 w-20 rounded-full object-cover"
                />
              </div>
            )}
          </div>

          {/* Spécialités */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Spécialités
            </label>
            <div className="flex gap-2 mb-3">
              <input
                type="text"
                value={newSpecialty}
                onChange={(e) => setNewSpecialty(e.target.value)}
                onKeyPress={handleSpecialtyKeyPress}
                placeholder="Ajouter une spécialité..."
                className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
              <button
                type="button"
                onClick={handleAddSpecialty}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
              >
                <Plus className="h-4 w-4" />
              </button>
            </div>
            
            {/* Affichage des spécialités */}
            <div className="flex flex-wrap gap-2">
              {specialties.map((specialty, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800"
                >
                  {specialty}
                  <button
                    type="button"
                    onClick={() => handleRemoveSpecialty(index)}
                    className="ml-2 text-indigo-600 hover:text-indigo-800"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </span>
              ))}
            </div>
          </div>

          {/* Calendrier */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <label className="block text-sm font-medium text-gray-700">
                Calendrier
              </label>
              <button
                type="button"
                onClick={addAvailabilityField}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
              >
                <Plus className="h-4 w-4 mr-1" />
                Ajouter un jour
              </button>
            </div>

            <div className="space-y-3">
              {availability.map((slot, index) => (
                <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                  <select
                    value={slot.day}
                    onChange={(e) => handleAvailabilityChange(index, 'day', e.target.value)}
                    className="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  >
                    {daysOfWeek.map((day) => (
                      <option key={day} value={day}>
                        {day}
                      </option>
                    ))}
                  </select>

                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <select
                      value={slot.start}
                      onChange={(e) => handleAvailabilityChange(index, 'start', e.target.value)}
                      className="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    >
                      <option value="">Début</option>
                      {hours.map((hour) => (
                        <option key={hour} value={hour}>
                          {hour}
                        </option>
                      ))}
                    </select>

                    <span className="text-gray-500">-</span>

                    <select
                      value={slot.end}
                      onChange={(e) => handleAvailabilityChange(index, 'end', e.target.value)}
                      className="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    >
                      <option value="">Fin</option>
                      {hours.map((hour) => (
                        <option key={hour} value={hour}>
                          {hour}
                        </option>
                      ))}
                    </select>
                  </div>

                  <button
                    type="button"
                    onClick={() => removeAvailabilityField(index)}
                    className="text-red-600 hover:text-red-800"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* Boutons d'action */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Enregistrement...
                </>
              ) : (
                editingStaff ? 'Mettre à jour' : 'Ajouter'
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Modal de rognage d'image */}
      {showCropper && imageToCrop && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white p-4 rounded-lg max-w-2xl w-full">
            <h3 className="text-lg font-semibold mb-4">Rogner l'image</h3>
            <ReactCrop
              crop={crop}
              onChange={(c) => setCrop(c)}
              onComplete={onCropComplete}
              aspect={1}
              circularCrop
            >
              <img
                ref={imageRef}
                src={imageToCrop}
                alt="Image à rogner"
                className="max-w-full max-h-[80vh] object-contain"
                style={{ transform: `scale(${zoom})`, transformOrigin: 'center center' }}
              />
            </ReactCrop>
            <div className="mt-4">
              <label htmlFor="zoom-range" className="block text-sm font-medium text-gray-700">
                Zoom: {zoom.toFixed(2)}x
              </label>
              <input
                type="range"
                id="zoom-range"
                min="1"
                max="3"
                step="0.01"
                value={zoom}
                onChange={(e) => setZoom(Number(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
            </div>
            <div className="mt-4 flex justify-end space-x-2">
              <button
                type="button"
                onClick={handleCropCancel}
                className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Annuler
              </button>
              <button
                type="button"
                onClick={handleCropSave}
                className="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
              >
                Enregistrer
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StaffModal; 