import React from 'react';
import { Save } from 'lucide-react';

export default function SettingsManagement() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">System Settings</h2>
      </div>

      <div className="bg-white shadow rounded-lg">
        <div className="p-6">
          <div className="space-y-8">
            {/* General Settings */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">General Settings</h3>
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <label htmlFor="siteName" className="block text-sm font-medium text-gray-700">
                    Site Name
                  </label>
                  <input
                    type="text"
                    id="siteName"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    defaultValue="StyleSync"
                  />
                </div>
                <div>
                  <label htmlFor="supportEmail" className="block text-sm font-medium text-gray-700">
                    Support Email
                  </label>
                  <input
                    type="email"
                    id="supportEmail"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    defaultValue="<EMAIL>"
                  />
                </div>
              </div>
            </div>

            {/* Booking Settings */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Booking Settings</h3>
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <label htmlFor="advanceBooking" className="block text-sm font-medium text-gray-700">
                    Maximum Advance Booking (days)
                  </label>
                  <input
                    type="number"
                    id="advanceBooking"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    defaultValue="30"
                  />
                </div>
                <div>
                  <label htmlFor="cancellationPeriod" className="block text-sm font-medium text-gray-700">
                    Cancellation Period (hours)
                  </label>
                  <input
                    type="number"
                    id="cancellationPeriod"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    defaultValue="24"
                  />
                </div>
              </div>
            </div>

            {/* Notification Settings */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Notification Settings</h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    id="emailNotifications"
                    type="checkbox"
                    className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                    defaultChecked
                  />
                  <label htmlFor="emailNotifications" className="ml-2 block text-sm text-gray-700">
                    Enable Email Notifications
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    id="smsNotifications"
                    type="checkbox"
                    className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                    defaultChecked
                  />
                  <label htmlFor="smsNotifications" className="ml-2 block text-sm text-gray-700">
                    Enable SMS Notifications
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    id="reminderNotifications"
                    type="checkbox"
                    className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                    defaultChecked
                  />
                  <label htmlFor="reminderNotifications" className="ml-2 block text-sm text-gray-700">
                    Send Appointment Reminders
                  </label>
                </div>
              </div>
            </div>

            {/* API Integration Settings */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">API Integration Settings</h3>
              <div className="grid grid-cols-1 gap-6">
                <div>
                  <label htmlFor="googleApiKey" className="block text-sm font-medium text-gray-700">
                    Google Maps API Key
                  </label>
                  <input
                    type="text"
                    id="googleApiKey"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    placeholder="Enter API key"
                  />
                </div>
                <div>
                  <label htmlFor="stripeApiKey" className="block text-sm font-medium text-gray-700">
                    Stripe Secret Key
                  </label>
                  <input
                    type="password"
                    id="stripeApiKey"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    placeholder="Enter API key"
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="mt-8 flex justify-end">
            <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
              <Save className="h-4 w-4 mr-2" />
              Save Settings
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}