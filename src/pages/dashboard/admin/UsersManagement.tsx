import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { User, Edit, Trash2, Plus, Loader2 } from 'lucide-react';
import { useAuth } from '../../../context/AuthContext';
import { BASE_URL, IMG_URL } from '../../../context/url'; // Import IMG_URL
import UserModal from './UserModal';
import toast from 'react-hot-toast';

export type User = {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  gender?: string;
  role: string;
  specialties?: string;
  salonId?: number;
  photoUrl?: string;
  availability?: string;
  status?: string; // Ajoutez cette propriété ici
  ownerId?: number; // Ajout de l'ownerId
};


type LocalUser = {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  gender: 'male' | 'female' | 'other';
  role: 'admin' | 'manager' | 'hairdresser' | 'client';
  specialties?: string;
  salonId?: number;
  photoUrl?: string;
  availability?: string;
  status?: string; // Ajoutez cette propriété si nécessaire
  ownerId?: number; // Ajout de l'ownerId
};




export default function UsersManagement() {
  const { user, isLoading: authLoading } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState('all');
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);
  const [userToDeleteId, setUserToDeleteId] = useState<number | null>(null);
  const [deletingUserId, setDeletingUserId] = useState<number | null>(null);

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Fonction pour ouvrir le modal en mode édition
  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setIsModalOpen(true);
  };

  // Fetch users function
  const fetchUsers = async () => {
    const token = localStorage.getItem('auth_token');
    if (!token) {
      console.error('Aucun jeton trouvé, impossible de récupérer les utilisateurs.');
      setIsLoading(false);
      return;
    }

    try {
      const response = await axios.get<User[]>(`${BASE_URL}/admin/users/users-list`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      
      // Le backend fait déjà le filtrage selon le rôle de l'utilisateur connecté
      setUsers(response.data);
    } catch (error) {
      console.error('Erreur lors de la récupération des utilisateurs:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteUser = async (userId: number) => {
    const token = localStorage.getItem('auth_token');
    if (!token) {
      console.error('Aucun jeton trouvé.');
      toast.error("Aucun jeton d'authentification trouvé.");
      return;
    }

    setDeletingUserId(userId);

    try {
      const response = await axios.delete(`${BASE_URL}/admin/users/delete-users/${userId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      toast.success('Utilisateur supprimé avec succès');
      fetchUsers();
    } catch (error: any) {
      console.error('Erreur lors de la suppression:', error);
       if (error.response?.data?.message) {
         toast.error(error.response.data.message); // Afficher le message d'erreur du backend si disponible
       } else {
         toast.error("Impossible de supprimer l'utilisateur.");
       }
    } finally {
      setShowDeleteConfirmModal(false);
      setUserToDeleteId(null);
      setDeletingUserId(null);
    }
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  const mapUserToLocalUser = (user: User): LocalUser => ({
    id: user.id,
    firstName: user.firstName,
    lastName: user.lastName,
    email: user.email,
    phone: user.phone || '',
    gender: (user.gender as 'male' | 'female' | 'other') || 'male',
    role: user.role as LocalUser['role'], // Mappage correct des rôles
    specialties: user.specialties || '',
    salonId: user.salonId || 0,
    photoUrl: user.photoUrl || '',
    availability: user.availability || '{}',
    status: user.status || 'inactive', // Ajoutez un statut par défaut si nécessaire
    ownerId: user.ownerId || undefined, // Ajout de l'ownerId
  });
  
  
  

  const confirmDelete = (userId: number) => {
    if (deletingUserId === userId) {
        return;
    }
    setUserToDeleteId(userId);
    setShowDeleteConfirmModal(true);
  };

  const handleCancelDelete = () => {
    setShowDeleteConfirmModal(false);
    setUserToDeleteId(null);
  };

  const handleConfirmDelete = () => {
    if (userToDeleteId !== null) {
      handleDeleteUser(userToDeleteId);
      }
  };

  const handleUserAdded = () => {
    fetchUsers();
  };

  useEffect(() => {
    fetchUsers();
  }, [user]);

  // Filtered and paginated users
  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = selectedRole === 'all' || user.role === selectedRole;
    return matchesSearch && matchesRole;
  });

  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedUsers = filteredUsers.slice(startIndex, startIndex + itemsPerPage);
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);

  const getRoleName = (role: string): string => {
    const roles: Record<string, string> = {
      admin: 'Administrateur',
      manager: 'Gérant',
      hairdresser: 'Coiffeur',
      client: 'Client',
    };
    return roles[role] || role;
  };

  if (isLoading || authLoading) {
    return <div>Chargement des utilisateurs...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header and Add User Button */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">
          Gestion des Utilisateurs
        </h2>
        <button
          onClick={() => setIsModalOpen(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
        >
          <Plus className="h-4 w-4 mr-2" />
          Ajouter un Utilisateur
        </button>

        <UserModal
          isOpen={isModalOpen}
          onClose={handleModalClose}
          onUserAdded={handleUserAdded}
          editingUser={editingUser ? mapUserToLocalUser(editingUser) : null}
        />

        {/* Custom Delete Confirmation Modal */}
        {showDeleteConfirmModal && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-sm">
              <h3 className="text-lg font-semibold mb-4">Confirmer la suppression</h3>
              <p className="mb-4">Êtes-vous sûr de vouloir supprimer cet utilisateur ? Cette action est irréversible.</p>
              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={handleCancelDelete}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                >
                  Annuler
                </button>
                <button
                  type="button"
                  onClick={handleConfirmDelete}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                >
                  Supprimer
                </button>
              </div>
            </div>
          </div>
        )}

      </div>

      {/* Users Table */}
      <div className="bg-white shadow rounded-lg">
        <div className="p-6">
          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <input
                type="search"
                placeholder="Rechercher des utilisateurs..."
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="sm:w-48">
              <select
                value={selectedRole}
                onChange={(e) => setSelectedRole(e.target.value)}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="all">
                  Tous les rôles
                </option>
                {user?.role === 'admin' && (
                  <>
                    <option value="admin">Administrateur</option>
                  </>
                )}
                <option value="manager">Gérant</option>
                <option value="hairdresser">Coiffeur</option>
                {user?.role === 'admin' && <option value="client">Client</option>}
              </select>
            </div>
          </div>

          {/* Users Table */}
          <div className="mt-8 flow-root">
            <table className="min-w-full divide-y divide-gray-300">
              <thead>
                <tr>
                  <th className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">
                    Utilisateur
                  </th>
                  <th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                    Contact
                  </th>
                  <th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                    Rôle
                  </th>
                  <th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                    Statut
                  </th>
                  <th className="relative py-3.5 pl-3 pr-4 sm:pr-0">
                    <span className="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {paginatedUsers.map((user) => (
                  <tr key={user.id}>
                    <td className="whitespace-nowrap py-4 pl-4 pr-3">
                      <div className="flex items-center">
                        <div className="h-10 w-10 flex-shrink-0">
                          <img
                            className="h-10 w-10 rounded-full object-cover"
                            src={user.photoUrl ? `${IMG_URL}${user.photoUrl}` : '/default_profile.jpeg'}
                            alt="Photo utilisateur"
                          />
                        </div>
                        <div className="ml-4">
                          <div className="font-medium text-gray-900">
                            {user.firstName} {user.lastName}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4">{user.email}</td>
                    <td className="whitespace-nowrap px-3 py-4">{getRoleName(user.role)}</td>
                    <td className="whitespace-nowrap px-3 py-4">{user.status}</td>
                    <td className="whitespace-nowrap px-3 py-4 text-right">
                      <button
                        onClick={() => handleEditUser(user)}
                        className="text-indigo-600 hover:text-indigo-900 mr-4"
                      >
                        <Edit className="h-4 w-4" />
                      </button>

                      <button
                        onClick={() => confirmDelete(user.id)}
                        disabled={user.role === 'admin' || deletingUserId === user.id}
                        className={`${user.role === 'admin' ? 'text-gray-400 cursor-not-allowed' : 'text-red-600 hover:text-red-900'} ${deletingUserId === user.id ? 'cursor-progress' : ''}`}
                        title={user.role === 'admin' ? "Les administrateurs ne peuvent pas être supprimés" : deletingUserId === user.id ? "Suppression en cours..." : "Supprimer l'utilisateur"}
                      >
                        {deletingUserId === user.id ? <Loader2 className="h-4 w-4 animate-spin" /> : <Trash2 className="h-4 w-4" />}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="mt-6 flex items-center justify-between">
            <div className="text-sm text-gray-500">
              Affichage de {startIndex + 1} -{' '}
              {Math.min(startIndex + itemsPerPage, filteredUsers.length)} sur{' '}
              {filteredUsers.length} utilisateurs
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className={`inline-flex items-center px-3 py-2 text-sm font-medium rounded-md ${currentPage === 1 ? 'text-gray-400' : 'text-gray-700 bg-white hover:bg-gray-50'
                  }`}
              >
                Précédent
              </button>
              <button
                onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className={`inline-flex items-center px-3 py-2 text-sm font-medium rounded-md ${currentPage === totalPages ? 'text-gray-400' : 'text-gray-700 bg-white hover:bg-gray-50'
                  }`}
              >
                Suivant
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
