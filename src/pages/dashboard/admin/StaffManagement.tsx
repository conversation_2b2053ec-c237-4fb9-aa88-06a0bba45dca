import React, { useState, useEffect } from 'react';
import { User, Star, Calendar, Plus, Edit, Trash2, Loader2 } from 'lucide-react';
import axios from 'axios';
import { BASE_URL, IMG_URL } from '../../../context/url';
import { useAuth } from '../../../context/AuthContext';
import toast from 'react-hot-toast';
import StaffModal from './StaffModal';

type StaffMember = {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  gender?: string;
  role: string;
  specialties?: string;
  salonId?: number;
  photoUrl?: string;
  availability?: string;
  status?: string;
  salon?: {
    id: number;
    name: string;
  };
};

export default function StaffManagement() {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingStaff, setEditingStaff] = useState<StaffMember | null>(null);
  const [staffMembers, setStaffMembers] = useState<StaffMember[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [salons, setSalons] = useState<{ id: number; name: string }[]>([]);
  const [selectedSalon, setSelectedSalon] = useState<string>('all');
  const [deleteModal, setDeleteModal] = useState<{
    isOpen: boolean;
    staffId: number | null;
    staffName: string;
  }>({
    isOpen: false,
    staffId: null,
    staffName: '',
  });
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchStaffMembers = async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('auth_token');
      if (!token) {
        toast.error("Aucun jeton d'authentification trouvé.");
        return;
      }

      const response = await axios.get(`${BASE_URL}/admin/staff`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        params: {
          search: searchTerm,
          salon_id: selectedSalon !== 'all' ? selectedSalon : undefined,
        },
      });
      
      setStaffMembers(response.data.data || response.data);
    } catch (error) {
      console.error('Erreur lors de la récupération du staff:', error);
      toast.error("Impossible de charger la liste du staff.");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSalons = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) return;

      const response = await axios.get(`${BASE_URL}/admin/salons`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.data.data) {
        setSalons(response.data.data.map((salon: any) => ({ 
          id: salon.id, 
          name: salon.name 
        })));
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des salons:', error);
    }
  };

  const handleDeleteStaff = async () => {
    if (!deleteModal.staffId) return;
    
    try {
      setIsDeleting(true);
      const token = localStorage.getItem('auth_token');
      if (!token) {
        toast.error("Aucun jeton d'authentification trouvé.");
        return;
      }

      const response = await axios.delete(`${BASE_URL}/admin/staff/${deleteModal.staffId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      toast.success('Membre du staff supprimé avec succès');
      fetchStaffMembers();
      setDeleteModal({ isOpen: false, staffId: null, staffName: '' });
    } catch (error: any) {
      console.error('Erreur lors de la suppression:', error);
      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error("Impossible de supprimer le membre du staff.");
      }
    } finally {
      setIsDeleting(false);
    }
  };

  const openDeleteModal = (staff: StaffMember) => {
    setDeleteModal({
      isOpen: true,
      staffId: staff.id,
      staffName: `${staff.firstName} ${staff.lastName}`,
    });
  };

  const filteredStaff = staffMembers.filter(staff =>
    `${staff.firstName} ${staff.lastName}`.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleEditStaff = (staff: StaffMember) => {
    setEditingStaff(staff);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setEditingStaff(null);
  };

  const handleStaffAdded = () => {
    fetchStaffMembers();
    toast.success(editingStaff ? 'Staff modifié avec succès' : 'Staff ajouté avec succès');
    setEditingStaff(null);
  };

  const parseSpecialties = (specialties: string | string[]): string[] => {
    if (!specialties) return [];
    if (Array.isArray(specialties)) {
      return specialties;
    }
    if (typeof specialties === 'string') {
      return specialties.split(',').map(s => s.trim());
    }
    return [];
  };

  const parseAvailability = (availability: string | any[]) => {
    if (!availability) return {};
    try {
      let parsed;
      if (Array.isArray(availability)) {
        parsed = availability;
      } else if (typeof availability === 'string') {
        parsed = JSON.parse(availability);
      } else {
        return {};
      }
      
      const formatted: { [key: string]: { start: string; end: string } } = {};
      
      if (Array.isArray(parsed)) {
        parsed.forEach((item: any) => {
          if (item.day && item.open && item.close) {
            formatted[item.day] = { start: item.open, end: item.close };
          }
        });
      } else if (typeof parsed === 'object') {
        Object.entries(parsed).forEach(([day, timeRange]) => {
          if (typeof timeRange === 'string' && timeRange.includes('-')) {
            const [start, end] = timeRange.split('-');
            formatted[day] = { start, end };
          }
        });
      }
      
      return formatted;
    } catch {
      return {};
    }
  };

  const renderStars = (rating: number = 0) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`h-4 w-4 ${index < rating ? 'text-yellow-400 fill-current' : 'text-yellow-400'}`}
      />
    ));
  };

  useEffect(() => {
    fetchStaffMembers();
    fetchSalons();
  }, [searchTerm, selectedSalon]);

  if (!user) {
    return <div>Veuillez vous connecter pour accéder à cette page.</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Gestion du Staff</h2>
        <button 
          onClick={() => setIsModalOpen(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
        >
          <Plus className="h-4 w-4 mr-2" />
          Ajouter un Coiffeur
        </button>
      </div>

      {/* Modal pour ajouter/éditer un staff member */}
      <StaffModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        onStaffAdded={handleStaffAdded}
        editingStaff={editingStaff}
      />

      <div className="bg-white shadow rounded-lg">
        <div className="p-6">
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <input
                type="search"
                placeholder="Rechercher des coiffeurs..."
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="sm:w-48">
              <select
                value={selectedSalon}
                onChange={(e) => setSelectedSalon(e.target.value)}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="all">Tous les salons</option>
                {salons.map((salon) => (
                  <option key={salon.id} value={salon.id}>
                    {salon.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center h-32">
              <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
              <span className="ml-2 text-gray-600">Chargement du staff...</span>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
              {filteredStaff.map((staff) => {
                const specialties = parseSpecialties(staff.specialties || '');
                const availability = parseAvailability(staff.availability || '{}');
                
                return (
                  <div
                    key={staff.id}
                    className="bg-white overflow-hidden shadow rounded-lg border border-gray-200"
                  >
                    <div className="p-6">
                      <div className="flex items-center">
                        <img
                          className="h-16 w-16 rounded-full object-cover"
                          src={staff.photoUrl ? `${IMG_URL}${staff.photoUrl}` : '/default_profile.jpeg'}
                          alt={`${staff.firstName} ${staff.lastName}`}
                        />
                        <div className="ml-4 flex-1">
                          <h3 className="text-lg font-medium text-gray-900">
                            {staff.firstName} {staff.lastName}
                          </h3>
                          <p className="text-sm text-gray-500">{staff.email}</p>
                          {staff.salon && (
                            <p className="text-sm text-gray-500">
                              Salon: {staff.salon.name}
                            </p>
                          )}
                          <div className="mt-1 flex items-center">
                            {renderStars(0)}
                          </div>
                        </div>
                      </div>

                      <div className="mt-4">
                        <h4 className="text-sm font-medium text-gray-900">Spécialités</h4>
                        <div className="mt-2 flex flex-wrap gap-2">
                          {specialties.length > 0 ? (
                            specialties.map((specialty, index) => (
                              <span
                                key={index}
                                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800"
                              >
                                {specialty}
                              </span>
                            ))
                          ) : (
                            <span className="text-sm text-gray-500">Aucune spécialité</span>
                          )}
                        </div>
                      </div>

                      <div className="mt-4">
                        <h4 className="text-sm font-medium text-gray-900">Disponibilités</h4>
                        <div className="mt-2 space-y-2">
                          {Object.keys(availability).length > 0 ? (
                            Object.entries(availability).map(([day, slot]) => (
                              <div key={day} className="flex justify-between text-sm">
                                <span className="text-gray-500 capitalize">{day}</span>
                                <span className="text-gray-900">
                                  {slot.start} - {slot.end}
                                </span>
                              </div>
                            ))
                          ) : (
                            <span className="text-sm text-gray-500">Aucune disponibilité définie</span>
                          )}
                        </div>
                      </div>

                      <div className="mt-6 flex justify-end space-x-3">
                        <button 
                          onClick={() => handleEditStaff(staff)}
                          className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                          title="Modifier"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button 
                          onClick={() => openDeleteModal(staff)}
                          className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
                          title="Supprimer"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {!isLoading && filteredStaff.length === 0 && (
            <div className="mt-8 text-center">
              <p className="text-gray-500">
                {searchTerm ? 'Aucun coiffeur trouvé pour cette recherche.' : 'Aucun coiffeur ajouté pour le moment.'}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Modal de confirmation de suppression */}
      {deleteModal.isOpen && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-900">
                Confirmer la suppression
              </h3>
            </div>
            
            <div className="mb-6">
              <p className="text-gray-700">
                Êtes-vous sûr de vouloir supprimer <strong>{deleteModal.staffName}</strong> ?
              </p>
              <p className="text-sm text-gray-500 mt-2">
                Cette action est <strong>irréversible</strong> et supprimera définitivement ce membre du staff.
              </p>
            </div>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setDeleteModal({ isOpen: false, staffId: null, staffName: '' })}
                className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                disabled={isDeleting}
              >
                Annuler
              </button>
              <button
                onClick={handleDeleteStaff}
                disabled={isDeleting}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 disabled:opacity-50"
              >
                {isDeleting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Suppression...
                  </>
                ) : (
                  'Oui, supprimer'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}