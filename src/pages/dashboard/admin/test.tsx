import React, { useEffect, useState } from 'react';
import { Plus, X, MapPin, Phone, Star } from 'lucide-react';
import { BASE_URL, IMG_URL } from '../../../context/url';
import { useAuth } from '../../../context/AuthContext';

// Types pour les données
interface SalonAPI {
  id: number;
  name: string;
  address: string;
  hours: Record<string, { open: string; close: string }> | Array<{ day: string; open: string; close: string }>;
  contact: { phone: string; email: string };
  images: string | null;
  rating: string;
  status: string;
  cancellation_hours: number;
}

interface FormattedSalon {
  id: string;
  name: string;
  address: string;
  hours: Array<{ day: string; open: string; close: string }>;
  contact: { phone: string; email: string };
  rating: number;
  images: string;
  status: string;
  cancellation_hours: number;
}

export default function SalonsManagement() {
  const { user } = useAuth(); // Accéder à l'utilisateur via le contexte
  const [salons, setSalons] = useState<FormattedSalon[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [formValues, setFormValues] = useState({
    name: '',
    address: '',
    hours: [] as { day: string; open: string; close: string }[],
    phone: '',
    email: '',
    images: '' as string | File,
  });
  const [hoursFields, setHoursFields] = useState([{ day: '', open: '', close: '' }]);
  

  const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  // Charger les salons depuis l'API
  useEffect(() => {
    const fetchSalons = async () => {
      try {
        const response = await fetch(`${BASE_URL}/salons-list`);
        const result = await response.json();

        if (result.success) {
          const formattedSalons: FormattedSalon[] = result.data.map((salon: SalonAPI) => ({
            id: salon.id.toString(),
            name: salon.name,
            address: salon.address,
            hours: Array.isArray(salon.hours)
              ? salon.hours
              : Object.entries(salon.hours).map(([day, hours]) => ({
                day,
                open: (hours as { open: string; close: string }).open,
                close: (hours as { open: string; close: string }).close,
              })),
            contact: { phone: salon.contact.phone, email: salon.contact.email },
            rating: parseFloat(salon.rating),
            images: salon.images || '',
            status: salon.status,
            cancellation_hours: salon.cancellation_hours,
          }));
          setSalons(formattedSalons);
        }
      } catch (error) {
        console.error('Erreur lors de la récupération des salons :', error);
      }
    };

    fetchSalons();
  }, []); // Le hook est maintenant inconditionnel

  // Gestion des champs de formulaire
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormValues({ ...formValues, [name]: value });
  };

  const handleHoursChange = (index: number, field: keyof typeof hoursFields[0], value: string) => {
    const updatedHours = [...hoursFields];
    updatedHours[index][field] = value;
    setHoursFields(updatedHours);
  };

  const addHoursField = () => {
    setHoursFields([...hoursFields, { day: '', open: '', close: '' }]);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files ? e.target.files[0] : '';
    setFormValues({ ...formValues, images: file });
  };

  const submitSalon = async () => {
    const token = localStorage.getItem('auth_token');

    if (!token) {
      console.error('Utilisateur non authentifié');
      return;
    }

    const newSalon = {
      name: formValues.name,
      address: formValues.address,
      hours: hoursFields,
      contact: { phone: formValues.phone, email: formValues.email },
    };

    try {
      const response = await fetch(`${BASE_URL}/salons/add-salon`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(newSalon),
      });

      if (!response.ok) {
        throw new Error(`Erreur lors de l’ajout du salon: ${response.statusText}`);
      }

      const createdSalon = await response.json();
      setSalons((prev) => [...prev, createdSalon.data]);
      setIsModalOpen(false);
      resetForm();
    } catch (error) {
      console.error('Erreur lors de la création du salon:', error);
    }
  };

  const resetForm = () => {
    setFormValues({
      name: '',
      address: '',
      hours: [],
      phone: '',
      email: '',
      images: '',
    });
    setHoursFields([{ day: '', open: '', close: '' }]);
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    submitSalon();
  };

  const filteredSalons = salons.filter(
    (salon) =>
      salon.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      salon.address.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Si l'utilisateur n'est pas connecté
  if (!user) {
    return <div>Veuillez vous connecter pour accéder à cette page.</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Gestion des Salons</h2>
        <button
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          onClick={() => setIsModalOpen(true)}
        >
          <Plus className="h-4 w-4 mr-2" />
          Ajouter un Salon
        </button>
      </div>


      {isModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50">
          <div className="bg-white rounded-lg shadow-lg w-[50rem] p-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900">Ajouter un Salon</h3>
              <button
                className="text-gray-400 hover:text-gray-600"
                onClick={() => setIsModalOpen(false)}
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            <form onSubmit={handleSubmit} className="mt-4 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Nom</label>
                <input
                  type="text"
                  name="name"
                  value={formValues.name}
                  onChange={handleInputChange}
                  className="block w-full mt-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Adresse</label>
                <input
                  type="text"
                  name="address"
                  value={formValues.address}
                  onChange={handleInputChange}
                  className="block w-full mt-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Téléphone</label>
                <input
                  type="text"
                  name="phone"
                  value={formValues.phone}
                  onChange={handleInputChange}
                  className="block w-full mt-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Email</label>
                <input
                  type="email"
                  name="email"
                  value={formValues.email}
                  onChange={handleInputChange}
                  className="block w-full mt-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Heures</label>
                {hoursFields.map((field, index) => (
                  <div key={index} className="flex space-x-4 mb-2">
                    <select
                      value={field.day}
                      onChange={(e) => handleHoursChange(index, 'day', e.target.value)}
                      className="block w-1/3 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      required
                    >
                      <option value="">Sélectionnez un jour</option>
                      {daysOfWeek.map((day) => (
                        <option key={day} value={day}>
                          {day}
                        </option>
                      ))}
                    </select>
                    <input
                      type="time"
                      placeholder="Ouverture"
                      value={field.open}
                      onChange={(e) => handleHoursChange(index, 'open', e.target.value)}
                      className="block w-1/3 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      required
                    />
                    <input
                      type="time"
                      placeholder="Fermeture"
                      value={field.close}
                      onChange={(e) => handleHoursChange(index, 'close', e.target.value)}
                      className="block w-1/3 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      required
                    />
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addHoursField}
                  className="mt-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                >
                  Ajouter une heure
                </button>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Image</label>
                <input
                  type="file"
                  name="images"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="block w-full mt-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>

              <button
                type="submit"
                className="w-full px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700"
              >
                Ajouter
              </button>
            </form>

          </div>
        </div>
      )}


      {/* Liste des salons */}
      <div className="bg-white shadow rounded-lg">
        <div className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <input
                type="search"
                placeholder="Rechercher des salons..."
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          <div className="mt-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {filteredSalons.map((salon) => (
              <div
                key={salon.id}
                className="bg-white overflow-hidden shadow rounded-lg border border-gray-200"
              >
                <div className="relative h-48">
                  <img
                    className="w-full h-full object-cover"
                    src={`${IMG_URL}/storage/${salon.images}`}
                    alt={salon.name}
                  />
                  <div className="absolute top-4 right-4 flex space-x-2">
                    <button className="p-2 bg-white rounded-full shadow-lg text-indigo-600 hover:text-indigo-900">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-square-pen h-4 w-4"><path d="M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z"></path></svg>
                    </button>
                    <button className="p-2 bg-white rounded-full shadow-lg text-red-600 hover:text-red-900">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-trash2 h-4 w-4"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path><line x1="10" x2="10" y1="11" y2="17"></line><line x1="14" x2="14" y1="11" y2="17"></line></svg>
                    </button>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-lg font-medium text-gray-900">{salon.name}</h3>
                  <div className="mt-2 flex items-center text-sm text-gray-500">
                    <MapPin className="h-4 w-4 mr-1" />
                    {salon.address}
                  </div>
                  <div className="mt-2 flex items-center text-sm text-gray-500">
                    <Phone className="h-4 w-4 mr-1" />
                    {salon.contact.phone}
                  </div>
                  <div className="mt-2 flex items-center">
                    <Star className="h-4 w-4 text-yellow-400" />
                    <span className="ml-1 text-sm text-gray-600">{salon.rating}</span>
                  </div>
                  <div className="mt-4 flex justify-between items-center">
                    <span className="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium bg-green-100 text-green-800">
                      {salon.status === 'active' ? 'Actif' : salon.status}
                    </span>
                    <button className="text-sm font-medium text-indigo-600 hover:text-indigo-900">
                      Voir les détails
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
