import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import toast from 'react-hot-toast';
import { BASE_URL, IMG_URL } from '../../../context/url';
import { useAuth } from '../../../context/AuthContext';
import type { User } from '../../../pages/dashboard/admin/UsersManagement';
import ReactCrop, { Crop, PixelCrop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';

type Availability = {
  day: keyof typeof daysOfWeekMap;
  open: string;
  close: string;
};

type Salon = {
  id: number;
  name: string;
};

type UserModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onUserAdded: () => void;
  editingUser?: LocalUser | null;
};

type LocalUser = {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  gender: 'male' | 'female' | 'other';
  role: 'admin' | 'owner' | 'manager' | 'hairdresser' | 'client';
  specialties?: string;
  salonId?: number;
  photoUrl?: string;
  availability?: string;
  ownerId?: number;
};

const daysOfWeekMap = {
  Lundi: 'Monday',
  Mardi: 'Tuesday',
  Mercredi: 'Wednesday',
  Jeudi: 'Thursday',
  Vendredi: 'Friday',
  Samedi: 'Saturday',
  Dimanche: 'Sunday',
} as const;

const UserModal: React.FC<UserModalProps> = ({ isOpen, onClose, onUserAdded, editingUser }) => {
  const { user: currentUser } = useAuth();
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    phone: '',
    gender: 'male',
    role: 'manager', // Changé de 'client' à 'manager' par défaut
    specialties: '',
    salonId: '',
    ownerId: '',
    profileImage: null as File | string | null,
  });

  const [salons, setSalons] = useState<Salon[]>([]);
  const [isLoadingSalons, setIsLoadingSalons] = useState(false);
  const [availability, setAvailability] = useState<Availability[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [owners, setOwners] = useState<User[]>([]);
  const [isLoadingOwners, setIsLoadingOwners] = useState(false);
  const [specialties, setSpecialties] = useState<string[]>([]);
  const [newSpecialty, setNewSpecialty] = useState('');

  // États pour le cropper
  const [crop, setCrop] = useState<Crop>();
  const [showCropper, setShowCropper] = useState(false);
  const [imageToCrop, setImageToCrop] = useState<string | null>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const [completedCrop, setCompletedCrop] = useState<PixelCrop | null>(null);
  const [zoom, setZoom] = useState(1);

  const daysOfWeek = Object.keys(daysOfWeekMap) as Array<keyof typeof daysOfWeekMap>;
  const minutes = ['00', '15', '30', '45'];
  const hours = Array.from({ length: 24 }, (_, i) =>
    minutes.map((minute) => `${i.toString().padStart(2, '0')}:${minute}`)
  ).flat();

  useEffect(() => {
    if (isOpen) {
      fetchSalons();
      fetchOwners();

      // Réinitialiser les états du cropper lors de l'ouverture du modal
      setImageToCrop(null);
      setCompletedCrop(null);
      setZoom(1);
      setCrop(undefined);

      if (editingUser) {
        setFormData({
          firstName: editingUser.firstName || '',
          lastName: editingUser.lastName || '',
          email: editingUser.email || '',
          password: '',
          phone: editingUser.phone || '',
          gender: editingUser.gender || 'male',
          role: editingUser.role || 'client',
          specialties: editingUser.specialties || '',
          salonId: editingUser.salonId?.toString() || '',
          ownerId: editingUser.ownerId?.toString() || '',
          profileImage: editingUser.photoUrl || null,
        });

        if (editingUser.role === 'hairdresser' && editingUser.availability) {
          const parsedAvailability = JSON.parse(editingUser.availability);
          const formattedAvailability = Object.entries(parsedAvailability).map(([day, times]) => {
            const [open, close] = (times as string).split('-');

            // Convertir le jour de l'anglais vers le français
            const dayInFrench = Object.keys(daysOfWeekMap).find(
              key => daysOfWeekMap[key as keyof typeof daysOfWeekMap] === day
            );

            return {
              day: dayInFrench as keyof typeof daysOfWeekMap,
              open,
              close,
            };
          });
          setAvailability(formattedAvailability);
        } else {
          setAvailability([]);
        }

        // Initialiser les spécialités si l'utilisateur en a
        if (editingUser.role === 'hairdresser' && editingUser.specialties) {
          try {
            const parsedSpecialties = JSON.parse(editingUser.specialties);
            if (Array.isArray(parsedSpecialties)) {
              setSpecialties(parsedSpecialties);
            } else {
              setSpecialties([editingUser.specialties]);
            }
          } catch {
            setSpecialties([editingUser.specialties]);
          }
        } else {
          setSpecialties([]);
        }

        // Si on est en mode édition et qu'il y a une photo existante, la charger pour le cropper
        if (editingUser.photoUrl) {
          // Note: Charger l'image existante dans le cropper peut nécessiter une gestion spécifique
          // pour la convertir en blob si elle est stockée en tant qu'URL sur le serveur.
          // Pour l'instant, nous gérons seulement le cas d'upload d'une nouvelle image.
        }
      } else {
        // Réinitialiser le formData si on n'est pas en mode édition
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          password: '',
          phone: '',
          gender: 'male',
          role: 'manager', // Changé de 'client' à 'manager' par défaut
          specialties: '',
          salonId: '',
          ownerId: '',
          profileImage: null,
        });
        setAvailability([]);
      }
    } else {
      // Réinitialiser les états quand le modal se ferme
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        password: '',
        phone: '',
        gender: 'male',
        role: 'manager', // Changé de 'client' à 'manager' par défaut
        specialties: '',
        salonId: '',
        ownerId: '',
        profileImage: null,
      });
      setSalons([]);
      setAvailability([]);
      setIsSubmitting(false);
      // Les états du cropper sont déjà réinitialisés lors de l'ouverture
    }
  }, [isOpen, editingUser]);

  // Initialiser le crop lorsque l'image à rogner est définie
  useEffect(() => {
    if (imageToCrop) {
      const initialCrop: Crop = {
        unit: '%',
        width: 90,
        height: 90,
        x: 5,
        y: 5,
      };
      setCrop(initialCrop);
      setShowCropper(true);
    }
  }, [imageToCrop]);

  const fetchSalons = async () => {
    try {
      setIsLoadingSalons(true);
      const token = localStorage.getItem('auth_token');
      const response = await axios.get(`${BASE_URL}/salons-list`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      setSalons(response.data.data);
    } catch (error) {
      console.error("Erreur lors de la récupération des salons :", error);
      toast.error("Impossible de charger les salons.");
    } finally {
      setIsLoadingSalons(false);
    }
  };

  const fetchOwners = async () => {
    try {
      setIsLoadingOwners(true);
      const token = localStorage.getItem('auth_token');
      const response = await axios.get(`${BASE_URL}/admin/users/owners`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      setOwners(response.data);
    } catch (error) {
      console.error("Erreur lors de la récupération des propriétaires :", error);
      toast.error("Impossible de charger les propriétaires.");
    } finally {
      setIsLoadingOwners(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    if (name === 'role') {
      if (value !== 'hairdresser') {
        // Réinitialiser les champs spécifiques au rôle "hairdresser"
        setFormData({ ...formData, [name]: value, specialties: '', salonId: '' });
        setAvailability([]);
        setSpecialties([]);
      } else {
        setFormData({ ...formData, [name]: value });
      }
      
      // Réinitialiser ownerId si le rôle n'est pas 'manager'
      if (value !== 'manager') {
        setFormData(prev => ({ ...prev, ownerId: '' }));
      }
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  const handleAvailabilityChange = (index: number, field: keyof Availability, value: string) => {
    const updatedAvailability = [...availability];

    if (field === 'day') {
      if (Object.keys(daysOfWeekMap).includes(value)) {
        updatedAvailability[index][field] = value as keyof typeof daysOfWeekMap;
      } else {
        console.error(`Valeur invalide pour le jour : ${value}`);
        return;
      }
    } else {
      updatedAvailability[index][field] = value;
    }

    setAvailability(updatedAvailability);
  };

  const addAvailabilityField = () => {
    setAvailability([...availability, { day: 'Lundi', open: '', close: '' }]);
  };

  const removeAvailabilityField = (index: number) => {
    setAvailability(availability.filter((_, i) => i !== index));
  };

  const addSpecialty = () => {
    if (newSpecialty.trim() && !specialties.includes(newSpecialty.trim())) {
      setSpecialties([...specialties, newSpecialty.trim()]);
      setNewSpecialty('');
    }
  };

  const removeSpecialty = (specialtyToRemove: string) => {
    setSpecialties(specialties.filter(specialty => specialty !== specialtyToRemove));
  };

  const handleSpecialtyKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addSpecialty();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (!file.type.startsWith('image/')) {
        toast.error('Le fichier doit être une image.');
        return;
      }

      const validTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif'];
      if (!validTypes.includes(file.type)) {
        toast.error('Le fichier doit être de type JPEG, PNG, JPG ou GIF.');
        return;
      }

      // Créer une URL pour l'image
      const imageUrl = URL.createObjectURL(file);
      setImageToCrop(imageUrl);
    }
  };

  const onCropComplete = (crop: PixelCrop) => {
    setCompletedCrop(crop);
  };

  const handleCropValidation = async () => {
    if (!imageRef.current || !completedCrop || !completedCrop.width || !completedCrop.height) {
      return;
    }

    const canvas = document.createElement('canvas');
    const scaleX = imageRef.current.naturalWidth / imageRef.current.width;
    const scaleY = imageRef.current.naturalHeight / imageRef.current.height;
    canvas.width = completedCrop.width;
    canvas.height = completedCrop.height;
    const ctx = canvas.getContext('2d');

    if (ctx) {
      ctx.drawImage(
        imageRef.current,
        completedCrop.x * scaleX,
        completedCrop.y * scaleY,
        completedCrop.width * scaleX,
        completedCrop.height * scaleY,
        0,
        0,
        completedCrop.width,
        completedCrop.height
      );

      // Convertir le canvas en fichier
      canvas.toBlob((blob) => {
        if (blob) {
          const croppedFile = new File([blob], 'cropped-image.jpg', { type: 'image/jpeg' });
          setFormData({ ...formData, profileImage: croppedFile });
          setShowCropper(false);
          setImageToCrop(null);
          setCompletedCrop(null);
          setCrop(undefined);
        }
      }, 'image/jpeg', 0.95);
    }
  };

  const handleCropCancel = () => {
    setShowCropper(false);
    setImageToCrop(null);
    setCompletedCrop(null);
    setCrop(undefined);
    // Optionnel: Réinitialiser le champ de fichier pour pouvoir choisir à nouveau la même image
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    const formDataObject = new FormData();
    formDataObject.append('firstName', formData.firstName);
    formDataObject.append('lastName', formData.lastName);
    formDataObject.append('email', formData.email);

    if (!editingUser) {
      formDataObject.append('password', formData.password);
    }

    formDataObject.append('phone', formData.phone);
    formDataObject.append('role', formData.role);
    formDataObject.append('gender', formData.gender);

    // Debug: Afficher les données envoyées
    console.log('Données envoyées au backend:', {
      firstName: formData.firstName,
      lastName: formData.lastName,
      email: formData.email,
      role: formData.role,
      gender: formData.gender,
      phone: formData.phone
    });

    // Ajouter specialties uniquement si le rôle est 'hairdresser'
    if (formData.role === 'hairdresser' && specialties.length > 0) {
      specialties.forEach((specialty, index) => {
        formDataObject.append(`specialties[${index}]`, specialty);
      });
    }

    // Ajouter salonId si disponible
    if (formData.salonId) {
      formDataObject.append('salonId', formData.salonId);
    }

    // Ajouter ownerId si le rôle est 'manager'
    if (formData.role === 'manager' && formData.ownerId) {
      formDataObject.append('ownerId', formData.ownerId);
    }

    // Ajouter les disponibilités uniquement si le rôle est 'hairdresser'
    if (formData.role === 'hairdresser' && availability.length > 0) {
      availability.forEach((slot, index) => {
        const dayInEnglish = daysOfWeekMap[slot.day];
        if (!dayInEnglish) {
          console.error(`Jour invalide : ${slot.day}`);
          return;
        }
    
        formDataObject.append(`availability[${index}][day]`, dayInEnglish);
        formDataObject.append(`availability[${index}][open]`, slot.open);
        formDataObject.append(`availability[${index}][close]`, slot.close);
      });
    }

    // Ajouter l'image de profil si présente
    if (formData.profileImage) {
      formDataObject.append('profileImage', formData.profileImage);
    }

    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        toast.error("Aucun jeton d'authentification trouvé.");
        return;
      }

      let response;
      if (editingUser) {
        response = await axios.post(
          `${BASE_URL}/admin/users/update-users/${editingUser.id}`,
          formDataObject,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'multipart/form-data',
            },
          }
        );
      } else {
        response = await axios.post(`${BASE_URL}/admin/users/add-users`, formDataObject, {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'multipart/form-data',
          },
        });
      }

      if (response.status === 200 || response.status === 201) {
        toast.success(
          editingUser ? 'Utilisateur mis à jour avec succès.' : 'Utilisateur ajouté avec succès.'
        );
        onUserAdded();
        onClose();
      } else {
        toast.error('Une erreur inattendue s\'est produite.');
      }
    } catch (error: any) {
      console.error("Erreur lors de l'ajout ou de la mise à jour de l'utilisateur :", error);
      if (error.response?.data?.errors?.email) {
        toast.error("Cette adresse email est déjà utilisée.");
      } else {
        toast.error(
        editingUser
          ? "Impossible de mettre à jour l'utilisateur."
            : "Impossible d'ajouter l'utilisateur."
      );
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-4xl">
        <h2 className="text-lg font-bold mb-4">
          {editingUser ? 'Modifier' : 'Ajouter'} un Utilisateur
        </h2>
        <form onSubmit={handleSubmit} className="grid grid-cols-2 gap-x-4 gap-y-6">
          {/* Colonne 1 */}
          <div>
            <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">
              Prénom
            </label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              value={formData.firstName}
              onChange={handleChange}
              required
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>
          <div>
            <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">
              Nom
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              value={formData.lastName}
              onChange={handleChange}
              required
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              Email
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              required
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700">
              Mot de passe
            </label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              required
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>

          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
              Téléphone
            </label>
            <input
              type="text"
              id="phone"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              required
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>
          <div>
            <label htmlFor="gender" className="block text-sm font-medium text-gray-700">
              Genre
            </label>
            <select
              id="gender"
              name="gender"
              value={formData.gender}
              onChange={handleChange}
              required
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            >
              <option value="male">Homme</option>
              <option value="female">Femme</option>
              <option value="other">Autre</option>
            </select>
          </div>

          {/* Photo de profil avec cropper */}
          <div className="col-span-2">
            <label htmlFor="profileImage" className="block text-sm font-medium text-gray-700">
              Photo de profil
            </label>
            <input
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              className="block w-full mt-2"
            />
            {showCropper && imageToCrop && (
              <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
                <div className="bg-white p-4 rounded-lg max-w-2xl w-full">
                  <h3 className="text-lg font-semibold mb-4">Rogner l'image</h3>
                  <ReactCrop
                    crop={crop}
                    onChange={(c) => setCrop(c)}
                    onComplete={onCropComplete}
                    aspect={1}
                    circularCrop
                  >
                    <img
                      ref={imageRef}
                      src={imageToCrop}
                      alt="Image à rogner"
                      className="max-w-full max-h-[80vh] object-contain"
                      style={{ transform: `scale(${zoom})`, transformOrigin: 'center center' }}
                    />
                  </ReactCrop>
                  <div className="mt-4">
                    <label htmlFor="zoom-range" className="block text-sm font-medium text-gray-700">Zoom: {zoom.toFixed(2)}x</label>
                    <input
                      type="range"
                      id="zoom-range"
                      min="1"
                      max="3"
                      step="0.01"
                      value={zoom}
                      onChange={(e) => setZoom(Number(e.target.value))}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                    />
                  </div>
                  <div className="mt-4 flex justify-end space-x-2">
                    <button
                      type="button"
                      onClick={handleCropValidation}
                      disabled={!completedCrop}
                      className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50"
                    >
                      Valider
                    </button>
                    <button
                      type="button"
                      onClick={handleCropCancel}
                      className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md"
                    >
                      Annuler
                    </button>
                  </div>
                </div>
              </div>
            )}
            {formData.profileImage && !showCropper && (
              <div className="mt-4">
                <img
                  src={
                    typeof formData.profileImage === 'string'
                      ? `${IMG_URL}${formData.profileImage}`
                      : URL.createObjectURL(formData.profileImage)
                  }
                  alt="Preview"
                  className="h-24 w-24 rounded-full object-cover"
                />
              </div>
            )}
          </div>

          <div>
            <label htmlFor="role" className="block text-sm font-medium text-gray-700">
              Rôle
            </label>
            <select
              id="role"
              name="role"
              value={formData.role}
              onChange={handleChange}
              required
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            >
              {currentUser?.role === 'admin' && (
                <>
                  <option value="admin">Administrateur</option>
                  <option value="owner">Propriétaire</option>
                </>
              )}
              <option value="manager">Gérant</option>
              <option value="hairdresser">Coiffeur</option>
              {currentUser?.role === 'admin' && <option value="client">Client</option>}
            </select>
          </div>

          {/* Sélection de propriétaire pour les gérants */}
          {formData.role === 'manager' && (
            <div className="col-span-2">
              <label htmlFor="ownerId" className="block text-sm font-medium text-gray-700">
                Propriétaire associé *
              </label>
              <select
                id="ownerId"
                name="ownerId"
                value={formData.ownerId}
                onChange={handleChange}
                required
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="">Sélectionnez un propriétaire</option>
                {isLoadingOwners ? (
                  <option value="" disabled>Chargement des propriétaires...</option>
                ) : owners.length === 0 ? (
                  <option value="" disabled>Aucun propriétaire disponible</option>
                ) : (
                  owners.map((owner) => (
                    <option key={owner.id} value={owner.id}>
                      {owner.firstName} {owner.lastName} ({owner.email})
                    </option>
                  ))
                )}
              </select>
              {owners.length === 0 && !isLoadingOwners && (
                <p className="text-sm text-gray-500 mt-1">
                  Aucun propriétaire disponible. Créez d'abord un propriétaire.
                </p>
              )}
            </div>
          )}

          {formData.role === 'hairdresser' && (
            <>
              <div>
                <label htmlFor="salonId" className="block text-sm font-medium text-gray-700">
                  Salon
                </label>
                <select
                  id="salonId"
                  name="salonId"
                  value={formData.salonId}
                  onChange={handleChange}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                >
                  <option value="">Sélectionnez un salon</option>
                  {isLoadingSalons ? (
                    <option value="" disabled>Chargement...</option>
                  ) : (
                    salons.map((salon) => (
                      <option key={salon.id} value={salon.id}>
                        {salon.name}
                      </option>
                    ))
                  )}
                </select>
              </div>
              <div>
                <label htmlFor="specialties" className="block text-sm font-medium text-gray-700">
                  Spécialités
                </label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    id="specialties"
                    name="specialties"
                    placeholder="Ajouter une spécialité..."
                    value={newSpecialty}
                    onChange={(e) => setNewSpecialty(e.target.value)}
                    onKeyPress={handleSpecialtyKeyPress}
                    className="block flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  />
                  <button
                    type="button"
                    onClick={addSpecialty}
                    className="px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 flex items-center"
                  >
                    <span className="text-lg">+</span>
                  </button>
                </div>
                <div className="mt-2 flex flex-wrap gap-2">
                  {specialties.map((specialty, index) => (
                    <span 
                      key={index} 
                      className="inline-flex items-center bg-indigo-100 text-indigo-800 text-sm font-medium px-2.5 py-0.5 rounded-full"
                    >
                      {specialty}
                      <button
                        type="button"
                        onClick={() => removeSpecialty(specialty)}
                        className="ml-1 text-indigo-800 hover:text-indigo-900 font-bold"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Disponibilités</label>
                {availability.map((field, index) => (
                  <div key={index} className="flex space-x-4 mb-2">
                    <select
                      value={field.day}
                      onChange={(e) => handleAvailabilityChange(index, 'day', e.target.value)}
                      className="block w-1/3 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      required
                    >
                      <option value="">Sélectionnez un jour</option>
                      {daysOfWeek.map((day) => (
                        <option key={day} value={day}>
                          {day}
                        </option>
                      ))}
                    </select>
                    <select
                      value={field.open}
                      onChange={(e) => handleAvailabilityChange(index, 'open', e.target.value)}
                      className="block w-1/3 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      required
                    >
                      {hours.map((hour) => (
                        <option key={hour} value={hour}>
                          {hour}
                        </option>
                      ))}
                    </select>
                    <select
                      value={field.close}
                      onChange={(e) => handleAvailabilityChange(index, 'close', e.target.value)}
                      className="block w-1/3 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      required
                    >
                      {hours.map((hour) => (
                        <option key={hour} value={hour}>
                          {hour}
                        </option>
                      ))}
                    </select>
                    <button
                      type="button"
                      onClick={() => removeAvailabilityField(index)}
                      className="text-red-500"
                    >
                      Supprimer
                    </button>
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addAvailabilityField}
                  className="mt-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                >
                  Ajouter une plage horaire
                </button>
              </div>
            </>
          )}


          {/* Actions */}
          <div className="col-span-2 flex justify-end space-x-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md"
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
            >
              {isSubmitting
                ? editingUser
                  ? 'Mise à jour...'
                  : 'Ajout...'
                : editingUser
                  ? 'Mettre à jour'
                  : 'Ajouter'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UserModal;