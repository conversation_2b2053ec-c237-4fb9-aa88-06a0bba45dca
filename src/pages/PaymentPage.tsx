import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import PaymentInterface from '../components/payment/PaymentInterface';
import MessagingInterface from '../components/messaging/MessagingInterface';
import { BASE_URL, API_ENDPOINTS } from '../context/url';
import { useAuth } from '../context/AuthContext';

interface Booking {
  id: number;
  dateTime: string;
  total_amount: number;
  status: string;
  salon: {
    id: number;
    name: string;
    address: string;
  };
  services: Array<{
    id: number;
    name: string;
    price: number;
    duration: number;
  }>;
  hairdresser: {
    id: number;
    user: {
      firstName: string;
      lastName: string;
    };
  };
}

const PaymentPage: React.FC = () => {
  const { bookingId } = useParams<{ bookingId: string }>();
  const navigate = useNavigate();
  const { token } = useAuth();
  
  const [booking, setBooking] = useState<Booking | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [showMessaging, setShowMessaging] = useState(false);

  useEffect(() => {
    if (bookingId) {
      fetchBooking();
    }
  }, [bookingId]);

  const fetchBooking = async () => {
    try {
      const response = await fetch(`${BASE_URL}/bookings/${bookingId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setBooking(data.booking);
      } else {
        setError('Réservation non trouvée');
      }
    } catch (error) {
      setError('Erreur lors du chargement de la réservation');
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentSuccess = (paymentData: any) => {
    setPaymentSuccess(true);
    
    // Rediriger vers la page de confirmation après 3 secondes
    setTimeout(() => {
      navigate(`/bookings/${bookingId}/confirmation`);
    }, 3000);
  };

  const handlePaymentError = (error: string) => {
    setError(error);
  };

  const handlePaymentCancel = () => {
    navigate(`/bookings/${bookingId}`);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <svg className="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <h3 className="mt-2 text-sm font-medium text-gray-900">Erreur</h3>
          <p className="mt-1 text-sm text-gray-500">{error}</p>
          <div className="mt-6">
            <button
              onClick={() => navigate('/bookings')}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
            >
              Retour aux réservations
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!booking) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Réservation non trouvée</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate(-1)}
                className="text-gray-600 hover:text-gray-900"
              >
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <h1 className="text-xl font-semibold text-gray-900">
                Paiement - Réservation #{booking.id}
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowMessaging(!showMessaging)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                {showMessaging ? 'Masquer' : 'Messages'}
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Détails de la réservation */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold mb-4">Détails de la réservation</h2>
              
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-600">Salon</p>
                  <p className="font-medium">{booking.salon.name}</p>
                  <p className="text-sm text-gray-500">{booking.salon.address}</p>
                </div>
                
                <div>
                  <p className="text-sm text-gray-600">Coiffeur</p>
                  <p className="font-medium">
                    {booking.hairdresser.user.firstName} {booking.hairdresser.user.lastName}
                  </p>
                </div>
                
                <div>
                  <p className="text-sm text-gray-600">Date et heure</p>
                  <p className="font-medium">{formatDate(booking.dateTime)}</p>
                </div>
                
                <div>
                  <p className="text-sm text-gray-600">Services</p>
                  <div className="space-y-2">
                    {booking.services.map((service) => (
                      <div key={service.id} className="flex justify-between">
                        <span>{service.name}</span>
                        <span>{service.price.toFixed(2)} €</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="border-t pt-4">
                  <div className="flex justify-between font-semibold text-lg">
                    <span>Total</span>
                    <span>{booking.total_amount.toFixed(2)} €</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Interface de paiement ou messagerie */}
          <div className="lg:col-span-2">
            {showMessaging ? (
              <div className="bg-white rounded-lg shadow overflow-hidden" style={{ height: '600px' }}>
                <MessagingInterface />
              </div>
            ) : (
              <PaymentInterface
                bookingId={booking.id}
                amount={booking.total_amount}
                onSuccess={handlePaymentSuccess}
                onError={handlePaymentError}
                onCancel={handlePaymentCancel}
              />
            )}
          </div>
        </div>
      </div>

      {/* Modal de succès */}
      {paymentSuccess && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-lg leading-6 font-medium text-gray-900 mt-2">
                Paiement réussi !
              </h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  Votre réservation a été confirmée. Vous allez être redirigé vers la page de confirmation.
                </p>
              </div>
              <div className="items-center px-4 py-3">
                <button
                  onClick={() => navigate(`/bookings/${bookingId}/confirmation`)}
                  className="px-4 py-2 bg-green-500 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-green-600"
                >
                  Voir la confirmation
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentPage;
