{"name": "best-salon", "version": "0.1.0", "private": true, "dependencies": {"@stripe/react-stripe-js": "^3.9.2", "@stripe/stripe-js": "^7.9.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.86", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "axios": "^1.7.7", "date-fns": "^3.3.1", "framer-motion": "^11.0.8", "lucide-react": "^0.344.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-easy-crop": "^5.2.0", "react-hot-toast": "^2.5.2", "react-image-crop": "^11.0.10", "react-router-dom": "^6.22.3", "react-scripts": "5.0.1", "react-toastify": "^11.0.2", "recharts": "^2.12.2", "socket.io-client": "^4.8.1", "sweetalert2": "^11.15.10", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "zod": "^3.22.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-toastify": "^4.0.2", "autoprefixer": "^10.4.18", "postcss": "^8.4.35", "tailwindcss": "^3.4.1"}}