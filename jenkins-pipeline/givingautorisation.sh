#!/bin/bash

source "/var/www/html/best-salon-react/jenkins-pipeline/accessrepertoryfront.sh"

pwd
sudo chmod 755 /var/www
sudo chmod 755 /var/www/html
sudo chmod 755 /var/www/html/best-salon-react
sudo chmod 755 /var/www/html/best-salon-react/src
# sudo chown -R www-data:root /var/www/html/best-salon-react/
# sudo chmod -R 770 /var/www/html/best-salon-react/


# Restart server nextjs,nginx PM2
sudo systemctl reload nginx
sudo systemctl restart nginx
# PM2

npm run build

pm2 restart best-salon-react

# soipakon est le nom de l'application
# pm2 start npm --name soipakon -- run start -- -p 3000

# Restart server laravel apache2
sudo apache2ctl configtest
sudo systemctl reload apache2
sudo systemctl restart apache2

# Build npm for laravel backend
source "/var/www/html/best-salon-react/jenkins-pipeline/accessrepertoryback.sh"
pwd
# npm run build

