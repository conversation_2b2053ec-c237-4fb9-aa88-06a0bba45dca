# 💬💳 Système de Messagerie et Paiements - Best Salon

Ce document décrit les nouvelles fonctionnalités de messagerie et de paiements intégrées dans l'application Best Salon.

## 🚀 Fonctionnalités Ajoutées

### 💬 Système de Messagerie
- **Conversations privées** entre clients et coiffeurs
- **Conversations de groupe** pour les équipes
- **Conversations liées aux réservations** pour un suivi personnalisé
- **Envoi de fichiers et images**
- **Messages en temps réel** (prêt pour WebSocket)
- **Historique des messages** avec pagination
- **Statuts de lecture** des messages

### 💳 Système de Paiements
- **Paiements Stripe** (cartes bancaires)
- **Paiements PayPal** (compte PayPal et cartes via PayPal)
- **Sauvegarde des méthodes de paiement**
- **Gestion des remboursements**
- **Webhooks sécurisés** pour la synchronisation
- **Interface utilisateur moderne** et responsive

## 📋 Prérequis

### Backend (Laravel API)
- PHP 8.1+
- Composer
- MySQL 8.0+
- Extensions PHP : `curl`, `json`, `mbstring`, `openssl`, `pdo_mysql`

### Frontend (React)
- Node.js 16+
- npm ou yarn

### Services Externes
- **Compte Stripe** (clés API test/production)
- **Compte PayPal Developer** (client ID/secret)

## 🛠️ Installation

### 1. Configuration Backend

```bash
cd src/api

# Installer les dépendances
composer install

# Copier le fichier d'environnement
cp .env.example .env

# Générer la clé d'application
php artisan key:generate

# Configurer la base de données dans .env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=best_salon
DB_USERNAME=root
DB_PASSWORD=your_password

# Configurer Stripe dans .env
STRIPE_KEY=pk_test_your_publishable_key
STRIPE_SECRET=sk_test_your_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Configurer PayPal dans .env
PAYPAL_MODE=sandbox
PAYPAL_CLIENT_ID=your_client_id
PAYPAL_CLIENT_SECRET=your_client_secret

# Exécuter les migrations
php artisan migrate

# Démarrer le serveur
php artisan serve
```

### 2. Configuration Frontend

```bash
# Installer les dépendances
npm install

# Copier le fichier d'environnement
cp .env.example .env

# Configurer les variables dans .env
REACT_APP_API_URL=http://localhost:8000/api
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key
REACT_APP_PAYPAL_CLIENT_ID=your_paypal_client_id

# Démarrer l'application
npm start
```

## 🔧 Configuration des Webhooks

### Stripe Webhooks
1. Aller sur le dashboard Stripe
2. Créer un endpoint webhook : `https://votre-domaine.com/api/webhooks/stripe`
3. Sélectionner les événements :
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `payment_intent.canceled`
   - `charge.dispute.created`
4. Copier le secret webhook dans `STRIPE_WEBHOOK_SECRET`

### PayPal Webhooks
1. Aller sur PayPal Developer Dashboard
2. Créer un webhook : `https://votre-domaine.com/api/webhooks/paypal`
3. Sélectionner les événements :
   - `CHECKOUT.ORDER.APPROVED`
   - `PAYMENT.CAPTURE.COMPLETED`
   - `PAYMENT.CAPTURE.DENIED`
4. Copier l'ID webhook dans `PAYPAL_WEBHOOK_ID`

## 📊 Structure de la Base de Données

### Nouvelles Tables

#### `conversations`
- Gestion des conversations entre utilisateurs
- Support pour différents types (privé, groupe, réservation)
- Métadonnées et participants

#### `messages`
- Stockage des messages avec support fichiers
- Statuts de lecture et édition
- Types de messages (texte, image, fichier, système)

#### `payments`
- Enregistrement de tous les paiements
- Support multi-providers (Stripe, PayPal)
- Gestion des statuts et métadonnées

#### `payment_methods`
- Sauvegarde des méthodes de paiement
- Informations sécurisées (derniers chiffres uniquement)
- Gestion des méthodes par défaut

## 🎯 Utilisation

### Messagerie

```typescript
import MessagingInterface from '../components/messaging/MessagingInterface';

// Utilisation simple
<MessagingInterface />
```

### Paiements

```typescript
import PaymentInterface from '../components/payment/PaymentInterface';

// Paiement pour une réservation
<PaymentInterface
  bookingId={123}
  amount={50.00}
  onSuccess={(paymentData) => console.log('Paiement réussi', paymentData)}
  onError={(error) => console.error('Erreur paiement', error)}
/>
```

## 🔐 Sécurité

### Paiements
- **Chiffrement SSL/TLS** obligatoire en production
- **Validation côté serveur** de tous les montants
- **Webhooks sécurisés** avec vérification de signature
- **Aucune donnée sensible** stockée localement
- **Conformité PCI DSS** via Stripe

### Messagerie
- **Authentification JWT** requise
- **Autorisation par conversation** (participants uniquement)
- **Validation des fichiers** uploadés
- **Limitation de taille** des fichiers (10MB)

## 🚀 Déploiement

### Variables d'Environnement Production

```bash
# Backend (.env)
APP_ENV=production
APP_DEBUG=false
STRIPE_KEY=pk_live_your_live_key
STRIPE_SECRET=sk_live_your_live_secret
PAYPAL_MODE=live

# Frontend (.env)
REACT_APP_API_URL=https://api.votre-domaine.com/api
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_live_your_live_key
```

### Optimisations Production
- **Cache Redis** pour les sessions
- **Queue système** pour les emails
- **CDN** pour les fichiers statiques
- **Monitoring** des webhooks
- **Logs centralisés**

## 🧪 Tests

### Backend
```bash
cd src/api
php artisan test --filter=Payment
php artisan test --filter=Message
```

### Frontend
```bash
npm test -- --testPathPattern=payment
npm test -- --testPathPattern=messaging
```

## 📈 Monitoring

### Métriques Importantes
- **Taux de réussite des paiements**
- **Temps de réponse des webhooks**
- **Volume de messages par jour**
- **Erreurs de paiement par provider**

### Logs à Surveiller
- Échecs de paiement
- Erreurs de webhook
- Messages non délivrés
- Tentatives de fraude

## 🆘 Dépannage

### Problèmes Courants

#### Paiements Stripe
```bash
# Vérifier les logs
tail -f storage/logs/laravel.log | grep -i stripe

# Tester les webhooks
stripe listen --forward-to localhost:8000/api/webhooks/stripe
```

#### Paiements PayPal
```bash
# Vérifier la configuration
php artisan tinker
>>> config('services.paypal')
```

#### Messagerie
```bash
# Vérifier les permissions
php artisan route:list | grep conversation
```

## 📞 Support

Pour toute question ou problème :
1. Vérifier les logs d'erreur
2. Consulter la documentation des APIs (Stripe/PayPal)
3. Tester en mode sandbox/test
4. Contacter l'équipe de développement

## 🔄 Mises à Jour

### Prochaines Fonctionnalités
- [ ] Notifications push pour les messages
- [ ] Paiements récurrents
- [ ] Intégration Apple Pay / Google Pay
- [ ] Messagerie vocale
- [ ] Chatbot automatisé

---

**Version :** 1.0.0  
**Dernière mise à jour :** Septembre 2025  
**Développé par :** Équipe Best Salon
