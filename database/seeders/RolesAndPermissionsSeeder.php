<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    public function run()
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions by group
        $permissionGroups = [
            'user' => [
                'view-users' => 'View users',
                'create-users' => 'Create users',
                'edit-users' => 'Edit users',
                'delete-users' => 'Delete users',
            ],
            'salon' => [
                'view-salons' => 'View salons',
                'create-salons' => 'Create salons',
                'edit-salons' => 'Edit salons',
                'delete-salons' => 'Delete salons',
            ],
            'service' => [
                'view-services' => 'View services',
                'create-services' => 'Create services',
                'edit-services' => 'Edit services',
                'delete-services' => 'Delete services',
            ],
            'booking' => [
                'view-bookings' => 'View bookings',
                'create-bookings' => 'Create bookings',
                'edit-bookings' => 'Edit bookings',
                'cancel-bookings' => 'Cancel bookings',
            ],
            'staff' => [
                'view-staff' => 'View staff',
                'manage-staff' => 'Manage staff',
                'manage-schedules' => 'Manage schedules',
            ],
            'inventory' => [
                'view-inventory' => 'View inventory',
                'manage-inventory' => 'Manage inventory',
            ],
            'reports' => [
                'view-reports' => 'View reports',
                'export-reports' => 'Export reports',
            ],
            'settings' => [
                'manage-settings' => 'Manage settings',
            ],
        ];

        foreach ($permissionGroups as $group => $permissions) {
            foreach ($permissions as $name => $description) {
                Permission::create([
                    'name' => $name,
                    'description' => $description,
                    'group' => $group,
                    'guard_name' => 'web'
                ]);
            }
        }

        // Create roles and assign permissions
        $roles = [
            'admin' => [
                'description' => 'System Administrator',
                'permissions' => [
                    'view-users', 'create-users', 'edit-users', 'delete-users',
                    'view-salons', 'create-salons', 'edit-salons', 'delete-salons',
                    'view-services', 'create-services', 'edit-services', 'delete-services',
                    'view-bookings', 'create-bookings', 'edit-bookings', 'cancel-bookings',
                    'view-staff', 'manage-staff', 'manage-schedules',
                    'view-inventory', 'manage-inventory',
                    'view-reports', 'export-reports',
                    'manage-settings',
                ],
            ],
            'owner' => [
                'description' => 'Salon Owner',
                'permissions' => [
                    'view-salons', 'edit-salons',
                    'view-services', 'create-services', 'edit-services', 'delete-services',
                    'view-bookings', 'edit-bookings', 'cancel-bookings',
                    'view-staff', 'manage-staff', 'manage-schedules',
                    'view-inventory', 'manage-inventory',
                    'view-reports', 'export-reports',
                ],
            ],
            'manager' => [
                'description' => 'Salon Manager',
                'permissions' => [
                    'view-services', 'edit-services',
                    'view-bookings', 'edit-bookings', 'cancel-bookings',
                    'view-staff', 'manage-schedules',
                    'view-inventory', 'manage-inventory',
                    'view-reports',
                ],
            ],
            'hairdresser' => [
                'description' => 'Hairdresser',
                'permissions' => [
                    'view-bookings', 'edit-bookings',
                    'manage-schedules',
                ],
            ],
            'client' => [
                'description' => 'Client',
                'permissions' => [
                    'view-bookings', 'create-bookings', 'cancel-bookings',
                ],
            ],
        ];

        foreach ($roles as $roleName => $roleData) {
            $role = Role::create([
                'name' => $roleName,
                'description' => $roleData['description'],
                'guard_name' => 'web'
            ]);
            $role->givePermissionTo($roleData['permissions']);
        }
    }
}